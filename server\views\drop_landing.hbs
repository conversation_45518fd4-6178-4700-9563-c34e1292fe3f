<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!-- RESEARCH-BASED: Optimal viewport meta tag for mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <title>{{pageTitle}} | {{config.SITE_NAME}}</title>

    <!-- Meta tags for social sharing -->
    <meta name="description" content="{{metaDescription}}">
    <meta property="og:title" content="{{pageTitle}}">
    <meta property="og:description" content="{{metaDescription}}">
    {{#if metaImage}}
    <meta property="og:image" content="{{metaImage}}">
    {{/if}}
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{pageTitle}}">
    <meta name="twitter:description" content="{{metaDescription}}">
    {{#if metaImage}}
    <meta name="twitter:image" content="{{metaImage}}">
    {{/if}}

    <!-- Favicon -->
    <link rel="icon" href="/images/favicon.ico">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Modular Drop System -->
    <link rel="stylesheet" href="/css/drop-shared.css">

    <!-- Drop Landing Page Styles -->
    <style>
        /* Modular Drop System - CSS Variables */
        :root {
            --drop-background-color: {{drop.background_color}};
            --drop-card-color: {{drop.card_color}};
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{getContrastColor drop.button_color}};
            --drop-form-field-color: {{drop.form_field_color}};
        }

        body.drop-landing-page {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            /* RESEARCH-BASED: Ensure body takes full height for proper background */
            height: 100%;
            overflow-x: hidden;
            /* SAFARI FIX: Prevent bounce scrolling */
            -webkit-overflow-scrolling: touch;
            /* MODERN: Better box-sizing */
            box-sizing: border-box;
            /* CRITICAL: Apply background to body for consistent appearance */
            background: linear-gradient(135deg, var(--drop-background-color, #f8fafc) 0%, color-mix(in srgb, var(--drop-background-color, #f8fafc) 85%, #ffffff) 100%);
            min-height: 100vh;
        }



        /* Messages - Keep only essential styles not in modular system */
        .success-message,
        .error-message {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.1);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .success-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .button-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 🎯 ENSURE PERFECT FORM LAYOUT CONSISTENCY */
        .signup-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 100%;
            max-width: 100%;
        }

        .form-group {
            position: relative;
            width: 100%;
            margin: 0;
        }

        /* CRITICAL: Ensure form field color variables work on live page */
        .form-input {
            background: var(--drop-form-field-color, #ffffff) !important;
        }
    </style>

    <!-- Custom drop styles -->
    {{#if drop.custom_css}}
    <style>
        {{{drop.custom_css}}}
    </style>
    {{/if}}
</head>
<body class="drop-landing-page {{deviceType}}-device" data-device="{{deviceType}}">
    <div class="drop-page-wrapper">
        <div class="drop-container">
        <!-- Header -->
        <header class="drop-header">
            <div class="drop-brand">
                <a href="/" class="brand-link">
                    <img src="/images/logo.png" alt="BOUNCE2BOUNCE" class="brand-logo">
                    <span class="brand-text">BOUNCE2BOUNCE</span>
                </a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="drop-main">
            <div class="drop-content">
                {{#if drop.cover_image}}
                <div class="drop-cover-image">
                    <img src="{{drop.cover_image}}" alt="{{drop.title}}" loading="lazy">
                </div>
                {{/if}}

                <div class="drop-info">
                    <h1 class="drop-title">{{drop.title}}</h1>

                    {{#if drop.description}}
                    <div class="drop-description">
                        <p>{{drop.description}}</p>
                    </div>
                    {{/if}}

                    {{#if drop.signup_count}}
                    <div class="drop-stats">
                        <span class="signup-count">{{drop.signup_count}} people signed up</span>
                    </div>
                    {{/if}}
                </div>

                <!-- Signup Form -->
                <div class="drop-signup-section">
                    <form id="drop-signup-form" class="signup-form">
                        {{#if drop.collect_email}}
                        <div class="form-group">
                            <label for="email" class="sr-only">Email address</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                placeholder="Enter your email"
                                required
                                class="form-input"
                            >
                        </div>
                        {{/if}}

                        {{#if drop.collect_phone}}
                        <div class="form-group">
                            <label for="phone" class="sr-only">Phone number</label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                placeholder="Enter your phone number"
                                class="form-input"
                            >
                        </div>
                        {{/if}}

                        <div class="form-group">
                            <label for="name" class="sr-only">Name (optional)</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                placeholder="Your name (optional)"
                                class="form-input"
                            >
                        </div>

                        <button
                            type="submit"
                            class="signup-button"
                        >
                            <span class="button-text">{{drop.button_text}}</span>
                            <span class="button-spinner" style="display: none;">
                                <svg class="spinner" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"/>
                                    <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
                                </svg>
                            </span>
                        </button>
                    </form>

                    <!-- Success Message -->
                    <div id="success-message" class="success-message" style="display: none;">
                        <div class="success-icon">✓</div>
                        <p id="success-text"></p>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="error-message" style="display: none;">
                        <p id="error-text"></p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="drop-footer">
            <p>Powered by <a href="/" class="footer-link">BOUNCE2BOUNCE</a></p>
        </footer>
        </div>
    </div>

    <!-- Modular Color System -->
    <script src="/js/drop-color-system.js"></script>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('drop-signup-form');
            const submitButton = form.querySelector('.signup-button');
            const buttonText = submitButton.querySelector('.button-text');
            const buttonSpinner = submitButton.querySelector('.button-spinner');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');
            const successText = document.getElementById('success-text');
            const errorText = document.getElementById('error-text');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                // Reset messages
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';

                // Show loading state
                submitButton.disabled = true;
                buttonText.style.display = 'none';
                buttonSpinner.style.display = 'inline-flex';

                try {
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData.entries());

                    console.log('🚀 Submitting form data:', data);
                    console.log('🚀 Submitting to URL:', '/drop/signup/{{drop.slug}}');

                    const response = await fetch('/drop/signup/{{drop.slug}}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    console.log('🚀 Response status:', response.status);
                    console.log('🚀 Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();
                    console.log('🚀 Response data:', result);

                    if (result.success) {
                        // Success
                        successText.textContent = result.message;
                        successMessage.style.display = 'block';
                        form.style.display = 'none';
                    } else {
                        // Error
                        errorText.textContent = result.message || 'An error occurred. Please try again.';
                        errorMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('🚨 Form submission error:', error);

                    // More specific error messages
                    if (error.message.includes('HTTP 404')) {
                        errorText.textContent = 'Drop not found. Please check the URL and try again.';
                    } else if (error.message.includes('HTTP 400')) {
                        errorText.textContent = 'Invalid form data. Please check your information and try again.';
                    } else if (error.message.includes('HTTP 500')) {
                        errorText.textContent = 'Server error. Please try again in a few moments.';
                    } else if (error.message.includes('Failed to fetch') || error.message.includes('Network')) {
                        errorText.textContent = 'Network error. Please check your connection and try again.';
                    } else {
                        errorText.textContent = error.message || 'An unexpected error occurred. Please try again.';
                    }

                    errorMessage.style.display = 'block';
                } finally {
                    // Reset button state
                    submitButton.disabled = false;
                    buttonText.style.display = 'inline';
                    buttonSpinner.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
