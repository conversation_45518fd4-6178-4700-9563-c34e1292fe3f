<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{drop.title}} - BOUNCE2BOUNCE</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{drop.description}}">
    <meta name="keywords" content="music, artist, drop, signup, {{drop.title}}">
    <meta name="author" content="BOUNCE2BOUNCE">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta property="og:description" content="{{drop.description}}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://{{default_domain}}/drop/{{drop.slug}}">
    {{#if drop.cover_image}}
    <meta property="og:image" content="{{drop.cover_image}}">
    {{/if}}
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta name="twitter:description" content="{{drop.description}}">
    {{#if drop.cover_image}}
    <meta name="twitter:image" content="{{drop.cover_image}}">
    {{/if}}

    <!-- HTML GlobalBank Template Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    
    <!-- HTML GlobalBank Template CSS -->
    <link rel="stylesheet" href="/css/globalbank-authentic.css"/>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon.ico"/>

    <!-- Laylo-Inspired Design CSS -->
    <style>
        :root {
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{drop.button_text_color}};
            --drop-background-color: {{drop.background_color}};

            /* Laylo Design System */
            --laylo-red: #DC2626;
            --laylo-dark: #1F2937;
            --laylo-card-bg: #111827;
            --laylo-text-primary: #FFFFFF;
            --laylo-text-secondary: #9CA3AF;
            --laylo-border-radius: 24px;
            --laylo-button-red: #EF4444;
        }

        /* Laylo-Inspired Layout */
        body {
            background: linear-gradient(135deg, var(--laylo-red) 0%, #B91C1C 100%);
            font-family: 'Clash Grotesk', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .laylo-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }

        /* Hero Section - Laylo Style */
        .laylo-hero {
            text-align: center;
            margin-bottom: 32px;
        }

        .laylo-cover-image {
            width: 100%;
            max-width: 350px;
            height: auto;
            border-radius: var(--laylo-border-radius);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            margin-bottom: 24px;
        }

        /* Title Section - Laylo Style */
        .laylo-title-section {
            text-align: center;
            margin-bottom: 32px;
            padding: 24px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--laylo-border-radius);
            backdrop-filter: blur(10px);
        }

        .laylo-main-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--laylo-text-primary);
            margin: 0 0 8px 0;
            letter-spacing: -0.02em;
            text-transform: uppercase;
        }

        .laylo-subtitle {
            font-size: 1rem;
            font-weight: 500;
            color: var(--laylo-text-primary);
            margin: 0;
            opacity: 0.9;
        }

        /* Contact Form Card - Laylo Style with Inlaid Button */
        .laylo-form-card {
            background: var(--laylo-card-bg);
            border-radius: var(--laylo-border-radius);
            padding: 32px 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            margin-bottom: 24px;
        }

        .laylo-form-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--laylo-text-primary);
            text-align: center;
            margin: 0 0 24px 0;
        }

        /* Inlaid Button Input Design */
        .laylo-input-container {
            position: relative;
            margin-bottom: 16px;
        }

        .laylo-input {
            width: 100%;
            padding: 16px 120px 16px 50px;
            background: #374151;
            border: none;
            border-radius: 50px;
            color: var(--laylo-text-primary);
            font-size: 1rem;
            font-family: inherit;
            outline: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .laylo-input:focus {
            background: #4B5563;
            box-shadow: 0 0 0 2px var(--laylo-button-red);
        }

        .laylo-input::placeholder {
            color: var(--laylo-text-secondary);
        }

        /* Country Flag Icon */
        .laylo-flag-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            z-index: 2;
        }

        /* Inlaid RSVP Button */
        .laylo-inlaid-button {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--laylo-button-red);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 3;
            font-family: inherit;
        }

        .laylo-inlaid-button:hover {
            background: #DC2626;
            transform: translateY(-50%) scale(1.05);
        }

        .laylo-inlaid-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* Form Footer */
        .laylo-form-footer {
            font-size: 0.75rem;
            color: var(--laylo-text-secondary);
            text-align: center;
            line-height: 1.4;
            margin-top: 16px;
        }

        .laylo-form-footer a {
            color: var(--laylo-text-secondary);
            text-decoration: underline;
        }

        /* Platform Links - Laylo Style */
        .laylo-platforms {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 24px;
        }

        .laylo-platform-link {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .laylo-platform-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .laylo-platform-link svg {
            width: 24px;
            height: 24px;
        }

        /* Success/Error Messages */
        .laylo-message {
            padding: 16px;
            border-radius: var(--laylo-border-radius);
            margin-bottom: 16px;
            text-align: center;
            font-weight: 500;
        }

        .laylo-success {
            background: #10B981;
            color: white;
        }

        .laylo-error {
            background: #EF4444;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            body {
                padding: 16px;
            }

            .laylo-main-title {
                font-size: 2rem;
            }

            .laylo-input {
                padding: 14px 100px 14px 45px;
                font-size: 0.9rem;
            }

            .laylo-inlaid-button {
                padding: 10px 20px;
                font-size: 0.8rem;
            }
        }

        /* Hidden class */
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="laylo-container">
        <!-- Hero Section - Laylo Style -->
        <div class="laylo-hero">
            {{#if drop.cover_image}}
            <img src="{{drop.cover_image}}" alt="{{drop.title}}" class="laylo-cover-image"/>
            {{/if}}
        </div>

        <!-- Title and Subtitle Section - Laylo Style -->
        <div class="laylo-title-section">
            <h1 class="laylo-main-title">YOUR INVITE</h1>
            <p class="laylo-subtitle">{{drop.title}}</p>
        </div>

        <!-- Contact Form Card - Laylo Style with Inlaid Button -->
        <div class="laylo-form-card">
            <h2 class="laylo-form-title">Get notified</h2>

            <!-- Success/Error Messages -->
            <div id="success-message" class="laylo-message laylo-success hidden">
                <p id="success-text">Thank you for signing up!</p>
            </div>
            <div id="error-message" class="laylo-message laylo-error hidden">
                <p id="error-text">Something went wrong. Please try again.</p>
            </div>

            <form id="drop-signup-form">
                <!-- Email Input (if enabled) -->
                {{#if drop.collect_email}}
                <div class="laylo-input-container">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        required
                        class="laylo-input"
                        style="padding-left: 16px; padding-right: 16px;"
                    />
                </div>
                {{/if}}

                <!-- Phone Input with Inlaid Button -->
                <div class="laylo-input-container">
                    <span class="laylo-flag-icon">🇺🇸</span>
                    <input
                        type="tel"
                        name="phone"
                        placeholder="****** 801 7985"
                        {{#if drop.collect_phone}}required{{/if}}
                        class="laylo-input"
                    />
                    <button type="submit" class="laylo-inlaid-button">
                        {{drop.button_text}}
                    </button>
                </div>

                <!-- Name Input (optional) -->
                <div class="laylo-input-container">
                    <input
                        type="text"
                        name="name"
                        placeholder="Your name (optional)"
                        class="laylo-input"
                        style="padding-left: 16px; padding-right: 16px;"
                    />
                </div>
            </form>

            <div class="laylo-form-footer">
                This site is protected by reCaptcha. By submitting my information, I agree to receive recurring automated messages to the contact information provided and to <a href="#">Laylo's Terms of Service</a>, <a href="#">Cookie Policy</a> and <a href="#">Privacy Policy</a>. Msg & Data Rates may apply. Reply STOP to cancel, HELP for help.
            </div>
        </div>

        <!-- Platform Links - Laylo Style -->
        {{#if (or drop.website_link drop.instagram_link drop.twitter_link drop.spotify_link drop.youtube_link drop.apple_music_url drop.soundcloud_url drop.tiktok_link)}}
        <div class="laylo-platforms">
            {{#if drop.website_link}}
            <a href="{{drop.website_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.instagram_link}}
            <a href="{{drop.instagram_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.40s-.644-1.44-1.439-1.44z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.twitter_link}}
            <a href="{{drop.twitter_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13.12 10.946 19.682 3h-1.554l-5.693 6.618L8.15 3H3l6.876 10.007L3 21h1.554l6.012-6.989L15.85 21h5.15l-7.88-10.054Zm-2.128 2.474-.697-.997L4.771 4.149h2.389l4.47 6.39.697.996 5.815 8.314h-2.39l-4.74-6.779Z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.spotify_link}}
            <a href="{{drop.spotify_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.youtube_link}}
            <a href="{{drop.youtube_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.apple_music_url}}
            <a href="{{drop.apple_music_url}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.soundcloud_url}}
            <a href="{{drop.soundcloud_url}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M1.175 12.225c-.051 0-.094.046-.101.1l-.233 2.154.233 2.105c.007.058.05.104.101.104.053 0 .094-.046.101-.104l.255-2.105-.255-2.154c-.007-.054-.048-.1-.101-.1zm1.655.096c-.058 0-.106.053-.113.115l-.138 1.958.138 1.887c.007.058.055.115.113.115.058 0 .102-.057.113-.115l.158-1.887-.158-1.958c-.011-.062-.055-.115-.113-.115zm1.565.019c-.062 0-.111.057-.125.126l-.1 1.939.1 1.852c.014.07.063.126.125.126.067 0 .111-.056.125-.126l.111-1.852-.111-1.939c-.014-.069-.058-.126-.125-.126zm1.558.021c-.071 0-.125.061-.134.138l-.071 1.918.071 1.821c.009.077.063.138.134.138.075 0 .125-.061.134-.138l.096-1.821-.096-1.918c-.009-.077-.059-.138-.134-.138zm1.567.015c-.078 0-.134.069-.146.15l-.067 1.923.067 1.798c.012.082.068.15.146.15.082 0 .134-.068.146-.15l.078-1.798-.078-1.923c-.012-.081-.064-.15-.146-.15zm1.543 0c-.089 0-.15.077-.15.168l-.054 1.905.054 1.77c0 .091.061.168.15.168.094 0 .15-.077.15-.168l.067-1.77-.067-1.905c0-.091-.056-.168-.15-.168zm1.558-.004c-.097 0-.167.08-.167.176l-.04 1.909.04 1.752c0 .096.07.176.167.176.102 0 .167-.08.167-.176l.051-1.752-.051-1.909c0-.096-.065-.176-.167-.176zm1.567 0c-.11 0-.184.089-.184.199l-.034 1.905.034 1.738c0 .11.074.199.184.199.115 0 .184-.089.184-.199l.04-1.738-.04-1.905c0-.11-.069-.199-.184-.199zm1.575-.011c-.115 0-.199.093-.199.207l-.029 1.916.029 1.728c0 .118.084.207.199.207.122 0 .199-.089.199-.207l.034-1.728-.034-1.916c0-.114-.077-.207-.199-.207zm1.563-.004c-.126 0-.211.097-.211.211l-.025 1.912.025 1.716c0 .118.085.211.211.211.131 0 .211-.093.211-.211l.029-1.716-.029-1.912c0-.114-.08-.211-.211-.211zm1.567-.007c-.138 0-.218.103-.218.218l-.022 1.905.022 1.712c0 .115.08.218.218.218.143 0 .218-.103.218-.218l.025-1.712-.025-1.905c0-.115-.075-.218-.218-.218zm1.575-.003c-.15 0-.243.111-.243.243l-.018 1.902.018 1.699c0 .132.093.243.243.243.155 0 .243-.111.243-.243l.022-1.699-.022-1.902c0-.132-.088-.243-.243-.243zm1.567-.007c-.158 0-.25.114-.25.25l-.014 1.895.014 1.703c0 .136.092.25.25.25.165 0 .25-.114.25-.25l.018-1.703-.018-1.895c0-.136-.085-.25-.25-.25zm1.558-.004c-.172 0-.265.118-.265.265l-.011 1.891.011 1.699c0 .147.093.265.265.265.179 0 .265-.118.265-.265l.014-1.699-.014-1.891c0-.147-.086-.265-.265-.265zm1.567-.011c-.186 0-.277.125-.277.277l-.007 1.884.007 1.692c0 .152.091.277.277.277.19 0 .277-.125.277-.277l.011-1.692-.011-1.884c0-.152-.087-.277-.277-.277zm1.575-.003c-.197 0-.29.132-.29.29l-.004 1.881.004 1.688c0 .158.093.29.29.29.204 0 .29-.132.29-.29l.007-1.688-.007-1.881c0-.158-.086-.29-.29-.29z"/>
                </svg>
            </a>
            {{/if}}

            {{#if drop.tiktok_link}}
            <a href="{{drop.tiktok_link}}" target="_blank" rel="noopener noreferrer" class="laylo-platform-link">
                <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                </svg>
            </a>
            {{/if}}
        </div>
        {{/if}}
    </div>

    <!-- JavaScript for Form Handling -->
    <script>
        document.getElementById('drop-signup-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const form = e.target;
            const formData = new FormData(form);
            const email = formData.get('email');
            const phone = formData.get('phone');
            const name = formData.get('name');

            const submitButton = form.querySelector('button[type="submit"]');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');

            // Hide previous messages
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');

            // Disable button and show loading state
            submitButton.disabled = true;
            const originalText = submitButton.textContent;
            submitButton.textContent = 'RSVP...';

            // Prepare data to send
            const dataToSend = {};
            if (email) dataToSend.email = email;
            if (phone) dataToSend.phone = phone;
            if (name) dataToSend.name = name;

            try {
                const response = await fetch('/drop/signup/{{drop.slug}}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(dataToSend)
                });

                const result = await response.json();

                if (response.ok) {
                    successMessage.classList.remove('hidden');
                    document.getElementById('success-text').textContent = 'Thank you! You\'re on the list.';
                    form.reset();
                } else {
                    errorMessage.classList.remove('hidden');
                    document.getElementById('error-text').textContent = result.message || 'Something went wrong. Please try again.';
                }
            } catch (error) {
                errorMessage.classList.remove('hidden');
                document.getElementById('error-text').textContent = 'Network error. Please try again.';
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }
        });

        // Add phone number formatting
        const phoneInput = document.querySelector('input[name="phone"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 0) {
                    if (value.length <= 3) {
                        value = `+1 ${value}`;
                    } else if (value.length <= 6) {
                        value = `+1 ${value.slice(0, 3)} ${value.slice(3)}`;
                    } else if (value.length <= 10) {
                        value = `+1 ${value.slice(0, 3)} ${value.slice(3, 6)} ${value.slice(6)}`;
                    } else {
                        value = `+1 ${value.slice(0, 3)} ${value.slice(3, 6)} ${value.slice(6, 10)}`;
                    }
                }
                e.target.value = value;
            });
        }
    </script>
</body>
</html>
