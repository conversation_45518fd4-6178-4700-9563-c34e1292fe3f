<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!-- RESEARCH-BASED: Optimal viewport meta tag for mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <title>{{pageTitle}} | {{config.SITE_NAME}}</title>

    <!-- Meta tags for social sharing -->
    <meta name="description" content="{{metaDescription}}">
    <meta property="og:title" content="{{pageTitle}}">
    <meta property="og:description" content="{{metaDescription}}">
    {{#if metaImage}}
    <meta property="og:image" content="{{metaImage}}">
    {{/if}}
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{pageTitle}}">
    <meta name="twitter:description" content="{{metaDescription}}">
    {{#if metaImage}}
    <meta name="twitter:image" content="{{metaImage}}">
    {{/if}}

    <!-- Favicon -->
    <link rel="icon" href="/images/favicon.ico">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Modular Drop System -->
    <link rel="stylesheet" href="/css/drop-shared.css">

    <!-- Glassmorphism Styling (Separate from Dashboard) -->
    <link rel="stylesheet" href="/css/drop-glassmorphism.css">

    <!-- Drop Landing Page Styles -->
    <style>
        /* Modular Drop System - CSS Variables */
        :root {
            --drop-background-color: {{drop.background_color}};
            --drop-card-color: {{drop.card_color}};
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{getContrastColor drop.button_color}};
            --drop-form-field-color: {{drop.form_field_color}};
        }

        body.drop-landing-page {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            /* RESEARCH-BASED: Ensure body takes full height for proper background */
            height: 100%;
            overflow-x: hidden;
            /* SAFARI FIX: Prevent bounce scrolling */
            -webkit-overflow-scrolling: touch;
            /* MODERN: Better box-sizing */
            box-sizing: border-box;
            /* GLASSMORPHISM: Enhanced gradient background with depth */
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, var(--drop-background-color, #f8fafc) 0%, color-mix(in srgb, var(--drop-background-color, #f8fafc) 85%, #ffffff) 100%);
            min-height: 100vh;
            /* GLASSMORPHISM: Add subtle texture */
            position: relative;
        }

        body.drop-landing-page::before {
            /* GLASSMORPHISM: Subtle noise texture overlay */
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
            background-size: 20px 20px;
            pointer-events: none;
            z-index: 1;
            opacity: 0.5;
        }



        /* Messages - Keep only essential styles not in modular system */
        .success-message,
        .error-message {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.1);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .success-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .button-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 🎯 ENSURE PERFECT FORM LAYOUT CONSISTENCY */
        .signup-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 100%;
            max-width: 100%;
        }

        .form-group {
            position: relative;
            width: 100%;
            margin: 0;
        }

        /* GLASSMORPHISM: Enhanced drop container styling */
        .drop-container {
            position: relative;
            z-index: 2;
        }

        /* GLASSMORPHISM: Form field integration with CSS variables */
        .form-input {
            background: color-mix(in srgb, var(--drop-form-field-color, #ffffff) 80%, transparent 20%) !important;
        }

        /* GLASSMORPHISM: Button integration with CSS variables */
        .signup-button {
            background: linear-gradient(135deg,
                color-mix(in srgb, var(--drop-button-color, #007bff) 90%, rgba(255,255,255,0.2) 10%) 0%,
                color-mix(in srgb, var(--drop-button-color, #007bff) 80%, rgba(255,255,255,0.1) 20%) 100%) !important;
            color: var(--drop-button-text-color, #ffffff) !important;
        }

        /* GLASSMORPHISM: Enhanced card styling with CSS variables */
        .drop-main {
            background: color-mix(in srgb, var(--drop-card-color, #ffffff) 85%, transparent 15%);
        }
    </style>

    <!-- Custom drop styles -->
    {{#if drop.custom_css}}
    <style>
        {{{drop.custom_css}}}
    </style>
    {{/if}}
</head>
<body class="drop-landing-page {{deviceType}}-device" data-device="{{deviceType}}">
    <div class="drop-page-wrapper">
        <div class="drop-container glass-card">
        <!-- Header -->
        <header class="drop-header glass-header">
            <div class="drop-brand">
                <a href="/" class="brand-link">
                    <img src="/images/logo.png" alt="BOUNCE2BOUNCE" class="brand-logo">
                    <span class="brand-text">BOUNCE2BOUNCE</span>
                </a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="drop-main">
            <div class="drop-content">
                {{#if drop.cover_image}}
                <div class="drop-cover-image">
                    <img src="{{drop.cover_image}}" alt="{{drop.title}}" loading="lazy">
                </div>
                {{/if}}

                <div class="drop-info">
                    <h1 class="drop-title">{{drop.title}}</h1>

                    {{#if drop.description}}
                    <div class="drop-description">
                        <p>{{drop.description}}</p>
                    </div>
                    {{/if}}

                    {{#if drop.signup_count}}
                    <div class="drop-stats">
                        <span class="signup-count">{{drop.signup_count}} people signed up</span>
                    </div>
                    {{/if}}
                </div>

                <!-- Platform Links with Glassmorphism -->
                {{#if (or drop.website_link drop.instagram_link drop.twitter_link drop.spotify_link drop.youtube_link drop.apple_music_url drop.soundcloud_url drop.tiktok_link)}}
                <div class="platform-links glassmorphism-container">
                    <div class="platform-links-grid">
                        {{#if drop.website_link}}
                        <a href="{{drop.website_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            <span>Website</span>
                        </a>
                        {{/if}}

                        {{#if drop.instagram_link}}
                        <a href="{{drop.instagram_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z"/>
                            </svg>
                            <span>Instagram</span>
                        </a>
                        {{/if}}

                        {{#if drop.twitter_link}}
                        <a href="{{drop.twitter_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                            <span>Twitter</span>
                        </a>
                        {{/if}}

                        {{#if drop.spotify_link}}
                        <a href="{{drop.spotify_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 14.5c-.17.29-.48.46-.81.46-.16 0-.33-.04-.47-.13-1.44-.83-3.22-1.21-5.25-1.21-1.52 0-2.8.19-2.8.19-.55 0-1-.45-1-1s.45-1 1-1c0 0 1.54-.23 3.55-.23 2.29 0 4.29.48 5.94 1.44.49.28.66.89.38 1.38l.46.1zm1.14-2.8c-.2.33-.56.53-.94.53-.18 0-.36-.05-.53-.14-1.8-1.04-4.02-1.51-6.63-1.51-1.78 0-3.14.18-3.14.18-.64 0-1.16-.52-1.16-1.16s.52-1.16 1.16-1.16c0 0 1.54-.21 3.67-.21 2.74 0 5.3.6 7.44 1.68.58.29.82 1.02.53 1.6l.6.19zm1.07-3.14c-.22.37-.63.59-1.07.59-.2 0-.41-.05-.6-.16-2.23-1.21-5.25-1.79-8.73-1.79-2.078 0-3.66.21-3.66.21-.72 0-1.31-.59-1.31-1.31s.59-1.31 1.31-1.31c0 0 1.74-.24 4.19-.24 3.64 0 7.17.77 9.77 2.15.67.35.93 1.15.58 1.82l.52.04z"/>
                            </svg>
                            <span>Spotify</span>
                        </a>
                        {{/if}}

                        {{#if drop.youtube_link}}
                        <a href="{{drop.youtube_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M10 15l5.19-3L10 9v6m11.56-7.83c.13.47.22 1.1.28 1.9.07.8.1 1.49.1 2.09L22 12c0 2.19-.16 3.8-.44 4.83-.25.9-.83 1.48-1.73 1.73-.47.13-1.33.22-2.65.28-1.3.07-2.49.1-3.59.1L12 19c-4.19 0-6.8-.16-7.83-.44-.9-.25-1.48-.83-1.73-1.73-.13-.47-.22-1.1-.28-1.9-.07-.8-.1-1.49-.1-2.09L2 12c0-2.19.16-3.8.44-4.83.25-.9.83-1.48 1.73-1.73.47-.13 1.33-.22 2.65-.28 1.3-.07 2.49-.1 3.59-.1L12 5c4.19 0 6.8.16 7.83.44.9.25 1.48.83 1.73 1.73z"/>
                            </svg>
                            <span>YouTube</span>
                        </a>
                        {{/if}}

                        {{#if drop.apple_music_url}}
                        <a href="{{drop.apple_music_url}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.997 6.124c0-.738-.065-1.47-.24-2.19-.317-1.31-1.062-2.31-2.18-3.043C21.003.517 20.373.285 19.7.164c-.517-.093-1.038-.135-1.564-.15-.04-.003-.083-.01-.124-.013H5.988c-.152.01-.303.017-.455.034C4.786.07 4.043.15 3.34.428 2.004.958 1.04 1.88.475 3.208c-.192.448-.292.925-.363 1.408-.056.392-.088.785-.1 1.18-.013.43-.024.86-.024 1.29v9.884c0 .21.006.42.014.63.01.367.025.735.06 1.101.09 1.004.32 1.972.86 2.837.592 .951 1.42 1.618 2.52 1.993.742.253 1.518.328 2.304.36.36.015.72.025 1.08.025h12.016c.17 0 .34-.01.51-.014.493-.01.98-.044 1.464-.123 1.005-.165 1.933-.475 2.754-1.108.88-.677 1.477-1.54 1.767-2.62.138-.512.196-1.04.237-1.57.024-.302.032-.604.04-.906.007-.3.012-.6.012-.9V6.124zM12.24 4.011c2.928 0 5.289 2.37 5.289 5.289 0 2.928-2.36 5.298-5.289 5.298S6.951 12.228 6.951 9.3c0-2.918 2.36-5.289 5.289-5.289zm0 9.878c2.52 0 4.579-2.05 4.579-4.589a4.584 4.584 0 0 0-4.579-4.579c-2.539 0-4.589 2.04-4.589 4.579 0 2.539 2.05 4.589 4.589 4.589z"/>
                            </svg>
                            <span>Apple Music</span>
                        </a>
                        {{/if}}

                        {{#if drop.soundcloud_url}}
                        <a href="{{drop.soundcloud_url}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M1.175 12.225c-.051 0-.094.046-.101.105l-.233 2.154.233 2.105c.007.059.05.104.101.104.05 0 .093-.045.101-.104L1.509 14.484l-.233-2.154c-.008-.059-.051-.105-.101-.105zm1.49.105c-.059 0-.109.053-.117.12l-.184 2.045.184 2.024c.008.067.058.12.117.12.058 0 .108-.053.117-.12l.201-2.024-.201-2.045c-.009-.067-.059-.12-.117-.12zm1.609.075c-.067 0-.122.058-.131.135l-.142 1.965.142 1.946c.009.077.064.135.131.135.066 0 .121-.058.13-.135l.158-1.946-.158-1.965c-.009-.077-.064-.135-.13-.135zm1.554.054c-.075 0-.137.067-.146.15l-.109 1.91.109 1.893c.009.083.071.15.146.15.074 0 .136-.067.145-.15l.125-1.893-.125-1.91c-.009-.083-.071-.15-.145-.15z"/>
                            </svg>
                            <span>SoundCloud</span>
                        </a>
                        {{/if}}

                        {{#if drop.tiktok_link}}
                        <a href="{{drop.tiktok_link}}" target="_blank" rel="noopener noreferrer" class="platform-link glass-button">
                            <svg class="platform-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                            </svg>
                            <span>TikTok</span>
                        </a>
                        {{/if}}
                    </div>
                </div>
                {{/if}}

                <!-- Glassmorphism Signup Form -->
                <div class="drop-signup-section">
                    <form id="drop-signup-form" class="signup-form glassmorphism-container">
                        {{#if drop.collect_email}}
                        <div class="form-group">
                            <label for="email" class="sr-only">Email address</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                placeholder="Enter your email"
                                required
                                class="form-input glass-input"
                            >
                        </div>
                        {{/if}}

                        {{#if drop.collect_phone}}
                        <div class="form-group">
                            <label for="phone" class="sr-only">Phone number</label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                placeholder="Enter your phone number"
                                class="form-input glass-input"
                            >
                        </div>
                        {{/if}}

                        <div class="form-group">
                            <label for="name" class="sr-only">Name (optional)</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                placeholder="Your name (optional)"
                                class="form-input glass-input"
                            >
                        </div>

                        <button
                            type="submit"
                            class="signup-button glass-button"
                        >
                            <span class="button-text">{{drop.button_text}}</span>
                            <span class="button-spinner" style="display: none;">
                                <svg class="spinner" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"/>
                                    <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
                                </svg>
                            </span>
                        </button>
                    </form>

                    <!-- Success Message -->
                    <div id="success-message" class="success-message" style="display: none;">
                        <div class="success-icon">✓</div>
                        <p id="success-text"></p>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="error-message" style="display: none;">
                        <p id="error-text"></p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Glassmorphism Footer -->
        <footer class="drop-footer glass-footer">
            <p>Powered by <a href="/" class="footer-link">BOUNCE2BOUNCE</a></p>
        </footer>
        </div>
    </div>

    <!-- Modular Color System -->
    <script src="/js/drop-color-system.js"></script>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('drop-signup-form');
            const submitButton = form.querySelector('.signup-button');
            const buttonText = submitButton.querySelector('.button-text');
            const buttonSpinner = submitButton.querySelector('.button-spinner');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');
            const successText = document.getElementById('success-text');
            const errorText = document.getElementById('error-text');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                // Reset messages
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';

                // Show loading state
                submitButton.disabled = true;
                buttonText.style.display = 'none';
                buttonSpinner.style.display = 'inline-flex';

                try {
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData.entries());

                    console.log('🚀 Submitting form data:', data);
                    console.log('🚀 Submitting to URL:', '/drop/signup/{{drop.slug}}');

                    const response = await fetch('/drop/signup/{{drop.slug}}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    console.log('🚀 Response status:', response.status);
                    console.log('🚀 Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();
                    console.log('🚀 Response data:', result);

                    if (result.success) {
                        // Success
                        successText.textContent = result.message;
                        successMessage.style.display = 'block';
                        form.style.display = 'none';
                    } else {
                        // Error
                        errorText.textContent = result.message || 'An error occurred. Please try again.';
                        errorMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('🚨 Form submission error:', error);

                    // More specific error messages
                    if (error.message.includes('HTTP 404')) {
                        errorText.textContent = 'Drop not found. Please check the URL and try again.';
                    } else if (error.message.includes('HTTP 400')) {
                        errorText.textContent = 'Invalid form data. Please check your information and try again.';
                    } else if (error.message.includes('HTTP 500')) {
                        errorText.textContent = 'Server error. Please try again in a few moments.';
                    } else if (error.message.includes('Failed to fetch') || error.message.includes('Network')) {
                        errorText.textContent = 'Network error. Please check your connection and try again.';
                    } else {
                        errorText.textContent = error.message || 'An unexpected error occurred. Please try again.';
                    }

                    errorMessage.style.display = 'block';
                } finally {
                    // Reset button state
                    submitButton.disabled = false;
                    buttonText.style.display = 'inline';
                    buttonSpinner.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
