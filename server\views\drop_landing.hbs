<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{drop.title}} - BOUNCE2BOUNCE</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{drop.description}}">
    <meta name="keywords" content="music, artist, drop, signup, {{drop.title}}">
    <meta name="author" content="BOUNCE2BOUNCE">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta property="og:description" content="{{drop.description}}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://{{default_domain}}/drop/{{drop.slug}}">
    {{#if drop.cover_image}}
    <meta property="og:image" content="{{drop.cover_image}}">
    {{/if}}
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta name="twitter:description" content="{{drop.description}}">
    {{#if drop.cover_image}}
    <meta name="twitter:image" content="{{drop.cover_image}}">
    {{/if}}

    <!-- HTML GlobalBank Template Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    
    <!-- HTML GlobalBank Template CSS -->
    <link rel="stylesheet" href="/css/globalbank-authentic.css"/>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon.ico"/>

    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Laylo-Inspired Design CSS -->
    <style>
        :root {
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{drop.button_text_color}};
            --drop-background-color: {{drop.background_color}};

            /* Laylo Design System - Exact Colors */
            --laylo-red: #ff0003;
            --laylo-dark: #1F2937;
            --laylo-card-bg: #1a1a1a;
            --laylo-text-primary: #FFFFFF;
            --laylo-text-secondary: #687178;
            --laylo-border-radius: 24px;
            --laylo-button-red: #ff0003;
            --laylo-input-bg: rgba(255, 255, 255, 0.1);
            --laylo-input-focus: rgba(255, 255, 255, 0.15);
        }

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
        }

        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            min-height: 100vh;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            overscroll-behavior: none;
            background: transparent !important;
            overflow-x: hidden;
        }

        /* Enhanced Background System - Fixed for Industry-Leading Quality */
        .drop-page-container {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: clamp(16px, 12vh, 120px) clamp(16px, 12vw, 185.5px) 24px;
            min-height: 100vh;
            width: 100%;
            max-width: 100vw;
            overflow-x: hidden;
            {{#if (eq drop.background_type "solid")}}
            background: {{#if drop.background_color}}{{drop.background_color}}{{else}}var(--laylo-red){{/if}};
            {{else}}
                {{#if drop.gradient_data}}
                    {{! Use gradient data if available }}
                    background: {{{parseGradientData drop.gradient_data}}};
                {{else}}
                    {{! Fallback gradient }}
                    background: linear-gradient(
                        179deg,
                        {{#if drop.background_color}}{{drop.background_color}}{{else}}var(--laylo-red){{/if}} 0%,
                        {{#if drop.background_color}}{{drop.background_color}}CC{{else}}#DC2626CC{{/if}} 30%,
                        {{#if drop.background_color}}{{drop.background_color}}80{{else}}#DC262680{{/if}} 60%,
                        rgba(0, 0, 0, 0.9) 100%
                    );
                {{/if}}
            {{/if}}
            background-attachment: fixed;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
        }

        .drop-container {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: clamp(24px, 5vw, 48px);
            width: 100%;
            max-width: 1400px;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        /* Image Container - Industry-Leading Responsive Design */
        .drop-image-container {
            position: relative;
            width: 100%;
            max-width: min(690px, 58.3333vw);
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            background: transparent;
            grid-column: 1;
        }

        .drop-image-container img {
            width: 100%;
            height: auto;
            max-width: 100%;
            border-radius: 12px;
            object-fit: cover;
            background: transparent;
            opacity: 0;
            animation: bloom 0.3s cubic-bezier(0.2, 0.2, 0.25, 1) normal forwards;
            animation-delay: 0.15s;
            display: block;
        }

        @keyframes bloom {
            0% {
                opacity: 0;
                filter: blur(2rem);
                transform: perspective(10rem) translateZ(-2rem);
            }
            100% {
                opacity: 1;
                filter: blur(0);
                transform: perspective(10rem) translateZ(0);
            }
        }

        /* RSVP Container - Industry-Leading Responsive Design */
        .rsvp-container {
            width: 100%;
            max-width: min(398px, 100%);
            padding-bottom: 48px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: transparent;
            grid-column: 2;
        }

        /* Title - Perfect Content Card Width Matching */
        .drop-title {
            font-size: 64px;
            line-height: 1.1;
            letter-spacing: 0px;
            text-align: left;
            margin: 0 0 8px 0;
            font-weight: 800;
            text-size-adjust: 100%;
            text-wrap: wrap;
            white-space-collapse: preserve-breaks;
            color: {{#if drop.title_color}}{{drop.title_color}}{{else}}var(--laylo-text-primary){{/if}};
            /* Perfect alignment with content card */
            width: 100%;
            max-width: 400px;
            box-sizing: border-box;
        }

        /* User Container - Perfect Left-Aligned Subtitle */
        .user-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin-bottom: 16px;
            text-align: left;
            justify-content: flex-start;
            width: 100%;
            max-width: 400px;
            box-sizing: border-box;
            padding: 0;
        }

        .avatar {
            height: 24px;
            width: 24px;
            border-radius: 50%;
            margin-left: 4px;
        }

        .avatar div {
            background-size: cover;
            background-position: center center;
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }

        .username {
            font-size: 14px;
            font-weight: bold;
            line-height: 21px;
            color: {{#if drop.description_color}}{{drop.description_color}}{{else}}var(--laylo-text-primary){{/if}};
        }

        .username:hover {
            color: var(--laylo-text-secondary) !important;
        }

        /* RSVP Card Container - Perfect Width Alignment */
        .rsvp-card-container {
            margin-top: 32px;
            min-width: 400px;
            max-width: 400px;
            position: sticky;
            bottom: -184px;
        }

        /* Enhanced RSVP Card System */
        .laylo-rsvp-card {
            {{#if (eq drop.card_background_type "solid_white")}}
            background-color: #ffffff;
            color: #000000;
            {{else if (eq drop.card_background_type "solid_dark")}}
            background-color: #1a1a1a;
            color: #ffffff;
            {{else if (eq drop.card_background_type "translucent_light")}}
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            backdrop-filter: blur(10px);
            {{else if (eq drop.card_background_type "translucent_dark")}}
            background-color: rgba(0, 0, 0, 0.7);
            color: #ffffff;
            backdrop-filter: blur(10px);
            {{else}}
            background-color: var(--laylo-card-bg);
            color: #ffffff;
            {{/if}}
            transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            height: 100%;
        }

        /* Card Title - Adaptive Color */
        .laylo-card-title {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            {{#if (eq drop.card_background_type "solid_white")}}
            color: #000000;
            {{else}}
            color: #ffffff;
            {{/if}}
            margin: 0 0 12.5px 0;
            text-align: center;
        }

        /* Phone Input Container - Standardized Dimensions */
        .phone-input-container {
            display: flex;
            align-items: center;
            background-color: var(--laylo-input-bg);
            border-radius: 50px;
            padding: 12px 16px;
            margin-bottom: 16px;
            position: relative;
            height: 56px;
            width: 100%;
            box-sizing: border-box;
            transition: background-color 0.3s ease;
        }

        .phone-input-container:focus-within {
            background-color: var(--laylo-input-focus);
        }

        /* Phone input validation states */
        .phone-input-container {
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .phone-input-container.valid {
            border-color: #10B981;
            box-shadow: 0 0 0 1px #10B981;
        }

        .phone-input-container.invalid {
            border-color: #EF4444;
            box-shadow: 0 0 0 1px #EF4444;
        }

        /* Email Input Container - Identical to Phone Input Design */
        .email-input-container {
            display: flex;
            align-items: center;
            background-color: var(--laylo-input-bg);
            border-radius: 50px;
            padding: 12px 16px;
            margin-bottom: 16px;
            position: relative;
            height: 56px;
            width: 100%;
            box-sizing: border-box;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            /* Remove any unwanted glow or border artifacts */
            box-shadow: none;
            outline: none;
        }

        .email-input-container:focus-within {
            background-color: var(--laylo-input-focus);
            box-shadow: none;
        }

        .email-input-container.valid {
            border-color: #10B981;
            box-shadow: 0 0 0 1px #10B981;
        }

        .email-input-container.invalid {
            border-color: #EF4444;
            box-shadow: 0 0 0 1px #EF4444;
        }

        /* Email Input Field - Force Identical Styling to Phone Field */
        .email-input-field {
            flex: 1;
            width: 100%;
            background-color: transparent !important;
            background: transparent !important;
            border: none !important;
            outline: none !important;
            color: white !important;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1.125rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            height: 100%;
            box-sizing: border-box;
            /* Force remove any browser defaults */
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            box-shadow: none !important;
        }

        .email-input-field::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
            opacity: 1 !important;
        }

        .email-input-field:focus {
            outline: none !important;
            background-color: transparent !important;
            background: transparent !important;
            box-shadow: none !important;
        }

        .email-input-field:active {
            background-color: transparent !important;
            background: transparent !important;
        }

        /* Force email container to match phone container exactly */
        .email-input-container {
            background-color: var(--laylo-input-bg) !important;
            background: var(--laylo-input-bg) !important;
        }

        /* Solid White Card Text Visibility Fixes */
        {{#if (eq drop.card_background_type "solid_white")}}
        .email-input-field,
        .phone-input-field {
            color: #1F2937 !important;
        }

        .email-input-field::placeholder,
        .phone-input-field::placeholder {
            color: #6B7280 !important;
            opacity: 1 !important;
        }

        .email-input-container,
        .phone-input-container {
            background-color: rgba(0, 0, 0, 0.05) !important;
            background: rgba(0, 0, 0, 0.05) !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        .email-input-container:focus-within,
        .phone-input-container:focus-within {
            background-color: rgba(0, 0, 0, 0.08) !important;
            background: rgba(0, 0, 0, 0.08) !important;
            border-color: rgba(0, 0, 0, 0.2) !important;
        }

        .country-code {
            color: #1F2937 !important;
        }
        {{/if}}

        /* Functional Country Select */
        .phone-country-select {
            position: relative;
            align-self: stretch;
            display: flex;
            align-items: center;
            margin-right: 0.5em;
            flex-shrink: 0;
        }

        .phone-country-flag {
            width: auto;
            height: 18px;
            border-radius: 2px;
            overflow: visible;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            cursor: pointer;
            padding: 2px 4px;
            transition: background-color 0.2s ease;
        }

        .phone-country-flag:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .phone-country-flag img {
            width: 21px;
            height: 15px;
            object-fit: cover;
            border-radius: 2px;
        }

        .country-code {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 2px;
        }

        .dropdown-arrow {
            margin-left: 2px;
            transition: transform 0.2s ease;
        }

        .phone-country-select.open .dropdown-arrow {
            transform: rotate(180deg);
        }

        /* Country Dropdown - Optimized Sizing */
        .country-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 280px;
            width: max-content;
            max-width: 320px;
            background: rgba(0, 0, 0, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            max-height: 180px;
            overflow-y: auto;
            z-index: 1000;
            margin-top: 4px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .country-search {
            padding: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .country-search input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            padding: 6px 8px;
            color: white;
            font-size: 0.875rem;
            outline: none;
        }

        .country-search input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .country-list {
            max-height: 140px;
            overflow-y: auto;
            padding: 4px 0;
        }

        .country-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            white-space: nowrap;
            min-height: 36px;
        }

        .country-option:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .country-option img {
            width: 21px;
            height: 15px;
            object-fit: cover;
            border-radius: 2px;
        }

        .country-option span {
            color: white;
            font-size: 0.875rem;
        }

        /* Phone Input Field - Standardized */
        .phone-input-field {
            flex: 1;
            min-width: 0;
            background-color: transparent;
            border: none;
            outline: none;
            color: white;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1.125rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            padding: 0 90px 0 0;
            height: 100%;
            box-sizing: border-box;
        }

        .phone-input-field::placeholder {
            color: rgba(255, 255, 255, 0.7);
            opacity: 1;
        }

        .phone-input-field:focus {
            outline: none;
        }

        /* Form Stability Enhancements */
        .laylo-rsvp-card form {
            position: relative;
            width: 100%;
        }

        .phone-input-container * {
            box-sizing: border-box;
        }

        /* Prevent layout shifts */
        .laylo-message {
            min-height: 20px;
            transition: all 0.3s ease;
        }

        .laylo-message.hidden {
            min-height: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        /* RSVP Submit Button - Properly Inlaid */
        .laylo-rsvp-button {
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            background-color: {{#if drop.button_color}}{{drop.button_color}}{{else}}var(--laylo-button-red){{/if}} !important;
            background: {{#if drop.button_color}}{{drop.button_color}}{{else}}var(--laylo-button-red){{/if}} !important;
            /* Debug: Button color value = {{drop.button_color}} */
            outline: 0;
            border: 0;
            margin: 0;
            padding: 10px 20px;
            cursor: pointer;
            user-select: none;
            vertical-align: middle;
            appearance: none;
            text-decoration: none;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            font-size: 0.875rem;
            line-height: 1.2;
            letter-spacing: 0.02857em;
            text-transform: uppercase;
            min-width: 70px;
            height: 44px;
            border-radius: 50px;
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            color: {{#if drop.button_text_color}}{{drop.button_text_color}}{{else}}#ffffff{{/if}};
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 10;
        }

        .laylo-rsvp-button:hover {
            text-decoration: none;
            filter: brightness(0.9);
            transform: translateY(-50%) scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }

        .laylo-rsvp-button:active {
            transform: translateY(-50%) scale(0.98);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .laylo-rsvp-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: translateY(-50%);
            filter: none;
        }

        .laylo-rsvp-button:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }

        /* Standalone Button for Email-Only Mode */
        .laylo-rsvp-button.standalone {
            position: static;
            transform: none;
            width: 100%;
            margin-top: 16px;
            height: 56px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            min-width: auto;
            padding: 16px 24px;
            background-color: {{#if drop.button_color}}{{drop.button_color}}{{else}}var(--laylo-button-red){{/if}} !important;
            background: {{#if drop.button_color}}{{drop.button_color}}{{else}}var(--laylo-button-red){{/if}} !important;
            color: {{#if drop.button_text_color}}{{drop.button_text_color}}{{else}}#ffffff{{/if}} !important;
        }

        .laylo-rsvp-button.standalone:hover {
            transform: scale(1.02);
        }

        .laylo-rsvp-button.standalone:active {
            transform: scale(0.98);
        }

        .laylo-rsvp-button.standalone:disabled {
            transform: none;
        }

        /* Form Footer - Adaptive Color */
        .laylo-form-footer {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 0.75rem;
            line-height: 1.57;
            letter-spacing: 0.00714em;
            {{#if (eq drop.card_background_type "solid_white")}}
            color: #666666;
            {{else}}
            color: var(--laylo-text-secondary);
            {{/if}}
            margin: 0;
            text-align: left;
            margin-top: 12.5px;
        }

        .laylo-form-footer a {
            color: inherit;
            text-decoration: underline;
            text-decoration-color: currentcolor;
        }

        .laylo-form-footer a:hover {
            text-decoration: none;
        }

        /* Laylo Branding - Exact Style */
        .laylo-branding {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            justify-content: center;
            margin-top: 16px;
        }

        .laylo-branding-text {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            color: var(--laylo-text-secondary);
            margin: 0;
        }

        .laylo-branding-link {
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--laylo-text-secondary);
            transition: color 0.2s ease;
        }

        .laylo-branding-link:hover {
            color: #1795B0;
        }

        /* Success/Error Messages - Exact Laylo Style */
        .laylo-message {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            text-align: center;
            font-weight: 500;
            font-family: "Inter", sans-serif;
        }

        .laylo-success {
            background: #10B981;
            color: white;
        }

        .laylo-error {
            background: #EF4444;
            color: white;
        }

        /* Industry-Leading Responsive Design System */

        /* Large Desktop: 1920px+ */
        @media only screen and (min-width: 1920px) {
            .drop-page-container {
                padding: clamp(60px, 12vh, 120px) clamp(200px, 15vw, 300px) 24px;
            }

            .drop-container {
                max-width: 1600px;
            }
        }

        /* Desktop: 1440px - 1919px */
        @media only screen and (max-width: 1919px) and (min-width: 1440px) {
            .drop-page-container {
                padding: clamp(40px, 10vh, 100px) clamp(120px, 12vw, 200px) 24px;
            }
        }

        /* Medium Desktop: 1024px - 1439px */
        @media only screen and (max-width: 1439px) and (min-width: 1024px) {
            .drop-page-container {
                padding: clamp(32px, 8vh, 80px) clamp(80px, 10vw, 150px) 24px;
            }

            .drop-title {
                font-size: clamp(36px, 5vw, 48px);
                line-height: 1.1;
            }
        }

        /* Tablet: 768px - 1023px */
        @media only screen and (max-width: 1023px) and (min-width: 768px) {
            .drop-page-container {
                padding: clamp(24px, 6vh, 60px) clamp(40px, 8vw, 80px) 24px;
            }

            .drop-container {
                grid-template-columns: 1fr;
                gap: clamp(16px, 4vw, 32px);
                text-align: center;
            }

            .drop-image-container {
                grid-column: 1;
                max-width: min(500px, 80vw);
                margin: 0 auto;
            }

            .rsvp-container {
                grid-column: 1;
                max-width: min(400px, 90vw);
                margin: 0 auto;
                padding-bottom: 24px;
            }

            .drop-title {
                font-size: clamp(28px, 6vw, 36px);
                line-height: 1.2;
                text-align: center;
                max-width: 100%;
            }

            .user-container {
                justify-content: center;
                max-width: 100%;
            }
        }

        /* Mobile Large: 375px - 767px */
        @media only screen and (max-width: 767px) and (min-width: 375px) {
            .drop-page-container {
                padding: clamp(16px, 4vh, 40px) clamp(12px, 3vw, 20px) 60px;
            }

            .drop-container {
                grid-template-columns: 1fr;
                gap: clamp(16px, 4vw, 24px);
                text-align: center;
                max-width: min(400px, 95vw);
                margin: 0 auto;
            }

            .drop-image-container {
                grid-column: 1;
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
            }

            .rsvp-container {
                grid-column: 1;
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                padding-bottom: 16px;
            }

            .drop-title {
                font-size: clamp(24px, 7vw, 32px);
                line-height: 1.2;
                text-align: center;
                margin: 8px 0;
                max-width: 100%;
            }

            .user-container {
                justify-content: center;
                max-width: 100%;
            }

            .rsvp-card-container {
                margin-top: 16px;
                min-width: auto;
                max-width: 100%;
            }

            /* Mobile form optimizations */
            .phone-input-container {
                height: 52px;
                padding: 10px 14px;
            }

            .laylo-rsvp-button {
                height: 40px;
                padding: 8px 16px;
                font-size: 0.8rem;
                min-width: 60px;
            }

            .phone-input-field {
                padding: 0 75px 0 0;
                font-size: 0.9rem;
            }
        }

        /* Mobile Small: 320px - 374px */
        @media only screen and (max-width: 374px) {
            .drop-page-container {
                padding: 16px 8px 50px;
            }

            .drop-container {
                grid-template-columns: 1fr;
                gap: 16px;
                max-width: 100%;
                margin: 0 auto;
            }

            .drop-image-container {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
            }

            .rsvp-container {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                padding-bottom: 12px;
            }

            .drop-title {
                font-size: clamp(20px, 6vw, 28px);
                line-height: 1.3;
                text-align: center;
                margin: 6px 0;
                max-width: 100%;
            }

            .user-container {
                justify-content: center;
                max-width: 100%;
            }

            .rsvp-card-container {
                margin-top: 12px;
            }

            .laylo-rsvp-card {
                padding: 20px;
            }

            .phone-input-container {
                height: 48px;
                padding: 8px 12px;
            }

            .laylo-rsvp-button {
                height: 36px;
                padding: 6px 12px;
                font-size: 0.75rem;
                min-width: 55px;
            }

            .phone-input-field {
                padding: 0 65px 0 0;
                font-size: 0.85rem;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .visually-hidden {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        /* Removed duplicate background system to prevent layering issues */

        /* Ensure proper stacking and eliminate white background artifacts */
        .drop-container {
            position: relative;
            z-index: 1;
            background: transparent !important;
        }

        /* Force transparent backgrounds on all elements to prevent white artifacts */
        .drop-image-container,
        .rsvp-container,
        .rsvp-card-container,
        .user-container,
        .avatar,
        .avatar div {
            background: transparent !important;
        }

        /* Ensure body and html have no background */
        body::before,
        body::after,
        html::before,
        html::after {
            display: none !important;
        }
    </style>
</head>
<body style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
    <div class="drop-page-container">
        <div class="drop-container">
            <!-- Image Container - Exact Laylo Style -->
            <div class="drop-image-container">
                {{#if drop.cover_image}}
                <img src="{{drop.cover_image}}" alt="{{drop.title}}" />
                {{/if}}
            </div>

            <!-- RSVP Container - Exact Laylo Style -->
            <div class="rsvp-container">
                <!-- Title - Now shows the actual drop title -->
                <h1 class="drop-title">{{drop.title}}</h1>

                <!-- User Container - Now shows description/subtitle -->
                <div class="user-container">
                    <div class="avatar">
                        <div style="background-image: url('{{drop.cover_image}}'); background-size: cover; background-position: center center; width: 100%; height: 100%; border-radius: 50%;"></div>
                    </div>
                    <div class="username">{{#if drop.description}}{{drop.description}}{{else}}Get notified when this drops{{/if}}</div>
                </div>

                <!-- RSVP Card Container -->
                <div class="rsvp-card-container">
                    <div class="laylo-rsvp-card">
                        <!-- Success/Error Messages -->
                        <div id="success-message" class="laylo-message laylo-success hidden">
                            <p id="success-text">Thank you! You're on the list.</p>
                        </div>
                        <div id="error-message" class="laylo-message laylo-error hidden">
                            <p id="error-text">Something went wrong. Please try again.</p>
                        </div>

                        <!-- Card Title -->
                        <h3 class="laylo-card-title">Get notified</h3>

                        <!-- Dynamic Form Based on Collection Settings -->
                        <form id="drop-signup-form">
                            {{#if drop.collect_email}}
                            <!-- Email Input Field -->
                            <div class="email-input-container">
                                <input
                                    type="email"
                                    name="email"
                                    placeholder="<EMAIL>"
                                    class="email-input-field"
                                    value=""
                                    autocomplete="email"
                                    {{#if drop.collect_email}}required{{/if}}
                                />
                            </div>
                            {{/if}}

                            {{#if drop.collect_phone}}
                            <!-- Phone Input Container with Conditional Inlaid Button -->
                            <div class="phone-input-container">
                                <!-- Functional Country Select -->
                                <div class="phone-country-select" id="country-selector">
                                    <div class="phone-country-flag" id="selected-country">
                                        <img alt="United States" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjQjIyMjM0Ii8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMCAwSDIxVjFIMFYwWk0wIDJIMjFWM0gwVjJaTTAgNEgyMVY1SDBWNFpNMCA2SDIxVjdIMFY2Wk0wIDhIMjFWOUgwVjhaTTAgMTBIMjFWMTFIMFYxMFpNMCAxMkgyMVYxM0gwVjEyWiIgZmlsbD0id2hpdGUiLz4KPHJlY3Qgd2lkdGg9IjkiIGhlaWdodD0iOCIgZmlsbD0iIzNDM0I2RSIvPgo8L3N2Zz4K" />
                                        <span class="country-code">+1</span>
                                        <svg class="dropdown-arrow" width="12" height="8" viewBox="0 0 12 8" fill="none">
                                            <path d="M1 1L6 6L11 1" stroke="rgba(255,255,255,0.7)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>

                                    <!-- Country Dropdown -->
                                    <div class="country-dropdown hidden" id="country-dropdown">
                                        <div class="country-search">
                                            <input type="text" placeholder="Search countries..." id="country-search" />
                                        </div>
                                        <div class="country-list" id="country-list">
                                            <div class="country-option" data-code="+1" data-country="US">
                                                <img alt="United States" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjQjIyMjM0Ii8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMCAwSDIxVjFIMFYwWk0wIDJIMjFWM0gwVjJaTTAgNEgyMVY1SDBWNFpNMCA2SDIxVjdIMFY2Wk0wIDhIMjFWOUgwVjhaTTAgMTBIMjFWMTFIMFYxMFpNMCAxMkgyMVYxM0gwVjEyWiIgZmlsbD0id2hpdGUiLz4KPHJlY3Qgd2lkdGg9IjkiIGhlaWdodD0iOCIgZmlsbD0iIzNDM0I2RSIvPgo8L3N2Zz4K" />
                                                <span>United States (+1)</span>
                                            </div>
                                            <div class="country-option" data-code="+44" data-country="GB">
                                                <img alt="United Kingdom" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+CjxwYXRoIGQ9Ik0wIDBoMjF2MTVIMHoiIGZpbGw9IiMwMTIxNjkiLz4KPHBhdGggZD0iTTAgMGgyMXYxNUgweiIgZmlsbD0iIzAxMjE2OSIvPgo8L3N2Zz4K" />
                                                <span>United Kingdom (+44)</span>
                                            </div>
                                            <div class="country-option" data-code="+1" data-country="CA">
                                                <img alt="Canada" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjRkYwMDAwIi8+CjxwYXRoIGQ9Ik03IDBoN3YxNUg3eiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K" />
                                                <span>Canada (+1)</span>
                                            </div>
                                            <div class="country-option" data-code="+61" data-country="AU">
                                                <img alt="Australia" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+Cjwvc3ZnPgo=" />
                                                <span>Australia (+61)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Industry-Standard Phone Input Field -->
                                <input
                                    type="tel"
                                    name="phone"
                                    placeholder="(*************"
                                    class="phone-input-field"
                                    value=""
                                    maxlength="14"
                                    autocomplete="tel"
                                    {{#if drop.collect_phone}}required{{/if}}
                                />

                                {{#if (and drop.collect_phone (not drop.collect_email))}}
                                <!-- Inlaid Button for Phone-Only Mode -->
                                <button type="submit" class="laylo-rsvp-button inlaid" name="rsvp" alt="Confirm RSVP" title="Confirm RSVP">
                                    {{#if drop.button_text}}{{drop.button_text}}{{else}}RSVP{{/if}}
                                </button>
                                {{else if (and drop.collect_phone drop.collect_email)}}
                                <!-- Inlaid Button for Both Fields Mode -->
                                <button type="submit" class="laylo-rsvp-button inlaid" name="rsvp" alt="Confirm RSVP" title="Confirm RSVP">
                                    {{#if drop.button_text}}{{drop.button_text}}{{else}}RSVP{{/if}}
                                </button>
                                {{/if}}
                            </div>
                            {{/if}}

                            {{#if (and drop.collect_email (not drop.collect_phone))}}
                            <!-- Standalone Button for Email-Only Mode -->
                            <button type="submit" class="laylo-rsvp-button standalone" name="rsvp" alt="Confirm RSVP" title="Confirm RSVP">
                                {{#if drop.button_text}}{{drop.button_text}}{{else}}RSVP{{/if}}
                            </button>
                            {{/if}}
                        </form>

                        <!-- Form Footer -->
                        <div class="laylo-form-footer">
                            This site is protected by reCaptcha. By submitting my information, I agree to receive recurring automated messages to the contact information provided and to Bounce2Bounce's <a href="https://terms.bounce2bounce.com" target="_blank">Terms of Service</a>, <a href="https://terms.bounce2bounce.com" target="_blank">Cookie Policy</a> and <a href="https://terms.bounce2bounce.com" target="_blank">Privacy Policy</a>. Msg & Data Rates may apply. Reply STOP to cancel, HELP for help.
                        </div>

                        <!-- Laylo Branding -->
                        <div class="laylo-branding">
                            <div class="laylo-branding-link">
                                <svg width="160" height="49" viewBox="0 0 3784.81 1161.8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M162.87,658.37c39.48.14,79.1-.14,118.58,0,0,41.61-9.47,60.49-25.25,102.1,51.78-27.66,101.5-41.4,153.15-41.28,58.13.14,100.23,16.36,121.11,48.56,21.23,32.22,14.3,80.19-17.8,143.99-15.9,32.14-32.01,60.31-47.89,84.53-15.94,24.23-30.49,43.83-45.08,58.79-14.6,14.98-30.41,27.13-48.3,36.46-17.88,9.34-35.63,15.74-53.86,19.21-18.22,3.46-38.83,5.23-61.93,5.31-58.86.21-105.02-14.84-131.25-45.23-2.88,14.1-4.83,28.19-5.64,42.29-39.57.13-79.15.27-118.72.4,9.13-159.43,162.87-295.71,162.87-455.14ZM189.97,891.75c-8.96,17.92-17.84,35.84-26.21,53.77-.05,20.4,7.37,33.89,21.28,40.43,13.91,6.55,37.39,9.79,70.26,9.74,58.42-.1,97.28-27.06,123.88-80.9,4.45-9.05,7.94-17.41,10.44-25.11,2.49-7.69,4.03-14.14,4.6-19.34.56-5.19-.04-9.95-1.79-14.25-1.77-4.3-3.31-7.69-4.63-10.19-1.32-2.48-4.25-4.64-8.79-6.46-4.54-1.8-7.83-3.06-9.87-3.74-2.04-.68-5.83-1.25-11.37-1.71-5.54-.45-8.73-.69-9.58-.69-.85,0-3.8,0-8.89,0-69.37-.06-118.88,19.42-149.34,58.45ZM830.6,718.26c25.03.06,47.13,1.58,66.25,4.55,19.11,2.97,37.38,8.86,54.44,17.65,17.04,8.8,29.51,20.39,36.89,34.77,7.4,14.39,9.66,33.58,6.23,57.58-3.35,24.01-13.75,52.14-29.64,84.44-18.11,36.79-36.88,68.18-56.12,94.21-19.34,26.04-39.2,46.61-61.72,61.7-22.52,15.09-46.24,25.95-72.52,32.55-26.26,6.59-56.67,9.97-91.61,10.08-35.58.13-66.21-3.15-91.45-9.82-25.23-6.68-46.55-17.52-62.63-32.59-16.09-15.05-25.16-35.54-25.14-61.48-.09-25.93,9.06-57.4,27.28-94.42,15.53-31.59,33.61-59.35,52.71-83.27,19.17-23.92,37.68-43.07,56.09-57.49,18.42-14.41,38.22-26.11,59.86-35.09,21.63-8.98,42.66-15.13,63.43-18.46,20.77-3.34,43.3-4.97,67.66-4.91ZM708.51,1005.11c29.85-.06,55-6.73,76.18-20.02,21.17-13.28,38.88-35.87,54.58-67.77,8.6-17.53,13.1-32.45,13.18-44.82.09-12.36-3.46-21.46-10.58-27.31-7.13-5.84-14.94-10.01-23.4-12.5-8.46-2.48-18.11-3.73-28.94-3.74-7.79-.01-15.95.78-24.49,2.35-8.55,1.57-17.84,4.38-27.94,8.42-10.09,4.05-19.66,9.22-28.7,15.52-9.05,6.31-18.06,14.52-27.03,24.66-8.97,10.14-16.63,21.52-22.84,34.13-15.35,31.09-18.21,54.06-9.6,68.9,8.61,14.84,28.78,22.22,59.59,22.17ZM1384.85,720.64c39.48.09,78.97.19,118.45.28-36.1,129.34-141.44,258.68-148.84,388.02-39.57.13-79.15.27-118.72.4,1.14-19.88,4.59-39.76,9.74-59.65-39.81,39.39-82.51,59.22-139.6,59.41-45.8.16-81.55-6.33-105.82-19.48-24.19-13.14-39.48-37.43-39.76-72.86-.4-25.56,7-57.19,22.41-94.9,14.29-37.7,46.1-94.71,74.58-171.03,3.84-10.32,7.33-20.65,10.38-30.97,39.32.09,78.63.19,117.95.28-11.93,42.75-31.39,85.49-52.4,128.24-.22.45-.55,1.12-.99,2.01-5.52,11.19-9.71,19.7-12.44,25.51-2.74,5.82-6.23,14.1-10.4,24.84-4.17,10.74-6.67,19.13-7.57,25.17-.9,6.05-1.11,12.98-.52,20.81.57,7.83,2.98,13.75,7.12,17.76,4.12,4.02,10.16,7.6,18.13,10.71,7.94,3.12,17.85,4.67,29.67,4.65,41.62-.06,87.44-20.45,141.42-61.08,32.3-66.05,68.76-132.1,87.2-198.15ZM1887.14,800.5c-2.4,23.99-12.64,54.74-29.62,92.24-15.21,37.52-51.23,98.88-67.84,184.11-1.73,10.21-2.93,20.42-3.51,30.63-39.57.13-79.15.27-118.72.4,2.42-42.35,15.4-84.69,32.99-127.04.55-1.33,1.39-3.33,2.52-5.99,3.59-8.42,6.53-14.96,8.64-19.62,2.11-4.65,4.96-11.41,8.68-20.29,3.71-8.86,6.45-15.84,8.11-20.95,1.65-5.09,3.48-11.53,5.5-19.29,2-7.76,2.92-13.97,2.7-18.62-.21-4.66-.95-9.65-2.2-14.97-1.26-5.32-3.68-9.53-7.25-12.65-3.57-3.11-8.43-5.66-14.59-7.67-6.16-1.99-13.49-3-21.98-3.01-21.73-.02-41.11,4.09-58.22,12.29-17.11,8.22-35.08,19.89-53.85,34.99-37.28,74.46-74.07,148.92-78.34,223.38-39.57.13-79.14.27-118.72.4,7.4-129.29,112.7-258.58,148.78-387.86,39.48.09,78.97.19,118.45.28-4.98,17.83-11.27,35.66-18.45,53.5,14.97-10.23,26.09-17.68,33.7-22.35,7.6-4.67,17.83-10.22,30.57-16.66,12.73-6.43,25.64-10.97,38.95-13.61,13.29-2.64,28.06-3.94,44.34-3.9,15.94.04,29.77.97,41.49,2.77,11.71,1.82,23.52,5.4,35.3,10.76,11.76,5.36,20.78,13.84,26.57,25.4,5.77,11.57,7.92,26.01,6.01,43.34ZM2161.64,721.47c31.79.07,59.61,3.02,83.28,8.8,23.65,5.79,42.14,13.43,55.19,22.92,13.04,9.5,22.48,20.63,28.11,33.4,5.62,12.77,7.56,25.86,5.96,39.28-1.61,13.42-6.02,27.16-13.04,41.23-43.45,7.68-86.94,15.38-130.42,23.1.66-1.32,1.11-3.08,1.37-5.29.25-2.2-.21-6.16-1.41-11.89-1.2-5.72-3.75-10.79-7.66-15.21-3.91-4.4-11.13-8.48-21.65-12.24-10.53-3.75-23.75-5.63-39.66-5.65-15.92-.01-30.6,1.63-44.06,4.94-13.47,3.3-24.54,7.39-33.2,12.24-8.68,4.87-16.69,10.92-24.06,18.21-7.38,7.29-13.07,13.92-17.06,19.87-3.99,5.96-7.97,12.92-11.87,20.87-3.04,6.19-5.43,11.82-7.17,16.89-1.75,5.08-3.11,10.83-4.02,17.22-.91,6.4-.21,12.04,2.08,16.88,2.27,4.86,5.77,9.38,10.47,13.55,4.69,4.2,11.88,7.38,21.51,9.56,9.62,2.2,21.23,3.27,34.79,3.25,12.2-.02,23.63-.92,34.34-2.7,10.69-1.77,19.75-4.1,27.2-6.98,7.44-2.87,14.36-6.07,20.78-9.61,6.41-3.54,11.67-7.05,15.77-10.59,4.1-3.52,7.57-6.72,10.4-9.59,2.83-2.85,4.82-5.17,5.98-6.94.58-.88,1.16-1.76,1.74-2.64,36.01,7.01,72.12,14.01,108.33,20.99-6.57,14.95-14.4,29.14-23.47,42.55-9.08,13.43-20.64,26.52-34.62,39.3-13.99,12.79-29.52,23.82-47.04,33.11-17.53,9.29-38.98,16.74-64.82,22.33-25.81,5.59-54.7,8.45-86.92,8.56-31.52.1-59.34-3.1-83.08-9.65-23.72-6.55-42.62-15.43-56.07-26.66-13.45-11.23-23.07-25.23-27.99-42.02-4.94-16.78-5.46-34.57-1.67-53.38,3.77-18.8,11.43-39.7,22.72-62.71,30.95-61.95,71.82-110.07,118.14-144.28,46.64-34.21,102.22-51.21,172.78-51.03Z" fill="#687178"/>
                                </svg>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Form Handling -->
    <script>
        // Production-ready drop landing page

        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Log drop configuration
            console.log('🔍 Drop Configuration:', {
                collect_phone: {{#if drop.collect_phone}}true{{else}}false{{/if}},
                collect_email: {{#if drop.collect_email}}true{{else}}false{{/if}},
                button_color: '{{drop.button_color}}',
                button_text: '{{drop.button_text}}'
            });

            const form = document.getElementById('drop-signup-form');
            const phoneInput = document.querySelector('input[name="phone"]');
            const emailInput = document.querySelector('input[name="email"]');
            const submitButton = form.querySelector('button[type="submit"]');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');

            // Enhanced Form submission handler for dynamic fields
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(form);
                const phone = formData.get('phone');
                const email = formData.get('email');

                // Hide previous messages
                successMessage.classList.add('hidden');
                errorMessage.classList.add('hidden');

                // Disable button and show loading state
                submitButton.disabled = true;
                const originalText = submitButton.textContent;
                submitButton.textContent = 'RSVP...';

                // Prepare data to send based on what fields are collected
                const dataToSend = {};
                if (phone && phone.trim()) dataToSend.phone = phone;
                if (email && email.trim()) dataToSend.email = email;

                console.log('📤 Submitting form data:', dataToSend);
                console.log('📍 Endpoint:', '/drop/signup/{{drop.slug}}');

                try {
                    const response = await fetch('/drop/signup/{{drop.slug}}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(dataToSend)
                    });

                    console.log('📥 Response status:', response.status);
                    console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

                    const result = await response.json();
                    console.log('📥 Response data:', result);

                    if (response.ok) {
                        successMessage.classList.remove('hidden');
                        document.getElementById('success-text').textContent = 'Thank you! You\'re on the list.';
                        // Don't reset form to keep the data visible
                    } else {
                        console.error('❌ Server error:', result);
                        errorMessage.classList.remove('hidden');
                        document.getElementById('error-text').textContent = result.message || `Server error: ${response.status}`;
                    }
                } catch (error) {
                    console.error('❌ Form submission error:', error);
                    errorMessage.classList.remove('hidden');
                    document.getElementById('error-text').textContent = `Network error: ${error.message}`;
                } finally {
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                }
            });

            // Industry-Standard Phone Input with Real-time Formatting
            if (phoneInput) {
                // Phone number formatting function
                function formatPhoneNumber(value) {
                    // Remove all non-numeric characters
                    const phoneNumber = value.replace(/[^\d]/g, '');

                    // Don't format if empty
                    if (phoneNumber.length === 0) return '';

                    // Format based on length
                    if (phoneNumber.length <= 3) {
                        return `(${phoneNumber}`;
                    } else if (phoneNumber.length <= 6) {
                        return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
                    } else {
                        return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
                    }
                }

                // Validate phone number
                function isValidPhoneNumber(value) {
                    const phoneNumber = value.replace(/[^\d]/g, '');
                    return phoneNumber.length === 10;
                }

                // Add visual feedback for validation
                function updateValidationState(isValid) {
                    const container = phoneInput.closest('.phone-input-container');
                    if (isValid) {
                        container.style.borderColor = '#10B981';
                        container.style.boxShadow = '0 0 0 1px #10B981';
                    } else if (phoneInput.value.length > 0) {
                        container.style.borderColor = '#EF4444';
                        container.style.boxShadow = '0 0 0 1px #EF4444';
                    } else {
                        container.style.borderColor = '';
                        container.style.boxShadow = '';
                    }
                }

                // Real-time formatting on input
                phoneInput.addEventListener('input', function(e) {
                    const cursorPosition = e.target.selectionStart;
                    const oldValue = e.target.value;
                    const newValue = formatPhoneNumber(e.target.value);

                    e.target.value = newValue;

                    // Maintain cursor position
                    const newCursorPosition = cursorPosition + (newValue.length - oldValue.length);
                    e.target.setSelectionRange(newCursorPosition, newCursorPosition);

                    // Update validation state
                    updateValidationState(isValidPhoneNumber(newValue));
                });

                // Focus styling
                phoneInput.addEventListener('focus', function(e) {
                    e.target.closest('.phone-input-container').style.backgroundColor = 'var(--laylo-input-focus)';
                });

                // Blur styling and validation
                phoneInput.addEventListener('blur', function(e) {
                    e.target.closest('.phone-input-container').style.backgroundColor = 'var(--laylo-input-bg)';
                    updateValidationState(isValidPhoneNumber(e.target.value));
                });

                // Prevent invalid characters
                phoneInput.addEventListener('keydown', function(e) {
                    // Allow: backspace, delete, tab, escape, enter
                    if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                        (e.keyCode === 65 && e.ctrlKey === true) ||
                        (e.keyCode === 67 && e.ctrlKey === true) ||
                        (e.keyCode === 86 && e.ctrlKey === true) ||
                        (e.keyCode === 88 && e.ctrlKey === true)) {
                        return;
                    }
                    // Ensure that it is a number and stop the keypress
                    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                        e.preventDefault();
                    }
                });

                // Form validation enhancement
                const form = phoneInput.closest('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        if (!isValidPhoneNumber(phoneInput.value)) {
                            e.preventDefault();
                            updateValidationState(false);
                            phoneInput.focus();

                            // Show error message
                            const errorMessage = document.getElementById('error-message');
                            const errorText = document.getElementById('error-text');
                            if (errorMessage && errorText) {
                                errorText.textContent = 'Please enter a valid 10-digit phone number.';
                                errorMessage.classList.remove('hidden');
                            }
                            return false;
                        }
                    });
                }
            }

            // Email Input Validation
            if (emailInput) {
                // Email validation function
                function isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                }

                // Add visual feedback for email validation
                function updateEmailValidationState(isValid) {
                    const container = emailInput.closest('.email-input-container');
                    if (isValid) {
                        container.style.borderColor = '#10B981';
                        container.style.boxShadow = '0 0 0 1px #10B981';
                        container.classList.add('valid');
                        container.classList.remove('invalid');
                    } else if (emailInput.value.length > 0) {
                        container.style.borderColor = '#EF4444';
                        container.style.boxShadow = '0 0 0 1px #EF4444';
                        container.classList.add('invalid');
                        container.classList.remove('valid');
                    } else {
                        container.style.borderColor = '';
                        container.style.boxShadow = '';
                        container.classList.remove('valid', 'invalid');
                    }
                }

                // Real-time email validation
                emailInput.addEventListener('input', function(e) {
                    updateEmailValidationState(isValidEmail(e.target.value));
                });

                // Focus styling
                emailInput.addEventListener('focus', function(e) {
                    e.target.closest('.email-input-container').style.backgroundColor = 'var(--laylo-input-focus)';
                });

                // Blur styling and validation
                emailInput.addEventListener('blur', function(e) {
                    e.target.closest('.email-input-container').style.backgroundColor = 'var(--laylo-input-bg)';
                    updateEmailValidationState(isValidEmail(e.target.value));
                });

                // Form validation enhancement for email
                form.addEventListener('submit', function(e) {
                    if (emailInput.hasAttribute('required') && !isValidEmail(emailInput.value)) {
                        e.preventDefault();
                        updateEmailValidationState(false);
                        emailInput.focus();

                        // Show error message
                        const errorMessage = document.getElementById('error-message');
                        const errorText = document.getElementById('error-text');
                        if (errorMessage && errorText) {
                            errorText.textContent = 'Please enter a valid email address.';
                            errorMessage.classList.remove('hidden');
                        }
                        return false;
                    }
                });
            }

            // Functional Country Selector
            const countrySelector = document.getElementById('country-selector');
            const selectedCountry = document.getElementById('selected-country');
            const countryDropdown = document.getElementById('country-dropdown');
            const countrySearch = document.getElementById('country-search');
            const countryList = document.getElementById('country-list');

            if (countrySelector && selectedCountry && countryDropdown) {
                let currentCountryCode = '+1';

                // Toggle dropdown
                selectedCountry.addEventListener('click', function(e) {
                    e.stopPropagation();
                    countryDropdown.classList.toggle('hidden');
                    countrySelector.classList.toggle('open');

                    if (!countryDropdown.classList.contains('hidden')) {
                        countrySearch.focus();
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!countrySelector.contains(e.target)) {
                        countryDropdown.classList.add('hidden');
                        countrySelector.classList.remove('open');
                    }
                });

                // Country search functionality
                if (countrySearch) {
                    countrySearch.addEventListener('input', function(e) {
                        const searchTerm = e.target.value.toLowerCase();
                        const countryOptions = countryList.querySelectorAll('.country-option');

                        countryOptions.forEach(option => {
                            const countryName = option.textContent.toLowerCase();
                            if (countryName.includes(searchTerm)) {
                                option.style.display = 'flex';
                            } else {
                                option.style.display = 'none';
                            }
                        });
                    });
                }

                // Country selection
                const countryOptions = countryList.querySelectorAll('.country-option');
                countryOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const code = this.dataset.code;
                        const country = this.dataset.country;
                        const img = this.querySelector('img').cloneNode(true);

                        // Update selected country display
                        const selectedFlag = selectedCountry.querySelector('img');
                        const selectedCode = selectedCountry.querySelector('.country-code');

                        selectedFlag.src = img.src;
                        selectedFlag.alt = img.alt;
                        selectedCode.textContent = code;

                        currentCountryCode = code;

                        // Close dropdown
                        countryDropdown.classList.add('hidden');
                        countrySelector.classList.remove('open');

                        // Clear search
                        countrySearch.value = '';
                        countryOptions.forEach(opt => opt.style.display = 'flex');

                        // Update phone number formatting based on country
                        if (phoneInput) {
                            phoneInput.focus();
                        }
                    });
                });
            }

            // Laylo branding link
            const layloLink = document.querySelector('.laylo-branding-link');
            if (layloLink) {
                layloLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.open('https://laylo.com', '_blank');
                });
            }
        });
    </script>
</body>
</html>
