<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{drop.title}} - BOUNCE2BOUNCE</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{drop.description}}">
    <meta name="keywords" content="music, artist, drop, signup, {{drop.title}}">
    <meta name="author" content="BOUNCE2BOUNCE">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta property="og:description" content="{{drop.description}}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://{{default_domain}}/drop/{{drop.slug}}">
    {{#if drop.cover_image}}
    <meta property="og:image" content="{{drop.cover_image}}">
    {{/if}}
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta name="twitter:description" content="{{drop.description}}">
    {{#if drop.cover_image}}
    <meta name="twitter:image" content="{{drop.cover_image}}">
    {{/if}}

    <!-- HTML GlobalBank Template Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    
    <!-- HTML GlobalBank Template CSS -->
    <link rel="stylesheet" href="/css/globalbank-authentic.css"/>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon.ico"/>

    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Laylo-Inspired Design CSS -->
    <style>
        :root {
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{drop.button_text_color}};
            --drop-background-color: {{drop.background_color}};

            /* Laylo Design System - Exact Colors */
            --laylo-red: #ff0003;
            --laylo-dark: #1F2937;
            --laylo-card-bg: #1a1a1a;
            --laylo-text-primary: #FFFFFF;
            --laylo-text-secondary: #687178;
            --laylo-border-radius: 24px;
            --laylo-button-red: #ff0003;
            --laylo-input-bg: #374151;
            --laylo-input-focus: #4B5563;
        }

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
        }

        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            min-height: 100vh;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            overscroll-behavior: none;
        }

        /* Main Container - Exact Laylo Layout */
        .drop-page-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 12vh 185.5px 24px;
            min-height: 100vh;
            background: linear-gradient(179deg, var(--laylo-red), rgba(0, 0, 0, 0.85)) 50% center, rgb(0, 0, 0);
        }

        .drop-container {
            display: flex;
            flex-direction: row;
            height: 100%;
            gap: 48px;
            max-width: 100%;
            flex: 1;
        }

        /* Image Container - Exact Laylo Style */
        .drop-image-container {
            position: sticky;
            top: 12%;
            max-width: 58.3333%;
            max-height: 80vh;
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            min-width: 690px;
            min-height: min(40vh, 364px);
            height: auto;
        }

        .drop-image-container img {
            width: 100%;
            max-width: 690px;
            height: auto;
            max-height: 100%;
            border-radius: 12px;
            overflow: hidden;
            object-fit: cover;
            opacity: 0;
            animation: bloom 0.3s cubic-bezier(0.2, 0.2, 0.25, 1) normal forwards;
            animation-delay: 0.15s;
        }

        @keyframes bloom {
            0% {
                opacity: 0;
                filter: blur(2rem);
                transform: perspective(10rem) translateZ(-2rem);
            }
            100% {
                opacity: 1;
                filter: blur(0);
                transform: perspective(10rem) translateZ(0);
            }
        }

        /* RSVP Container - Exact Laylo Style */
        .rsvp-container {
            min-width: 398px;
            max-width: 398px;
            padding-bottom: 48px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: 48px;
        }

        /* Title - Exact Laylo Style */
        .drop-title {
            font-size: 64px;
            line-height: 64px;
            letter-spacing: 0px;
            text-align: left;
            margin: 8px 0;
            font-weight: 800;
            text-size-adjust: 100%;
            text-wrap: wrap;
            white-space-collapse: preserve-breaks;
            color: var(--laylo-text-primary);
        }

        /* User Container - Exact Laylo Style */
        .user-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .avatar {
            height: 24px;
            width: 24px;
            border-radius: 50%;
            margin-left: 4px;
        }

        .avatar div {
            background-size: cover;
            background-position: center center;
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }

        .username {
            font-size: 14px;
            font-weight: bold;
            line-height: 21px;
            color: var(--laylo-text-primary);
        }

        .username:hover {
            color: var(--laylo-text-secondary) !important;
        }

        /* RSVP Card Container - Exact Laylo Style */
        .rsvp-card-container {
            margin-top: 32px;
            min-width: 349px;
            max-width: 349px;
            position: sticky;
            bottom: -184px;
        }

        /* MUI Paper Card - Exact Laylo Style */
        .laylo-rsvp-card {
            background-color: var(--laylo-card-bg);
            color: #fff;
            transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            border-radius: 24px;
            box-shadow: none;
            background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05));
            padding: 24px;
            height: 100%;
        }

        /* Card Title */
        .laylo-card-title {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            color: rgb(255, 255, 255);
            margin: 0 0 12.5px 0;
            text-align: center;
        }

        /* Phone Input Container - Exact Laylo Style */
        .phone-input-container {
            display: flex;
            align-items: center;
            background-color: var(--laylo-input-bg);
            border-radius: 50px;
            padding: 8.5px 14px;
            margin-bottom: 16px;
            position: relative;
        }

        /* Country Select */
        .phone-country-select {
            position: relative;
            align-self: stretch;
            display: flex;
            align-items: center;
            margin-right: 0.35em;
        }

        .phone-country-flag {
            width: calc(1em * 1.5);
            height: 1em;
            background-color: rgba(0,0,0,.1);
            box-shadow: 0 0 0 1px rgba(0,0,0,.5), inset 0 0 0 1px rgba(0,0,0,.5);
            border-radius: 2px;
        }

        .phone-country-flag img {
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 2px;
        }

        /* Phone Input Field */
        .phone-input-field {
            flex: 1;
            min-width: 0;
            background-color: transparent;
            border: none;
            outline: none;
            color: white;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.4375em;
            letter-spacing: 0.00938em;
            padding: 0 100px 0 0;
        }

        .phone-input-field::placeholder {
            color: rgba(255, 255, 255, 0.7);
            opacity: 1;
        }

        .phone-input-field:focus {
            outline: none;
        }

        /* RSVP Submit Button - Inlaid Style */
        .laylo-rsvp-button {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            background-color: var(--laylo-button-red);
            outline: 0;
            border: 0;
            margin: 0;
            padding: 8px 16px;
            cursor: pointer;
            user-select: none;
            vertical-align: middle;
            appearance: none;
            text-decoration: none;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            font-size: 0.875rem;
            line-height: 1.75;
            letter-spacing: 0.02857em;
            text-transform: uppercase;
            min-width: 64px;
            border-radius: 50px;
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            color: #fff;
            box-shadow: none;
            z-index: 10;
        }

        .laylo-rsvp-button:hover {
            text-decoration: none;
            background-color: #d32f2f;
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
        }

        .laylo-rsvp-button:active {
            transform: translateY(-50%) scale(0.95);
            box-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12);
        }

        .laylo-rsvp-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        /* Form Footer - Exact Laylo Style */
        .laylo-form-footer {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 0.875rem;
            line-height: 1.57;
            letter-spacing: 0.00714em;
            color: var(--laylo-text-secondary);
            margin: 0;
            text-align: left;
            margin-top: 12.5px;
        }

        .laylo-form-footer a {
            color: inherit;
            text-decoration: underline;
            text-decoration-color: currentcolor;
        }

        .laylo-form-footer a:hover {
            text-decoration: none;
        }

        /* Laylo Branding - Exact Style */
        .laylo-branding {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            justify-content: center;
            margin-top: 16px;
        }

        .laylo-branding-text {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
            letter-spacing: 0.00938em;
            color: var(--laylo-text-secondary);
            margin: 0;
        }

        .laylo-branding-link {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: var(--laylo-text-secondary);
            transition: color 0.2s ease;
        }

        .laylo-branding-link:hover {
            color: #1795B0;
        }

        .laylo-branding-arrow {
            margin-left: 4px;
        }

        /* Success/Error Messages - Exact Laylo Style */
        .laylo-message {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            text-align: center;
            font-weight: 500;
            font-family: "Inter", sans-serif;
        }

        .laylo-success {
            background: #10B981;
            color: white;
        }

        .laylo-error {
            background: #EF4444;
            color: white;
        }

        /* Responsive Design - Exact Laylo Breakpoints */
        @media only screen and (max-width: 1439px) {
            .drop-page-container {
                padding: 12vh 128px 24px;
            }

            .drop-title {
                font-size: 48px;
                line-height: 54px;
            }

            .rsvp-container {
                margin-right: 0;
            }
        }

        @media only screen and (max-width: 1199px) {
            .drop-page-container {
                padding: 8px 84px;
            }

            .drop-container {
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 16px;
            }

            .drop-image-container {
                width: 364px;
                position: relative;
                top: 0;
                min-width: auto;
            }

            .drop-title {
                font-size: 36px;
                line-height: 42px;
                margin: 0;
            }

            .rsvp-container {
                margin-top: 0;
                max-width: 364px;
                width: 100%;
                min-width: inherit;
                padding-bottom: 0;
            }

            .rsvp-card-container {
                max-width: 100%;
            }
        }

        @media only screen and (max-width: 639px) {
            .drop-page-container {
                padding: 16px 8px 100px;
            }

            .drop-title {
                padding: 0 16px;
            }

            .rsvp-container {
                min-width: 200px;
            }

            .rsvp-card-container {
                min-width: 200px;
                margin-top: 24px;
            }

            .user-container {
                padding: 0 16px;
            }
        }

        @media only screen and (max-width: 364px) {
            .drop-page-container {
                display: block;
            }

            .drop-image-container {
                width: 100%;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .visually-hidden {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }
    </style>
</head>
<body style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
    <div class="drop-page-container">
        <div class="drop-container">
            <!-- Image Container - Exact Laylo Style -->
            <div class="drop-image-container">
                {{#if drop.cover_image}}
                <img src="{{drop.cover_image}}" alt="{{drop.title}}" />
                {{/if}}
            </div>

            <!-- RSVP Container - Exact Laylo Style -->
            <div class="rsvp-container">
                <!-- Title -->
                <h1 class="drop-title">YOUR INVITE</h1>

                <!-- User Container -->
                <div class="user-container">
                    <div class="avatar">
                        <div style="background-image: url('{{drop.cover_image}}'); background-size: cover; background-position: center center; width: 100%; height: 100%; border-radius: 50%;"></div>
                    </div>
                    <div class="username">{{drop.title}}</div>
                </div>

                <!-- RSVP Card Container -->
                <div class="rsvp-card-container">
                    <div class="laylo-rsvp-card">
                        <!-- Success/Error Messages -->
                        <div id="success-message" class="laylo-message laylo-success hidden">
                            <p id="success-text">Thank you! You're on the list.</p>
                        </div>
                        <div id="error-message" class="laylo-message laylo-error hidden">
                            <p id="error-text">Something went wrong. Please try again.</p>
                        </div>

                        <!-- Card Title -->
                        <h3 class="laylo-card-title">Get notified</h3>

                        <!-- Form -->
                        <form id="drop-signup-form">
                            <!-- Phone Input Container with Inlaid Button -->
                            <div class="phone-input-container">
                                <!-- Country Select -->
                                <div class="phone-country-select">
                                    <div class="phone-country-flag">
                                        <img alt="United States" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMSAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIxIiBoZWlnaHQ9IjE1IiBmaWxsPSIjQjIyMjM0Ii8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMCAwSDIxVjFIMFYwWk0wIDJIMjFWM0gwVjJaTTAgNEgyMVY1SDBWNFpNMCA2SDIxVjdIMFY2Wk0wIDhIMjFWOUgwVjhaTTAgMTBIMjFWMTFIMFYxMFpNMCAxMkgyMVYxM0gwVjEyWiIgZmlsbD0id2hpdGUiLz4KPHJlY3Qgd2lkdGg9IjkiIGhlaWdodD0iOCIgZmlsbD0iIzNDM0I2RSIvPgo8L3N2Zz4K" />
                                    </div>
                                </div>

                                <!-- Phone Input Field -->
                                <input
                                    type="tel"
                                    name="phone"
                                    placeholder="Your number"
                                    class="phone-input-field"
                                    value="****** 801 7985"
                                    {{#if drop.collect_phone}}required{{/if}}
                                />

                                <!-- Inlaid RSVP Button -->
                                <button type="submit" class="laylo-rsvp-button" name="rsvp" alt="Confirm RSVP" title="Confirm RSVP">
                                    {{drop.button_text}}
                                </button>
                            </div>
                        </form>

                        <!-- Form Footer -->
                        <div class="laylo-form-footer">
                            This site is protected by reCaptcha. By submitting my information, I agree to receive recurring automated messages to the contact information provided and to <a href="https://laylo.com/terms" target="_blank">Laylo's Terms of Service</a>, <a href="https://laylo.com/cookies" target="_blank">Cookie Policy</a> and <a href="https://laylo.com/privacy" target="_blank">Privacy Policy</a>. Msg & Data Rates may apply. Reply STOP to cancel, HELP for help.
                        </div>

                        <!-- Laylo Branding -->
                        <div class="laylo-branding">
                            <div class="laylo-branding-link">
                                <svg width="45" height="13" viewBox="5 3 110 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M22.4994 35.0021C28.3365 35.0021 33.395 31.6682 35.8745 26.8006C38.2189 26.3869 39.9998 24.3397 39.9998 21.8766C39.9998 19.9366 38.895 18.2546 37.2802 17.4255C36.059 10.3678 29.906 5 22.4994 5C15.0925 5 8.93944 10.3681 7.71848 17.4261C6.10431 18.2553 5 19.937 5 21.8766C5 24.3393 6.78032 26.3862 9.12416 26.8004C11.6036 31.6681 16.6621 35.0021 22.4994 35.0021ZM10.0017 20.0014C10.0017 21.2156 10.1748 22.3893 10.4977 23.4993C13.8413 26.0122 17.9982 27.5014 22.5029 27.5014C27.0072 27.5014 31.1638 26.0125 34.5072 23.5C34.8302 22.3898 35.0034 21.2158 35.0034 20.0014C35.0034 13.0973 29.4066 7.50049 22.5026 7.50049C15.5985 7.50049 10.0017 13.0973 10.0017 20.0014ZM12.5086 18.7445C12.4227 19.4344 12.9945 20.0007 13.6897 20.0007C14.3849 20.0007 14.9374 19.4329 15.0519 18.7473C15.5809 15.5805 18.0805 13.0809 21.2472 12.552C21.9329 12.4374 22.5007 11.8849 22.5007 11.1897C22.5007 10.4945 21.9343 9.92274 21.2445 10.0086C16.6875 10.5757 13.0756 14.1876 12.5086 18.7445ZM44.9998 28.6138H58.6941V24.1321H50.5013V8.12605H44.9998V28.6138ZM64.6748 28.8539C66.6537 28.8539 68.1478 28.1737 69.068 26.453H69.1868V28.6138H74.2924V18.0898C74.2924 15.2488 71.6308 13.0479 67.287 13.0479C62.7453 13.0479 60.5091 15.4488 60.3211 18.2499H65.3476C65.4762 17.2995 66.1689 16.8494 67.2078 16.8494C68.1577 16.8494 68.8306 17.2895 68.8306 18.0898V18.1299C68.8306 19.0202 67.8609 19.3003 65.308 19.4904C62.1714 19.7205 59.7274 21.0009 59.7274 24.3722C59.7274 27.4334 61.7657 28.8539 64.6748 28.8539ZM66.4558 25.3726C65.5257 25.3726 64.8727 24.9124 64.8727 24.0521C64.8727 23.2618 65.4268 22.6516 66.6933 22.4515C67.564 22.3114 68.2962 22.1314 68.8701 21.8913V23.1718C68.8701 24.5723 67.6927 25.3726 66.4558 25.3726ZM85.5767 29.8943C84.7356 32.4353 82.9447 34.376 79.1253 34.376C77.8687 34.376 76.6912 34.176 75.8403 33.8358L77.0276 29.9143C78.3733 30.3945 79.4321 30.4245 79.7982 29.5742L79.9169 29.2941L74.5738 13.248H80.2731L82.727 23.8921H82.8853L85.3788 13.248H91.1178L85.5767 29.8943ZM97.5758 8.12605H92.114V28.6138H97.5758V8.12605ZM115 20.9709C115 25.6927 112.071 28.894 107.124 28.894C102.176 28.894 99.2474 25.6927 99.2474 20.9709C99.2474 16.2491 102.176 13.0479 107.124 13.0479C112.071 13.0479 115 16.2491 115 20.9709ZM104.828 20.9309C104.828 23.3418 105.699 24.8124 107.163 24.8124C108.548 24.8124 109.419 23.3418 109.419 20.9309C109.419 18.52 108.548 17.0494 107.163 17.0494C105.699 17.0494 104.828 18.52 104.828 20.9309Z" fill="#687178"/>
                                </svg>
                                <span class="laylo-branding-text">Make a Drop like this</span>
                                <svg class="laylo-branding-arrow" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#687178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                    <polyline points="12 5 19 12 12 19"></polyline>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>

    <!-- JavaScript for Form Handling -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('drop-signup-form');
            const phoneInput = document.querySelector('input[name="phone"]');
            const submitButton = form.querySelector('button[type="submit"]');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');

            // Form submission handler
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(form);
                const phone = formData.get('phone');

                // Hide previous messages
                successMessage.classList.add('hidden');
                errorMessage.classList.add('hidden');

                // Disable button and show loading state
                submitButton.disabled = true;
                const originalText = submitButton.textContent;
                submitButton.textContent = 'RSVP...';

                // Prepare data to send
                const dataToSend = {};
                if (phone) dataToSend.phone = phone;

                try {
                    const response = await fetch('/drop/signup/{{drop.slug}}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(dataToSend)
                    });

                    const result = await response.json();

                    if (response.ok) {
                        successMessage.classList.remove('hidden');
                        document.getElementById('success-text').textContent = 'Thank you! You\'re on the list.';
                        // Don't reset form to keep the phone number visible
                    } else {
                        errorMessage.classList.remove('hidden');
                        document.getElementById('error-text').textContent = result.message || 'Something went wrong. Please try again.';
                    }
                } catch (error) {
                    errorMessage.classList.remove('hidden');
                    document.getElementById('error-text').textContent = 'Network error. Please try again.';
                } finally {
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                }
            });

            // Phone number formatting (optional - can be removed if you want to keep the exact Laylo behavior)
            if (phoneInput) {
                phoneInput.addEventListener('focus', function(e) {
                    if (e.target.value === '****** 801 7985') {
                        e.target.value = '';
                        e.target.placeholder = '****** 801 7985';
                    }
                });

                phoneInput.addEventListener('blur', function(e) {
                    if (e.target.value === '') {
                        e.target.value = '****** 801 7985';
                        e.target.placeholder = 'Your number';
                    }
                });
            }

            // Laylo branding link
            const layloLink = document.querySelector('.laylo-branding-link');
            if (layloLink) {
                layloLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.open('https://laylo.com', '_blank');
                });
            }
        });
    </script>
</body>
</html>
