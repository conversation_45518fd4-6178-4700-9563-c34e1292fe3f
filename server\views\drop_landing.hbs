<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{drop.title}} - BOUNCE2BOUNCE</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{drop.description}}">
    <meta name="keywords" content="music, artist, drop, signup, {{drop.title}}">
    <meta name="author" content="BOUNCE2BOUNCE">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta property="og:description" content="{{drop.description}}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://{{default_domain}}/drop/{{drop.slug}}">
    {{#if drop.cover_image}}
    <meta property="og:image" content="{{drop.cover_image}}">
    {{/if}}
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{drop.title}} - BOUNCE2BOUNCE">
    <meta name="twitter:description" content="{{drop.description}}">
    {{#if drop.cover_image}}
    <meta name="twitter:image" content="{{drop.cover_image}}">
    {{/if}}

    <!-- HTML GlobalBank Template Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    
    <!-- HTML GlobalBank Template CSS -->
    <link rel="stylesheet" href="/css/globalbank-authentic.css"/>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon.ico"/>
    
    <!-- CSS Variables for Dynamic Customization -->
    <style>
        :root {
            --drop-title-color: {{drop.title_color}};
            --drop-description-color: {{drop.description_color}};
            --drop-button-color: {{drop.button_color}};
            --drop-button-text-color: {{drop.button_text_color}};
            --drop-background-color: {{drop.background_color}};
        }
    </style>
</head>
<body class="antialiased bg-body text-body font-body">
    <div>
        <!-- Hero Section - HTML GlobalBank Style -->
        <section class="relative overflow-hidden">
            <div class="relative pt-20 lg:pt-28">
                <div class="relative z-10 container px-4 mx-auto">
                    <div class="relative mb-24 text-center md:max-w-4xl mx-auto">
                        {{#if drop.cover_image}}
                        <div class="mb-10">
                            <img src="{{drop.cover_image}}" alt="{{drop.title}}" class="mx-auto rounded-5xl" style="max-width: 300px; height: auto;"/>
                        </div>
                        {{/if}}
                        
                        <span class="inline-block mb-2.5 text-sm text-green-400 font-medium tracking-tighter">{{drop.category}}</span>
                        <h1 class="font-heading mb-10 text-7xl lg:text-8xl xl:text-10xl text-white tracking-tighter">{{drop.title}}</h1>
                        
                        {{#if drop.description}}
                        <p class="mb-10 text-gray-300 md:max-w-md mx-auto">{{drop.description}}</p>
                        {{/if}}
                        
                        {{#if drop.signup_count}}
                        <div class="mb-10">
                            <span class="text-sm text-gray-300">{{drop.signup_count}} people have already signed up</span>
                        </div>
                        {{/if}}
                        
                        <!-- Signup Form -->
                        <form id="drop-signup-form" class="mb-10">
                            <div class="max-w-md mx-auto mb-6">
                                <input 
                                    type="email" 
                                    name="email" 
                                    placeholder="Enter your email address" 
                                    required 
                                    class="w-full px-6 py-4 text-white bg-transparent border-2 border-gray-800 rounded-full focus:border-green-400 focus:outline-none transition duration-300"
                                />
                            </div>
                            <button type="submit" class="inline-block px-8 py-4 tracking-tighter bg-green-400 hover:bg-green-500 text-black focus:ring-4 focus:ring-green-500 focus:ring-opacity-40 rounded-full transition duration-300">
                                {{drop.button_text}}
                            </button>
                        </form>
                        
                        <!-- Success/Error Messages -->
                        <div id="success-message" class="hidden mb-6 p-4 bg-green-400 text-black rounded-lg">
                            <p id="success-text">Thank you for signing up!</p>
                        </div>
                        <div id="error-message" class="hidden mb-6 p-4 bg-red-500 text-white rounded-lg">
                            <p id="error-text">Something went wrong. Please try again.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Platform Links Section -->
        {{#if (or drop.website_link drop.instagram_link drop.twitter_link drop.spotify_link drop.youtube_link drop.apple_music_url drop.soundcloud_url drop.tiktok_link)}}
        <section class="pt-20 pb-24 bg-blueGray-950">
            <div class="container px-4 mx-auto">
                <div class="text-center mb-20">
                    <span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Connect</span>
                    <h2 class="font-heading mb-6 text-7xl lg:text-8xl text-white tracking-8xl md:max-w-md mx-auto">Follow {{drop.title}}</h2>
                </div>
                <div class="flex flex-wrap justify-center -m-4">
                    {{#if drop.website_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.website_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.instagram_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.instagram_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.twitter_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.twitter_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.spotify_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.spotify_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 14.5c-.17.29-.48.46-.81.46-.16 0-.33-.04-.47-.13-1.44-.83-3.22-1.21-5.25-1.21-1.52 0-2.8.19-2.8.19-.55 0-1-.45-1-1s.45-1 1-1c0 0 1.54-.23 3.55-.23 2.29 0 4.29.48 5.94 1.44.49.28.66.89.38 1.38l.46.1zm1.14-2.8c-.2.33-.56.53-.94.53-.18 0-.36-.05-.53-.14-1.8-1.04-4.02-1.51-6.63-1.51-1.78 0-3.14.18-3.14.18-.64 0-1.16-.52-1.16-1.16s.52-1.16 1.16-1.16c0 0 1.54-.21 3.67-.21 2.74 0 5.3.6 7.44 1.68.58.29.82 1.02.53 1.6l.6.19zm1.07-3.14c-.22.37-.63.59-1.07.59-.2 0-.41-.05-.6-.16-2.23-1.21-5.25-1.79-8.73-1.79-2.078 0-3.66.21-3.66.21-.72 0-1.31-.59-1.31-1.31s.59-1.31 1.31-1.31c0 0 1.74-.24 4.19-.24 3.64 0 7.17.77 9.77 2.15.67.35.93 1.15.58 1.82l.52.04z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.youtube_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.youtube_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.apple_music_url}}
                    <div class="w-auto p-4">
                        <a href="{{drop.apple_music_url}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.soundcloud_url}}
                    <div class="w-auto p-4">
                        <a href="{{drop.soundcloud_url}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M1.175 12.225c-.051 0-.094.046-.101.1l-.233 2.154.233 2.105c.007.058.05.104.101.104.053 0 .094-.046.101-.104l.255-2.105-.255-2.154c-.007-.054-.048-.1-.101-.1zm1.655.096c-.058 0-.106.053-.113.115l-.138 1.958.138 1.887c.007.058.055.115.113.115.058 0 .102-.057.113-.115l.158-1.887-.158-1.958c-.011-.062-.055-.115-.113-.115zm1.565.019c-.062 0-.111.057-.125.126l-.1 1.939.1 1.852c.014.07.063.126.125.126.067 0 .111-.056.125-.126l.111-1.852-.111-1.939c-.014-.069-.058-.126-.125-.126zm1.558.021c-.071 0-.125.061-.134.138l-.071 1.918.071 1.821c.009.077.063.138.134.138.075 0 .125-.061.134-.138l.096-1.821-.096-1.918c-.009-.077-.059-.138-.134-.138zm1.567.015c-.078 0-.134.069-.146.15l-.067 1.923.067 1.798c.012.082.068.15.146.15.082 0 .134-.068.146-.15l.078-1.798-.078-1.923c-.012-.081-.064-.15-.146-.15zm1.543 0c-.089 0-.15.077-.15.168l-.054 1.905.054 1.77c0 .091.061.168.15.168.094 0 .15-.077.15-.168l.067-1.77-.067-1.905c0-.091-.056-.168-.15-.168zm1.558-.004c-.097 0-.167.08-.167.176l-.04 1.909.04 1.752c0 .096.07.176.167.176.102 0 .167-.08.167-.176l.051-1.752-.051-1.909c0-.096-.065-.176-.167-.176zm1.567 0c-.11 0-.184.089-.184.199l-.034 1.905.034 1.738c0 .11.074.199.184.199.115 0 .184-.089.184-.199l.04-1.738-.04-1.905c0-.11-.069-.199-.184-.199zm1.575-.011c-.115 0-.199.093-.199.207l-.029 1.916.029 1.728c0 .118.084.207.199.207.122 0 .199-.089.199-.207l.034-1.728-.034-1.916c0-.114-.077-.207-.199-.207zm1.563-.004c-.126 0-.211.097-.211.211l-.025 1.912.025 1.716c0 .118.085.211.211.211.131 0 .211-.093.211-.211l.029-1.716-.029-1.912c0-.114-.08-.211-.211-.211zm1.567-.007c-.138 0-.218.103-.218.218l-.022 1.905.022 1.712c0 .115.08.218.218.218.143 0 .218-.103.218-.218l.025-1.712-.025-1.905c0-.115-.075-.218-.218-.218zm1.575-.003c-.15 0-.243.111-.243.243l-.018 1.902.018 1.699c0 .132.093.243.243.243.155 0 .243-.111.243-.243l.022-1.699-.022-1.902c0-.132-.088-.243-.243-.243zm1.567-.007c-.158 0-.25.114-.25.25l-.014 1.895.014 1.703c0 .136.092.25.25.25.165 0 .25-.114.25-.25l.018-1.703-.018-1.895c0-.136-.085-.25-.25-.25zm1.558-.004c-.172 0-.265.118-.265.265l-.011 1.891.011 1.699c0 .147.093.265.265.265.179 0 .265-.118.265-.265l.014-1.699-.014-1.891c0-.147-.086-.265-.265-.265zm1.567-.011c-.186 0-.277.125-.277.277l-.007 1.884.007 1.692c0 .152.091.277.277.277.19 0 .277-.125.277-.277l.011-1.692-.011-1.884c0-.152-.087-.277-.277-.277zm1.575-.003c-.197 0-.29.132-.29.29l-.004 1.881.004 1.688c0 .158.093.29.29.29.204 0 .29-.132.29-.29l.007-1.688-.007-1.881c0-.158-.086-.29-.29-.29z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                    
                    {{#if drop.tiktok_link}}
                    <div class="w-auto p-4">
                        <a href="{{drop.tiktok_link}}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center w-24 h-24 bg-gradient-radial-dark border border-gray-900 border-opacity-30 rounded-5xl hover:border-green-400 transition duration-300">
                            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                            </svg>
                        </a>
                    </div>
                    {{/if}}
                </div>
            </div>
        </section>
        {{/if}}
    </div>

    <!-- JavaScript for Form Handling -->
    <script>
        document.getElementById('drop-signup-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            const email = formData.get('email');
            
            const submitButton = form.querySelector('button[type="submit"]');
            const successMessage = document.getElementById('success-message');
            const errorMessage = document.getElementById('error-message');
            
            // Hide previous messages
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');
            
            // Disable button and show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Signing up...';
            
            try {
                const response = await fetch('/drop/signup/{{drop.slug}}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    successMessage.classList.remove('hidden');
                    form.reset();
                } else {
                    errorMessage.classList.remove('hidden');
                    document.getElementById('error-text').textContent = result.message || 'Something went wrong. Please try again.';
                }
            } catch (error) {
                errorMessage.classList.remove('hidden');
                document.getElementById('error-text').textContent = 'Network error. Please try again.';
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = '{{drop.button_text}}';
            }
        });
    </script>
</body>
</html>
