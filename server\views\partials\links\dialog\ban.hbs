<div class="content">
  <h2>Ban link?</h2>
  <p>
    Are you sure do you want to ban the link &quot;<b>{{link}}</b>&quot;?
  </p>
  <div class="ban-checklist">
    <label class="checkbox">
      <input id="user" name="user" type="checkbox" />
      User
    </label>
    <label class="checkbox">
      <input id="userLinks" name="userLinks" type="checkbox" />
      User links
    </label>
    <label class="checkbox">
      <input id="host" name="host" type="checkbox" />
      Host
    </label>
    <label class="checkbox">
      <input id="domain" name="domain" type="checkbox" />
      Domain
    </label>
  </div>
  <div class="buttons">
    <button type="button" hx-on:click="closeDialog()">Cancel</button>
    <button 
      type="button"
      class="danger confirm" 
      hx-post="/api/links/admin/ban/{id}" 
      hx-ext="path-params" 
      hx-vals='{"id":"{{id}}"}' 
      hx-target="closest .content" 
      hx-swap="none" 
      hx-include=".ban-checklist"
      hx-indicator="closest .content"
      hx-select-oob="#dialog-error"
    >
      <span class="stop">
        {{> icons/stop}}
      </span>
      Ban
    </button>
    {{> icons/spinner}}
  </div>
  <div id="dialog-error">
    {{#if error}}
      <p class="error">{{error}}</p>
    {{/if}}
  </div>
</div>