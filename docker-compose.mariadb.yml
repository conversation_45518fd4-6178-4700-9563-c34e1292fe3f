services:
  server:
    build:
      context: .
    volumes:
      - custom:/kutt/custom
    environment:
        DB_CLIENT: mysql2
        DB_HOST: mariadb
        DB_PORT: 3306
        REDIS_ENABLED: true
        REDIS_HOST: redis
        REDIS_PORT: 6379
    ports:
      - 3000:3000
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_started
  mariadb:
    image: mariadb:10
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'mysql ${DB_NAME} --user=${DB_USER} --password=${DB_PASSWORD} --execute "SELECT 1;"']
      interval: 3s
      retries: 5
      start_period: 30s
    volumes:
      - db_data_mariadb:/var/lib/mysql
    environment:
      MARIADB_DATABASE: ${DB_NAME}
      MARIADB_USER: ${DB_USER}
      MARIADB_PASSWORD: ${DB_PASSWORD}
      MARIADB_ROOT_PASSWORD: ${DB_PASSWORD}
    expose:
      - 3306
  redis:
    image: redis:alpine
    restart: always
    expose:
      - 6379
volumes:
  db_data_mariadb:
  custom: