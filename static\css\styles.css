@font-face {
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 200 1000;
    src: url(/fonts/nunito-variable.woff2) format('woff2');
}

:root {
    --bg-color: hsl(206, 12%, 95%);
    --text-color: hsl(200, 35%, 25%);
    --color-primary: hsl(207, 90%, 54%);
    --outline-color: hsl(188, 100%, 54%);
    --button-bg: linear-gradient(to right, #e0e0e0, #bdbdbd);
    --button-bg-box-shadow-color: rgba(160, 160, 160, 0.5);
    --button-bg-primary: linear-gradient(to right, hsl(207, 90%, 61%), hsl(218, 100%, 58%));
    --button-bg-primary-box-shadow-color: hsla(207, 90%, 61%, 0.5);
    --button-bg-secondary: linear-gradient(to right, hsl(262, 47%, 55%), hsl(265, 100%, 46%));
    --button-bg-secondary-box-shadow-color: hsla(258, 58%, 42%, 0.5);
    --button-bg-danger: linear-gradient(to right, hsl(0, 84%, 58%), hsl(0, 78%, 50%));
    --button-bg-danger-box-shadow-color: hsla(0, 58%, 42%, 0.5);
    --button-bg-success: linear-gradient(to right, hsl(130, 58%, 45%), hsl(130, 67%, 45%));
    --button-bg-success-box-shadow-color: hsla(128, 80%, 48%, 0.5);
    --button-action-shadow-color: hsla(200, 15%, 60%, 0.12);
    --underline-color: hsl(200, 35%, 65%);
    --secondary-text-color: hsl(200, 14%, 60%);
    --send-icon-hover-color: hsl(262, 52%, 47%);
    --send-spinner-icon-color: hsl(200, 15%, 70%);
    --success-icon-color: hsl(144, 40%, 57%);
    --error-icon-color: hsl(0, 86%, 63%);
    --copy-icon-color: hsl(144, 40%, 57%);
    --copy-icon-bg-color: hsl(144, 100%, 96%);
    --copy-icon-shadow-color: hsla(200, 15%, 60%, 0.12);
    --focus-outline-color: hsla(207, 90%, 61%, 0.5);
    --checkbox-bg-color: hsl(262, 47%, 63%);
    --input-shadow-color: hsla(200, 15%, 70%, 0.2);
    --input-hover-shadow-color: hsla(200, 15%, 70%, 0.4);
    --input-label-color: hsl(200, 35%, 25%);
    --table-bg-color: hsl(200, 12%, 95%);
    --table-shadow-color: hsla(200, 20%, 70%, 0.3);
    --table-tr-border-color: hsl(200, 14%, 94%);
    --table-tr-hover-bg-color: hsl(200, 14%, 98%);
    --table-head-tr-border-color: hsl(200, 14%, 90%);
    --table-status-gray-bg-color: hsl(200, 12%, 95%);
    --keyframe-slidey-offset: 0;
}


/* ANIMATIONS */

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadein {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes slidey {
    from {
        transform: translateY(var(--keyframe-slidey-offset))
    }
    to {
        transform: translateY(0)
    }
}

@keyframes tooltip {
    to {
        opacity: 0.9;
        transform: translate(-50%, 0);
    }
}


/* GENERAL */

body {
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    font: 16px/1.45 'Nunito', sans-serif;
    overflow-x: hidden;
    color: var(--text-color);
}

* {
    box-sizing: border-box;
    outline-color: var(--outline-color);
}

*::-moz-focus-inner {
    border: none;
}

.hidden {
    display: none;
}

hr {
    width: 100%;
    height: 2px;
    outline: none;
    border: none;
    background-color: hsl(200, 20%, 92%);
}

span.bold {
    font-weight: bold;
}

span.underline {
    border-bottom: 2px dotted #999;
}

.space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.align-center {
    display: flex;
    align-items: center;
}

a,
button.link {
    color: var(--color-primary);
    border-bottom: 1px dotted transparent;
    text-decoration: none;
    transition: all 0.2s ease-out;
    cursor: pointer;
}

a:hover,
button.link:hover {
    border-bottom-color: var(--color-primary);
}

a.wrapper-only {
    color: inherit;
}

a.nav {
    color: inherit;
    padding-bottom: 2px;
}

a.nav:hover {
    color: var(--color-primary);
}

a.button,
button {
    position: relative;
    width: auto;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 32px;
    font-size: 13px;
    font-weight: normal;
    text-align: center;
    line-height: 1;
    word-break: keep-all;
    color: #444;
    border: none;
    border-radius: 100px;
    transition: all 0.4s ease-out;
    cursor: pointer;
    overflow: hidden;
    background: var(--button-bg);
    box-shadow: 0 5px 6px var(--button-bg-box-shadow-color);
}

a.button.primary,
button.primary {
    color: white;
    background: var(--button-bg-primary);
    box-shadow: 0 5px 6px var(--button-bg-primary-box-shadow-color);
}

a.button.secondary,
button.secondary {
    color: white;
    background: var(--button-bg-secondary);
    box-shadow: 0 5px 6px var(--button-bg-secondary-box-shadow-color);
}

a.button.danger,
button.danger {
    color: white;
    background: var(--button-bg-danger);
    box-shadow: 0 5px 6px var(--button-bg-danger-box-shadow-color);
}

a.button.success,
button.success {
    color: white;
    background: var(--button-bg-success);
    box-shadow: 0 5px 6px var(--button-bg-success-box-shadow-color);
}

a.button:focus,
a.button:hover,
button:focus,
button:hover {
    box-shadow: 0 6px 15px var(--button-bg-box-shadow-color);
    transform: translateY(-2px) scale(1.02, 1.02);
}

a.button.primary:focus,
a.button.primary:hover,
button.primary:focus,
button.primary:hover {
    box-shadow: 0 6px 15px var(--button-bg-primary-box-shadow-color);
}

a.button.secondary:focus,
a.button.secondary:hover,
button.secondary:focus,
button.secondary:hover {
    box-shadow: 0 6px 15px var(--button-bg-secondary-box-shadow-color);
}

a.button.danger:focus,
a.button.danger:hover,
button.danger:focus,
button.danger:hover {
    box-shadow: 0 6px 15px var(--button-bg-danger-box-shadow-color);
}

a.button.success:focus,
a.button.success:hover,
button.success:focus,
button.success:hover {
    box-shadow: 0 6px 15px var(--button-bg-success-box-shadow-color);
}

a.button:disabled,
button:disabled {
    cursor: default;
}

a.button:disabled:hover,
button:disabled:hover {
    transform: none;
}

a.button svg.with-text,
a.button span svg,
button svg.with-text,
button span svg {
    width: 1.1em;
    height: auto;
    margin-right: 0.5rem;
    stroke: white;
    stroke-width: 2;
}

a.button.action,
button.action {
    padding: 5px;
    width: 24px;
    height: 24px;
    box-shadow: 0 2px 1px var(--button-action-shadow-color);
}

a.button.action:disabled,
button.action:disabled {
    background: none;
    box-shadow: none;
}

a.button.action svg,
button.action svg {
    width: 100%;
    margin-right: 0;
}

a.button.action.delete,
button.action.delete {
    background: hsl(0, 100%, 96%);
}

a.button.action.delete svg,
button.action.delete svg {
    stroke-width: 2;
    stroke: hsl(0, 100%, 69%);
}

a.button.action.edit,
button.action.edit {
    background: hsl(46, 100%, 94%);
}

a.button.action.edit svg,
button.action.edit svg {
    stroke-width: 2.5;
    stroke: hsl(46, 90%, 50%);
}

a.button.action.qrcode,
button.action.qrcode {
    background: hsl(0, 0%, 94%);
}

a.button.action.qrcode svg,
button.action.qrcode svg {
    fill: hsl(0, 0%, 35%);
    stroke: none;
}

a.button.action.stats,
button.action.stats {
    background: hsl(260, 100%, 96%);
}

a.button.action.stats svg,
button.action.stats svg {
    stroke-width: 2.5;
    stroke: hsl(260, 100%, 69%);
}

a.button.action.ban,
button.action.ban {
    background: hsl(10, 100%, 96%);
}

a.button.action.ban svg,
button.action.ban svg {
    stroke-width: 2;
    stroke: hsl(10, 100%, 40%);
}

a.button.action.password sv,
button.action.password svg,
a.button.action.banned svg,
button.action.banned svg {
    stroke-width: 2.5;
    stroke: #bbb;
}

button.nav {
    box-sizing: border-box;
    width: auto;
    height: 28px;
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    border: none;
    border-radius: 4px;
    box-shadow: 0 0px 10px rgba(100, 100, 100, 0.1);
    background: none;
    background-color: white;
    transition: all 0.2s ease-in-out;
    font-size: 12px;
    cursor: pointer;
}

button.nav:disabled {
    background-color: #f6f6f6;
    box-shadow: 0 0px 5px rgba(150, 150, 150, 0.1);
    opacity: 0.9;
    color: #bbb;
    cursor: default;
}

button.nav svg {
    width: 14px;
    height: auto;
}

button.nav svg {
    stroke-width: 2.5;
}

button.nav:hover {
    transform: translateY(-2px);
}

button.nav:disabled:hover {
    transform: none;
}

button.table {
    height: 32px;
    padding: 0 1rem;
    font-size: 12px;
    border-radius: 3px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px var(--button-bg-box-shadow-color);
}

button.table:hover {
    transform: translateY(-2px);
    box-shadow: 0 1px 2px var(--button-bg-box-shadow-color);
}

button.table.primary,
button.primary:focus,
button.primary:hover {
    box-shadow: 0 1px 2px var(--button-bg-primary-box-shadow-color);
}

button.table.secondary,
button.secondary:focus,
button.secondary:hover {
    box-shadow: 0 1px 2px var(--button-bg-secondary-box-shadow-color);
}

button.table.danger,
button.danger:focus,
button.danger:hover {
    box-shadow: 0 1px 2px var(--button-bg-danger-box-shadow-color);
}

button.table.success,
button.success:focus,
button.success:hover {
    box-shadow: 0 1px 2px var(--button-bg-success-box-shadow-color);
}

button.link {
    position: relative;
    width: auto;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0 2px 0;
    font-size: 1rem;
    font-weight: normal;
    border-radius: 0;
    text-align: left;
    line-height: normal;
    word-break: normal;
    cursor: pointer;
    background: none;
    box-shadow: none;
}

button.link:hover {
    box-shadow: none;
    transform: none;
}

button.link span {
    height: 1rem;
}

button.link svg {
    stroke: var(--color-primary);
}

svg.spinner {
    animation: spin 1s linear infinite, fadein 0.3s ease-in-out;
}

input {
    filter: none;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    box-sizing: border-box;
    width: 240px;
    height: 44px;
    padding: 0 24px;
    font-size: 15px;
    letter-spacing: 0.05em;
    color: #444;
    background-color: white;
    border: none;
    border-radius: 100px;
    border-bottom: 5px solid #f5f5f5;
    border-bottom-width: 5px;
    box-shadow: 0 10px 35px var(--input-shadow-color);
    transition: all 0.5s ease-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    outline: none;
    box-shadow: 0 20px 35px var(--input-hover-shadow-color);
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder {
    font-size: 14px;
    letter-spacing: 0.05em;
    color: #888;
}

.error input[type="text"],
.error input[type="email"],
.error input[type="password"] {
    border-bottom-color: rgba(250, 10, 10, 0.8);
    box-shadow: 0 10px 15px hsla(0, 100%, 75%, 0.2);
}

select {
    position: relative;
    width: 240px;
    height: 44px;
    padding: 0 24px;
    font-size: 15px;
    box-sizing: border-box;
    letter-spacing: 0.05em;
    color: #444;
    background-color: white;
    box-shadow: 0 10px 35px var(--input-shadow-color);
    border: none;
    border-radius: 100px;
    border-bottom: 5px solid #f5f5f5;
    border-bottom-width: 5px;
    transition: all 0.5s ease-out;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%235c666b' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat, repeat;
    background-position: right 1.2em top 50%, 0 0;
    background-size: 1em auto, 100%;
}

select:focus {
    outline: none;
    box-shadow: 0 20px 35px var(--input-hover-shadow-color)
}

.error select {
    border-bottom-color: rgba(250, 10, 10, 0.8);
    box-shadow: 0 10px 15px hsla(0, 100%, 75%, 0.2);
}

input[type="checkbox"] {
    position: relative;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(50, 50, 50, 0.2);
    margin: 0;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

input[type="checkbox"]:focus {
    outline: 3px solid var(--focus-outline-color);
}

input[type="checkbox"]::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    display: block;
    border-radius: 2px;
    background-color: var(--checkbox-bg-color);
    box-shadow: 0 2px 4px rgba(50, 50, 50, 0.2);
    cursor: pointer;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.1s ease-in-out;
}

input[type="checkbox"]:checked:after {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

input.table-input,
select.table-input {
    width: auto;
    height: 32px;
    font-size: 13px;
    padding: 0 1.5rem;
    border-radius: 3px;
    border-bottom-width: 2px;
}

select.table-input {
    width: 150px;
}

input.table-input::placeholder {
    font-size: 13px;
}

select:has(option[value=""]:checked) {
    letter-spacing: 0.05em;
    color: #888;
}

label {
    display: flex;
    color: var(--input-label-color);
    font-size: 1rem;
    flex-direction: column;
    align-items: flex-start;
    font-weight: bold;
}

label input {
    margin-top: 0.5rem;
}

label.checkbox {
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

label.checkbox input[type="checkbox"] {
    margin: 0 0.75rem 2px 0;
}


/* Preview toggle specific styling */

label.show-preview {
    font-size: 13px !important;
    font-weight: normal !important;
    margin-bottom: 0.5rem !important;
    flex-direction: row !important;
    align-items: center !important;
}

label.show-preview input[type="checkbox"] {
    width: 14px !important;
    height: 14px !important;
    margin: 0 6px 0 0 !important;
    transform: none !important;
}


/* Enhanced metadata field styling for better responsive layout */


/* Standard textarea styling */

textarea {
    box-sizing: border-box;
    width: 240px;
    min-height: 80px;
    padding: 12px 24px;
    font-size: 15px;
    letter-spacing: 0.05em;
    color: #444;
    background-color: white;
    border: none;
    border-radius: 12px;
    border-bottom: 5px solid #f5f5f5;
    box-shadow: 0 10px 35px var(--input-shadow-color);
    transition: all 0.5s ease-out;
    resize: vertical;
    font-family: inherit;
}

textarea:focus {
    outline: none;
    box-shadow: 0 20px 35px var(--input-hover-shadow-color);
}


/* Table input styling for edit forms */

textarea.table-input {
    width: 100%;
    max-width: 300px;
    min-width: 200px;
    min-height: 60px;
    font-size: 13px;
    padding: 8px 1.5rem;
    border-radius: 3px;
    border-bottom-width: 2px;
}


/* Metadata field specific styling */

input.table-input[name*="meta_"],
textarea.table-input[name*="meta_"] {
    width: 100%;
    max-width: 350px;
    min-width: 250px;
}


/* Responsive metadata field layout */

@media (max-width: 768px) {
    input.table-input[name*="meta_"],
    textarea.table-input[name*="meta_"] {
        max-width: 100%;
        min-width: 200px;
    }
}


/* Fix form layout for metadata fields */

form div:has(label[class*="metadata"]) {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}


/* Ensure consistent spacing for metadata labels */

label[class*="metadata"] {
    margin-bottom: 0.75rem;
}


/* Fix overflow issues in table cells */

table td.content form>div {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    overflow: visible;
}

table td.content form>div>label {
    width: 100%;
    overflow: visible;
}

p.error,
p.success {
    display: flex;
    align-items: center;
    font-weight: normal;
    animation: fadein 0.3s ease-in-out;
}

p.error {
    color: red;
}

p.success {
    color: #0ea30e;
}

table {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 6px 15px var(--table-shadow-color);
    text-align: center;
    overflow: auto;
}

table tr {
    flex: 1 1 auto;
}

table tr,
table th,
table td,
table thead,
table tfoot {
    display: flex;
    overflow: hidden;
}

table tbody,
table tr {
    overflow: visible;
}

table tbody,
table thead,
table tfoot {
    flex-direction: column;
}

table tr {
    padding: 0 0.5rem;
    border-bottom: 1px solid var(--table-tr-border-color);
}

table th,
table td {
    flex-basis: 0;
    padding: 0.75rem;
}

table td {
    position: relative;
    white-space: nowrap;
    font-size: 15px;
    align-items: center;
}

table tbody {
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
    animation: fadein 0.3s ease-in-out;
}

table tbody+tfoot {
    border: none;
}

table thead {
    background-color: var(--table-bg-color);
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    font-weight: bold;
}

table thead tr {
    border-bottom: 1px solid var(--table-head-tr-border-color);
}

table tfoot {
    background-color: var(--table-bg-color);
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
}

table tr.loading-placeholder {
    flex: 1 1 auto;
    justify-content: center;
    animation: fadein 0.3s ease-in-out;
}

table tr.loading-placeholder td {
    flex: 0 0 auto;
    font-size: 18px;
    font-weight: 300;
}

table select {
    margin-right: 1rem;
}

table .tab {
    display: flex;
    align-items: center;
}

table .tab a {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.4rem 1rem;
    margin: 0 0.5rem;
    font-size: 12px;
    color: var(--text-color);
    border: none;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    box-shadow: 0 0px 10px rgba(100, 100, 100, 0.1);
    font-weight: normal;
    transition: all 0.2s ease-in-out;
}

table .tab a:first-child {
    margin-left: 0
}

table .tab a.active {
    background-color: #f6f6f6;
    box-shadow: 0 0px 5px rgba(150, 150, 150, 0.1);
    color: #aaa;
    font-weight: bold;
    opacity: 0.9;
    cursor: default;
}

table .tab a:not(.active):hover {
    transform: translateY(-2px);
}


/* ===== INDUSTRY-LEADING MOBILE MODAL DESIGN ===== */

.dialog {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: none;
    justify-content: center;
    align-items: center;
    /* RESEARCH-BASED: Enhanced backdrop with blur */
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1000;
    /* MOBILE-FIRST: Prevent body scroll */
    overflow: hidden;
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

.dialog.open {
    display: flex;
}

.dialog .box {
    /* RESEARCH-BASED: Mobile-first responsive sizing */
    width: 100%;
    max-width: 480px;
    max-height: 90vh;
    margin: 16px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    /* PREMIUM: Enhanced styling */
    background: white;
    border-radius: 20px;
    /* RESEARCH-BASED: Layered shadows for depth */
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 20px -5px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* MODERN: Glass morphism effect */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    overflow: hidden;
    /* RESEARCH-BASED: Smooth scale-in animation */
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dialog.qrcode .box {
    min-width: auto;
    padding: 2rem;
    /* Ensure QR code modal is properly sized on mobile */
    max-width: 300px;
    max-height: 90vh;
    position: relative;
}


/* QR code container styling */

.qr-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
}

.qr-container canvas {
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.qr-container canvas:hover {
    transform: scale(1.02);
}


/* QR code buttons container */

.qr-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
}


/* QR code button styling */

.qr-button {
    width: 100%;
    height: 44px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.qr-button.primary:hover {
    background-color: var(--primary-color-hover);
}

.qr-button.primary.copied {
    background-color: #28a745;
}

.qr-button.primary.fallback {
    background-color: #ffc107;
    color: #212529;
}

.qr-button.secondary {
    color: white;
    background: var(--button-bg-danger);
    box-shadow: 0 5px 6px var(--button-bg-danger-box-shadow-color);
    border: none;
    border-radius: 100px;
}

.qr-button.secondary:hover {
    box-shadow: 0 6px 15px var(--button-bg-danger-box-shadow-color);
    transform: translateY(-2px) scale(1.02, 1.02);
}


/* Button icon styling */

.qr-button .button-icon {
    margin-right: 0.5rem;
    font-size: 16px;
}


/* QR code instruction text styling */

.qr-instructions {
    text-align: center;
    padding: 1rem 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.qr-instructions .instruction-arrow {
    font-size: 24px;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.qr-instructions p {
    margin: 0.5rem 0;
}

.qr-instructions strong {
    color: #495057;
}


/* DROPS SECTION STYLING */

.drops-section {
    margin: 2rem 0;
    padding: 0;
}

.drops-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.drops-section .section-title h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.drops-section .section-description {
    margin: 0;
    color: var(--secondary-text-color);
    font-size: 0.9rem;
}

.drops-section .section-actions {
    flex-shrink: 0;
}


/* ===== MODERN DROPS GRID LAYOUT ===== */

.drops-grid {
    /* RESEARCH-BASED: Responsive grid with optimal card sizing */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 24px;
    /* FUTURE-PROOF: Smooth transitions for layout changes */
    transition: all 0.3s ease;
}


/* RESPONSIVE: Adjust grid for different screen sizes */

@media (max-width: 768px) {
    .drops-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 0 8px;
    }
}

@media (min-width: 1200px) {
    .drops-grid {
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
        gap: 32px;
    }
}

@media (min-width: 1600px) {
    .drops-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 40px;
    }
}


/* 🎯 STRATEGIC DROP CARD - CLEAN & PURPOSEFUL DESIGN ===== */

.strategic-drop-card {
    /* 🎨 CLEAN FOUNDATION */
    background: white;
    border-radius: 16px;
    /* 🎯 PURPOSEFUL SHADOWS */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
    /* ⚡ SMOOTH TRANSITIONS */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* 🔮 SUBTLE GLASS EFFECT */
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    /* 🎭 FLEXIBLE LAYOUT */
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

.strategic-drop-card:hover {
    /* 🎯 STRATEGIC HOVER - PURPOSEFUL LIFT */
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}


/* 🎨 CLEAN CARD HEADER */

.strategic-drop-card .card-header-clean {
    position: relative;
    height: 140px;
    overflow: hidden;
}

.strategic-drop-card .card-cover-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    transition: transform 0.3s ease;
}

.strategic-drop-card:hover .card-cover-image {
    transform: scale(1.05);
}

.strategic-drop-card .card-cover-gradient {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.strategic-drop-card .cover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
}

.strategic-drop-card .subtle-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
}

.strategic-drop-card .cover-icon {
    width: 48px;
    height: 48px;
    color: rgba(255, 255, 255, 0.9);
    z-index: 2;
    position: relative;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}


/* 🎯 STRATEGIC STATUS BADGE */

.strategic-drop-card .status-badge-container {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 10;
}

.strategic-drop-card .status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.strategic-drop-card .status-badge.status-live {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.strategic-drop-card .status-badge.status-draft {
    background: rgba(100, 116, 139, 0.9);
    color: white;
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.strategic-drop-card .live-pulse {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    animation: livePulse 2s infinite;
}

@keyframes livePulse {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

.strategic-drop-card .draft-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
}


/* ⚡ QUICK ACTIONS OVERLAY */

.strategic-drop-card .quick-actions-overlay {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 10;
}

.strategic-drop-card:hover .quick-actions-overlay {
    opacity: 1;
    transform: translateY(0);
}


/* RESEARCH-BASED QUICK ACTION BUTTONS */

.strategic-drop-card .quick-action {
    /* ACCESSIBILITY: 44px minimum touch target */
    width: 44px;
    height: 44px;
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    position: relative;
    overflow: hidden;
    /* ACCESSIBILITY: Focus outline */
    outline: none;
}


/* ACCESSIBILITY: Focus state */

.strategic-drop-card .quick-action:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}


/* EDIT ACTION - Blue primary */

.strategic-drop-card .quick-action.edit-action {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 0.95) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 4px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.strategic-drop-card .quick-action.edit-action:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.98) 0%, rgba(29, 78, 216, 0.98) 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5), 0 4px 8px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}


/* STATS ACTION - Orange secondary */

.strategic-drop-card .quick-action.stats-action {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.95) 0%, rgba(217, 119, 6, 0.95) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4), 0 2px 4px rgba(245, 158, 11, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.strategic-drop-card .quick-action.stats-action:hover {
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.98) 0%, rgba(180, 83, 9, 0.98) 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.5), 0 4px 8px rgba(245, 158, 11, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.strategic-drop-card .quick-action:active {
    transform: translateY(0) scale(1);
}

.strategic-drop-card .quick-action svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    /* ENSURE PROPER STROKE COLOR */
    stroke: currentColor;
    fill: none;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}


/* 📝 CLEAN CONTENT SECTION */

.strategic-drop-card .card-content-clean {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.strategic-drop-card .content-header {
    flex-shrink: 0;
}

.strategic-drop-card .card-title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.3;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.strategic-drop-card .card-description {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}


/* 📊 CLEAN STATS GRID */

.strategic-drop-card .stats-grid-clean {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.strategic-drop-card .stat-item {
    text-align: center;
    padding: 8px 4px;
}

.strategic-drop-card .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
    font-variant-numeric: tabular-nums;
}

.strategic-drop-card .stat-label {
    font-size: 11px;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}


/* 🔗 CLEAN URL DISPLAY */

.strategic-drop-card .url-display-clean {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.strategic-drop-card .url-container {
    flex: 1;
    min-width: 0;
    font-size: 13px;
    line-height: 1.4;
}

.strategic-drop-card .url-domain {
    color: #64748b;
    font-weight: 400;
}

.strategic-drop-card .url-slug {
    color: #1e293b;
    font-weight: 600;
    word-break: break-all;
}


/* RESEARCH-BASED COPY BUTTON */

.strategic-drop-card .copy-url-btn {
    /* ACCESSIBILITY: 44px minimum touch target */
    width: 44px;
    height: 44px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    color: #3b82f6;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    /* ACCESSIBILITY: Focus outline */
    outline: none;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* ACCESSIBILITY: Focus state */

.strategic-drop-card .copy-url-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.strategic-drop-card .copy-url-btn:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-color: rgba(59, 130, 246, 0.4);
    color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.2), 0 2px 4px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .copy-url-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2), inset 0 2px 4px rgba(59, 130, 246, 0.1);
}

.strategic-drop-card .copy-url-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    /* ENSURE PROPER STROKE COLOR */
    stroke: currentColor;
    fill: none;
}


/* 🎯 CLEAN ACTION SECTION */

.strategic-drop-card .card-actions-clean {
    padding: 16px 24px 24px;
    border-top: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
}

.strategic-drop-card .primary-actions {
    display: flex;
    gap: 12px;
}


/* 🎯 RESEARCH-BASED BUTTON SYSTEM */

.strategic-drop-card .action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 10px;
    border: 2px solid;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    /* ACCESSIBILITY: Minimum touch target */
    min-height: 44px;
    /* ACCESSIBILITY: Focus outline */
    outline: none;
}


/* ACCESSIBILITY: Focus state */

.strategic-drop-card .action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}


/* PRIMARY BUTTON - Research-based blue system */

.strategic-drop-card .action-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .action-btn.primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3), 0 2px 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.strategic-drop-card .action-btn.primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3), inset 0 2px 4px rgba(0, 0, 0, 0.1);
}


/* SECONDARY BUTTON - Research-based neutral system */

.strategic-drop-card .action-btn.secondary {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-color: #e2e8f0;
    color: #475569;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .action-btn.secondary:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #cbd5e1;
    color: #334155;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .action-btn.secondary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.strategic-drop-card .action-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.strategic-drop-card .secondary-actions {
    display: flex;
    gap: 8px;
}


/* RESEARCH-BASED ICON BUTTON SYSTEM */

.strategic-drop-card .icon-btn {
    /* ACCESSIBILITY: 44px minimum touch target */
    width: 44px;
    height: 44px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #64748b;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    /* ACCESSIBILITY: Focus outline */
    outline: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* ACCESSIBILITY: Focus state */

.strategic-drop-card .icon-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}


/* DEFAULT HOVER STATE */

.strategic-drop-card .icon-btn:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .icon-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.06);
}


/* QR BUTTON - Purple accent */

.strategic-drop-card .icon-btn.qr-btn:hover {
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-color: #c4b5fd;
    color: #7c3aed;
    box-shadow: 0 6px 12px rgba(124, 58, 237, 0.15), 0 2px 4px rgba(124, 58, 237, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* DELETE BUTTON - Red danger state */

.strategic-drop-card .icon-btn.delete-btn:hover {
    background: linear-gradient(135deg, #fef2f2 0%, #fde8e8 100%);
    border-color: #fca5a5;
    color: #dc2626;
    box-shadow: 0 6px 12px rgba(220, 38, 38, 0.15), 0 2px 4px rgba(220, 38, 38, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.strategic-drop-card .icon-btn svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    /* ENSURE PROPER STROKE COLOR */
    stroke: currentColor;
    fill: none;
}


/* 🗑️ DELETE MODAL STYLES */

.delete-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.delete-modal-overlay.show {
    opacity: 1;
}

.delete-modal {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 90%;
    margin: 20px;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.delete-modal-overlay.show .delete-modal {
    transform: scale(1) translateY(0);
}

.delete-modal-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f1f5f9;
}

.delete-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.delete-modal-content {
    padding: 16px 24px 24px;
}

.delete-modal-content p {
    margin: 0 0 12px 0;
    color: #64748b;
    line-height: 1.5;
}

.delete-modal-content p:last-child {
    margin-bottom: 0;
}

.delete-modal-content .warning-text {
    color: #dc2626;
    font-weight: 500;
    font-size: 14px;
}

.delete-modal-actions {
    padding: 0 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid;
}

.modal-btn.cancel-btn {
    background: white;
    border-color: #e2e8f0;
    color: #64748b;
}

.modal-btn.cancel-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.modal-btn.delete-btn {
    background: #dc2626;
    border-color: #dc2626;
    color: white;
}

.modal-btn.delete-btn:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.stunning-drop-card .card-cover-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    /* 🌊 PARALLAX EFFECT */
    transform: translateZ(0);
    transition: transform 0.6s ease;
}

.stunning-drop-card:hover .card-cover-image {
    transform: translateZ(20px) scale(1.1);
}

.stunning-drop-card .card-cover-gradient {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    /* 🌈 ANIMATED GRADIENT */
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0%,
    100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.stunning-drop-card .animated-mesh-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.15) 0%, transparent 50%), radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: meshFloat 6s ease-in-out infinite;
}

@keyframes meshFloat {
    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(2deg);
    }
}

.stunning-drop-card .floating-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.stunning-drop-card .orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: orbFloat 4s ease-in-out infinite;
}

.stunning-drop-card .orb-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.stunning-drop-card .orb-2 {
    width: 15px;
    height: 15px;
    top: 60%;
    right: 30%;
    animation-delay: 1.5s;
}

.stunning-drop-card .orb-3 {
    width: 25px;
    height: 25px;
    bottom: 30%;
    left: 70%;
    animation-delay: 3s;
}

@keyframes orbFloat {
    0%,
    100% {
        transform: translateY(0) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0.6;
    }
}

.stunning-drop-card .cover-icon-glow {
    width: 64px;
    height: 64px;
    color: rgba(255, 255, 255, 0.95);
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* 🔥 EPIC GLOW EFFECT */
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.8));
    }
}

.stunning-drop-card .image-overlay-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.15) 50%, rgba(16, 185, 129, 0.2) 100%);
}

.stunning-drop-card .floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent), radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent), radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
    0% {
        background-position: 0 0, 0 0, 0 0;
    }
    100% {
        background-position: 100px 100px, -100px 100px, 50px -100px;
    }
}


/* 🔥 GLOWING STATUS INDICATORS */

.stunning-drop-card .status-glow {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 20;
}

.stunning-drop-card .status-badge-glow {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 25px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    /* 🌟 EPIC GLASS MORPHISM */
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stunning-drop-card .status-badge-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.stunning-drop-card:hover .status-badge-glow::before {
    left: 100%;
}

.stunning-drop-card .status-badge-glow.status-live-glow {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(34, 197, 94, 0.9) 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4), 0 0 20px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stunning-drop-card .status-badge-glow.status-draft-glow {
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.9) 0%, rgba(148, 163, 184, 0.9) 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(100, 116, 139, 0.4), 0 0 20px rgba(100, 116, 139, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stunning-drop-card .pulse-ring {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    position: relative;
    animation: pulseRing 2s infinite;
}

.stunning-drop-card .pulse-ring::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulseDot 2s infinite;
}

@keyframes pulseRing {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.7;
    }
}

@keyframes pulseDot {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
    }
}

.stunning-drop-card .pulse-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    animation: pulseDot 2s infinite;
}

.stunning-drop-card .draft-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    opacity: 0.9;
}

.stunning-drop-card .status-text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}


/* ⚡ LIGHTNING QUICK ACTIONS */

.stunning-drop-card .lightning-actions {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 12px;
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: 15;
}

.stunning-drop-card:hover .lightning-actions {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.stunning-drop-card .lightning-btn {
    width: 44px;
    height: 44px;
    border-radius: 16px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    /* 🌟 EPIC GLASS MORPHISM */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.stunning-drop-card .lightning-btn .btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stunning-drop-card .lightning-btn:hover .btn-glow {
    opacity: 1;
}

.stunning-drop-card .lightning-btn.edit-lightning {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(99, 102, 241, 0.9) 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.3);
}

.stunning-drop-card .lightning-btn.stats-lightning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.9) 0%, rgba(251, 191, 36, 0.9) 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4), 0 0 20px rgba(245, 158, 11, 0.3);
}

.stunning-drop-card .lightning-btn:hover {
    transform: scale(1.15) rotate(5deg);
    filter: brightness(1.2);
}

.stunning-drop-card .lightning-btn svg {
    width: 20px;
    height: 20px;
    z-index: 2;
    position: relative;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}


/* 💎 HOLOGRAPHIC CONTENT */

.stunning-drop-card .holographic-content {
    padding: 28px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.stunning-drop-card .title-section-wow {
    flex-shrink: 0;
}

.stunning-drop-card .title-holographic {
    margin: 0 0 12px 0;
    font-size: 22px;
    font-weight: 800;
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.3;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    animation: titleShimmer 3s ease-in-out infinite;
}

@keyframes titleShimmer {
    0%,
    100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.stunning-drop-card .description-shimmer {
    margin: 0;
    color: #64748b;
    font-size: 15px;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 0.9;
}


/* 📊 NEON STATS DASHBOARD */

.stunning-drop-card .neon-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.stunning-drop-card .neon-stat-card {
    position: relative;
    padding: 16px 12px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border-radius: 16px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    text-align: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stunning-drop-card .neon-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
}

.stunning-drop-card .stat-glow-ring {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3));
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stunning-drop-card .neon-stat-card:hover .stat-glow-ring {
    opacity: 1;
}

.stunning-drop-card .stat-value-neon {
    font-size: 20px;
    font-weight: 800;
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 6px;
    font-variant-numeric: tabular-nums;
    position: relative;
    z-index: 2;
}

.stunning-drop-card .stat-label-glow {
    font-size: 10px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    z-index: 2;
}


/* 🔗 FUTURISTIC URL DISPLAY */

.stunning-drop-card .futuristic-url {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.05) 0%, rgba(30, 41, 59, 0.05) 100%);
    border-radius: 16px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.stunning-drop-card .url-hologram {
    flex: 1;
    position: relative;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.stunning-drop-card .url-scan-line {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
    0%,
    100% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
}

.stunning-drop-card .url-text-container {
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    z-index: 2;
}

.stunning-drop-card .url-domain-cyber {
    color: #64748b;
    font-weight: 400;
}

.stunning-drop-card .url-slug-neon {
    color: #1e293b;
    font-weight: 700;
    word-break: break-all;
}

.stunning-drop-card .copy-btn-cyber {
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    color: #3b82f6;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.stunning-drop-card .copy-btn-cyber:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
    transform: scale(1.1);
    border-color: rgba(59, 130, 246, 0.4);
}

.stunning-drop-card .copy-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stunning-drop-card .copy-btn-cyber:hover .copy-btn-glow {
    opacity: 1;
}

.stunning-drop-card .copy-btn-cyber svg {
    width: 16px;
    height: 16px;
    z-index: 2;
    position: relative;
}


/* 🎮 EPIC ACTION COMMAND CENTER */

.stunning-drop-card .action-command-center {
    padding: 20px 28px 28px;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(240, 248, 255, 0.05) 100%);
}

.stunning-drop-card .primary-command-group {
    display: flex;
    gap: 12px;
}

.stunning-drop-card .epic-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 14px;
    border: none;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stunning-drop-card .epic-action-btn.edit-command {
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    color: white;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.stunning-drop-card .epic-action-btn.stats-command {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    color: white;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

.stunning-drop-card .epic-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    filter: brightness(1.1);
}

.stunning-drop-card .btn-particle-field {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stunning-drop-card .epic-action-btn:hover .btn-particle-field {
    opacity: 1;
}

.stunning-drop-card .btn-energy-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.stunning-drop-card .epic-action-btn:hover .btn-energy-core {
    opacity: 1;
    transform: translate(-50%, -50%) scale(3);
}

.stunning-drop-card .btn-text-glow {
    position: relative;
    z-index: 2;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.stunning-drop-card .epic-action-btn svg {
    width: 16px;
    height: 16px;
    z-index: 2;
    position: relative;
}

.stunning-drop-card .secondary-command-group {
    display: flex;
    gap: 8px;
}

.stunning-drop-card .mini-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(100, 116, 139, 0.1);
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.stunning-drop-card .mini-action-btn:hover {
    transform: scale(1.1);
    background: rgba(100, 116, 139, 0.2);
}

.stunning-drop-card .mini-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stunning-drop-card .mini-btn-glow.danger-glow {
    background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
}

.stunning-drop-card .mini-action-btn:hover .mini-btn-glow {
    opacity: 1;
}

.stunning-drop-card .mini-action-btn svg {
    width: 16px;
    height: 16px;
    color: #64748b;
    z-index: 2;
    position: relative;
}


/* CARD CONTENT */

.modern-drop-card .card-content {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.modern-drop-card .content-header {
    flex-shrink: 0;
}

.modern-drop-card .card-title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.3;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.modern-drop-card .card-description {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.5;
    /* RESEARCH-BASED: Optimal text truncation */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}


/* STATS GRID */

.modern-drop-card .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.modern-drop-card .stat-item {
    text-align: center;
    padding: 8px 4px;
}

.modern-drop-card .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
    /* MODERN: Better number rendering */
    font-variant-numeric: tabular-nums;
}

.modern-drop-card .stat-label {
    font-size: 11px;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}


/* URL DISPLAY */

.modern-drop-card .url-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.modern-drop-card .url-container {
    flex: 1;
    min-width: 0;
    font-size: 13px;
    line-height: 1.4;
}

.modern-drop-card .url-domain {
    color: #64748b;
    font-weight: 400;
}

.modern-drop-card .url-slug {
    color: #1e293b;
    font-weight: 600;
    word-break: break-all;
}

.modern-drop-card .copy-url-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.modern-drop-card .copy-url-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: scale(1.05);
}

.modern-drop-card .copy-url-btn svg {
    width: 14px;
    height: 14px;
}


/* CARD ACTIONS */

.modern-drop-card .card-actions {
    padding: 16px 24px 24px;
    border-top: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.modern-drop-card .action-group {
    display: flex;
    gap: 8px;
}

.modern-drop-card .action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #64748b;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.modern-drop-card .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modern-drop-card .action-btn.edit {
    border-color: #3b82f6;
    color: #3b82f6;
}

.modern-drop-card .action-btn.edit:hover {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-color: #2563eb;
    color: #2563eb;
}

.modern-drop-card .action-btn.stats {
    border-color: #f59e0b;
    color: #f59e0b;
}

.modern-drop-card .action-btn.stats:hover {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-color: #d97706;
    color: #d97706;
}

.modern-drop-card .action-btn.qr {
    border-color: #8b5cf6;
    color: #8b5cf6;
}

.modern-drop-card .action-btn.qr:hover {
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-color: #7c3aed;
    color: #7c3aed;
}

.modern-drop-card .action-btn.delete {
    border-color: #ef4444;
    color: #ef4444;
}

.modern-drop-card .action-btn.delete:hover {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-color: #dc2626;
    color: #dc2626;
}

.modern-drop-card .action-btn svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

.modern-drop-card .secondary-actions .action-btn {
    padding: 8px;
}

.modern-drop-card .secondary-actions .action-btn span {
    display: none;
}

.drop-stats {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.drop-stats .stat {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--secondary-text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.drop-url {
    font-size: 0.85rem;
    color: var(--secondary-text-color);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 6px;
    word-break: break-all;
}

.drop-url .url-slug {
    color: var(--primary-color);
    font-weight: 500;
}

.drop-card-actions {
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}


/* Empty State */

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--secondary-text-color);
}

.empty-state .empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-weight: 500;
}

.empty-state p {
    margin: 0 0 1.5rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}


/* Loading State */

.drops-loading {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-text-color);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    margin: 0 auto 1rem;
}


/* Create Drop Form */

.slug-input-wrapper {
    display: flex;
    align-items: center;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.slug-prefix {
    background: #f8f9fa;
    padding: 0.5rem 0.75rem;
    color: var(--secondary-text-color);
    font-size: 0.9rem;
    border-right: 1px solid #dee2e6;
    white-space: nowrap;
}

.slug-input-wrapper input {
    border: none;
    flex: 1;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.slug-input-wrapper input:focus {
    outline: none;
    box-shadow: none;
}


/* DROP LANDING PAGE STYLING */

.drop-landing-page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.drop-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--bg-color) 0%, #f8f9fa 100%);
}

.drop-header {
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drop-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
    transition: opacity 0.2s ease;
}

.brand-link:hover {
    opacity: 0.8;
}

.brand-logo {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.drop-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.drop-content {
    max-width: 500px;
    width: 100%;
    text-align: center;
}

.drop-cover-image {
    margin-bottom: 2rem;
}

.drop-cover-image img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.drop-info {
    margin-bottom: 2rem;
}

.drop-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.drop-description {
    margin-bottom: 1.5rem;
}

.drop-description p {
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
    opacity: 0.9;
}

.drop-stats {
    margin-bottom: 1rem;
}

.signup-count {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.drop-signup-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}


/* 🎯 FORM STYLES MOVED TO drop-shared.css FOR CONSISTENCY */


/* All form styling is now unified in drop-shared.css to ensure
   perfect consistency between preview and live drop pages */

.signup-button {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 56px;
}

.signup-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.signup-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.button-spinner {
    display: inline-flex;
    align-items: center;
}

.spinner {
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.success-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}

.drop-footer {
    padding: 1rem 2rem;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

.footer-link {
    color: inherit;
    text-decoration: none;
    font-weight: 500;
}

.footer-link:hover {
    text-decoration: underline;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.dialog .content-wrapper {
    display: flex;
    flex-direction: column;
}

.dialog .loading {
    display: none;
    width: 24px;
    height: 24px;
    margin: 3rem 0;
    animation: fadein 0.2s ease-in-out;
}

.dialog.htmx-request .loading {
    display: block;
}

.dialog.htmx-request .content-wrapper {
    display: none;
}

.dialog .loading svg {
    animation: spin 1s linear infinite;
}


/* INDUSTRY-LEADING MODAL CONTENT STYLING */

.dialog .content {
    display: flex;
    flex-direction: column;
    /* RESEARCH-BASED: Optimal padding for mobile */
    padding: 24px;
    max-height: 90vh;
    overflow-y: auto;
    /* MOBILE-FIRST: Smooth scrolling */
    -webkit-overflow-scrolling: touch;
    animation: fadein 0.2s ease-in-out;
}

.dialog .content h2 {
    font-weight: 700 !important;
    font-size: 24px;
    margin: 0 0 24px 0 !important;
    color: #1e293b;
    text-align: center;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}


/* RESEARCH-BASED: Mobile-optimized button layout */

.dialog .content .buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f1f5f9;
}

.dialog .content .buttons button {
    /* RESEARCH-BASED: Full-width buttons for mobile */
    width: 100%;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* MOBILE-FIRST: Optimal touch target */
    min-height: 56px;
    margin: 0;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}


/* RESEARCH-BASED: Primary button styling */

.dialog .content .buttons button.primary,
.dialog .content .buttons button[type="submit"] {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 2px solid #3b82f6;
    color: white;
    /* RESEARCH-BASED: Primary action should be first */
    order: 1;
    /* PREMIUM: Enhanced shadows */
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 2px 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.dialog .content .buttons button.primary:hover,
.dialog .content .buttons button[type="submit"]:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4), 0 4px 8px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}


/* RESEARCH-BASED: Secondary button styling */

.dialog .content .buttons button:not(.primary):not([type="submit"]) {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    color: #475569;
    /* RESEARCH-BASED: Secondary button should be less prominent */
    order: 2;
}

.dialog .content .buttons button:not(.primary):not([type="submit"]):hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dialog .content .buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}


/* ENHANCED MESSAGE STYLING FOR MODALS */

.dialog .content #dialog-error,
.dialog .content #create-drop-error {
    margin-top: 16px;
    margin-bottom: 0;
    padding: 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}


/* ERROR STATE */

.dialog .content #dialog-error.error,
.dialog .content #create-drop-error.error,
.dialog .content #dialog-error:not(.success-message),
.dialog .content #create-drop-error:not(.success-message) {
    background: linear-gradient(135deg, #fef2f2 0%, #fde8e8 100%);
    border: 2px solid #fecaca;
    color: #dc2626;
    /* MODERN: Enhanced error styling */
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* SUCCESS STATE */

.dialog .content #dialog-error.success-message,
.dialog .content #create-drop-error.success-message {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #bbf7d0;
    color: #166534;
    /* MODERN: Enhanced success styling */
    box-shadow: 0 4px 8px rgba(34, 197, 94, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dialog .content .icon {
    width: 48px;
    height: 48px;
    border-radius: 100%;
    padding: 5px;
    margin-bottom: 1.5rem;
    border: 2px solid;
}

.dialog .content .icon svg {
    width: 100%;
    height: auto;
}

.dialog .content .icon.success {
    border-color: var(--success-icon-color);
}

.dialog .content .icon.success svg {
    stroke-width: 2;
    stroke: var(--success-icon-color);
}

.dialog .content .icon.error {
    border-color: var(--error-icon-color);
}

.dialog .content .icon.error svg {
    stroke-width: 1.5;
    stroke: var(--error-icon-color);
}

.dialog .content svg.spinner {
    display: none;
    width: 24px;
    margin: 0.5rem 0;
}

.dialog .content.htmx-request svg.spinner {
    display: block;
}

.dialog .content.htmx-request button {
    display: none;
}


/* RESEARCH-BASED: Single-column mobile-first form layout */

.dialog .content label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
}

.dialog .content .form-group {
    margin-bottom: 20px;
}

.dialog .content .form-group:last-child {
    margin-bottom: 0;
}

.dialog .content input[type="text"],
.dialog .content input[type="password"],
.dialog .content input[type="email"],
.dialog .content textarea,
.dialog .content select {
    width: 100%;
    /* RESEARCH-BASED: Optimal touch target size for mobile */
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    /* MOBILE-FIRST: Prevent zoom on iOS */
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-sizing: border-box;
    /* MODERN: Enhanced shadows */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* MOBILE-FIRST: Remove browser styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* RESEARCH-BASED: Consistent height */
    min-height: 56px;
}

.dialog .content input[type="text"]:focus,
.dialog .content input[type="password"]:focus,
.dialog .content input[type="email"]:focus,
.dialog .content textarea:focus,
.dialog .content select:focus {
    outline: none;
    border-color: #3b82f6;
    /* RESEARCH-BASED: Enhanced focus ring */
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.dialog .content textarea {
    resize: vertical;
    min-height: 100px;
    /* MOBILE-FIRST: Better line height for readability */
    line-height: 1.5;
}

.dialog .content .form-help {
    display: block;
    margin-top: 6px;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
}

.inputs {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.inputs label {
    flex: 0 0 0;
    margin-right: 1rem;
}

.inputs label:last-child {
    margin-right: 0;
}

.search-input-wrapper {
    position: relative;
}

.search-input-wrapper button {
    position: absolute;
    display: none;
    right: 0;
    top: 50%;
    width: auto;
    height: auto;
    padding: 3px;
    margin: 0;
    background-color: transparent;
    background: none;
    box-shadow: none;
    transform: translateY(-50%);
    cursor: pointer;
    margin-right: 0.25rem;
    transition: all 0.2s ease-in-out;
}

.search-input-wrapper button:hover {
    transform: translateY(-55%);
}

.search-input-wrapper svg {
    width: 0.9rem;
    height: auto;
    stroke-width: 2;
    stroke: #888;
}

[data-tooltip] {
    position: relative;
    overflow: visible;
}

[data-tooltip]:before,
[data-tooltip]:after {
    position: absolute;
    left: 50%;
    display: none;
    font-size: 11px;
    line-height: 1;
    opacity: 0;
    transform: translate(-50%, -0.5rem);
}

[data-tooltip]:before {
    content: "";
    border: 4px solid transparent;
    top: -4px;
    border-bottom-width: 0;
    border-top-color: #333;
    z-index: 1001;
}

[data-tooltip]:after {
    content: attr(data-tooltip);
    top: -25px;
    text-align: center;
    min-width: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 5px 7px;
    border-radius: 4px;
    box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.35);
    background: #333;
    color: #fff;
    z-index: 1000;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    display: block;
}

[data-tooltip]:before,
[data-tooltip]:after,
[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    animation: tooltip 300ms ease-out forwards;
}


/* DISTINCT */

.main-wrapper {
    min-height: 100vh;
    width: 100%;
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    flex-direction: column;
}

.section-container {
    max-width: 90%;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 1rem;
}

.htmx-spinner .spinner {
    display: none;
}

.htmx-spinner.htmx-request button svg {
    display: none;
}

.htmx-spinner.htmx-request .spinner {
    display: block;
}


/* LOGIN & SIGNUP */

form#login-signup {
    width: 420px;
    max-width: 100%;
    flex: 1 1 auto;
    display: flex;
    padding: 0 16px;
    flex-direction: column;
    margin: 3rem 0 0;
}

form#login-signup label {
    margin-bottom: 2rem;
}

form#login-signup input {
    width: 100%;
    height: 72px;
    margin-top: 1rem;
    padding: 0 3rem;
    font-size: 16px;
}

form#login-signup .buttons-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

form#login-signup .buttons-wrapper button {
    height: 56px;
    flex: 0 0 48%;
    padding: 0 1rem 2px;
    margin: 0;
}

form#login-signup .buttons-wrapper button.full {
    flex-basis: 100%;
}

form#login-signup a.forgot-password {
    align-self: flex-start;
    font-size: 14px;
}

form#login-signup svg.spinner {
    display: none;
}

form#login-signup.htmx-request:not(.signup) .login svg {
    display: none;
}

form#login-signup.htmx-request:not(.signup) .login svg.spinner {
    display: block;
}

form#login-signup.htmx-request.signup .signup svg {
    display: none;
}

form#login-signup.htmx-request.signup .signup svg.spinner {
    display: block;
}

form#login-signup.htmx-request .error {
    opacity: 0;
}

form#login-signup p.error {
    margin-bottom: 0;
}

.admin-form-title {
    font-size: 26px;
    font-weight: 300;
    margin: 0 0 3rem;
    text-align: center;
}

.login-signup-message {
    flex: 1 1 auto;
    margin-top: 3rem;
}

.login-signup-message h1 {
    font-weight: 300;
    font-size: 24px;
}


/* HEADER */

header {
    box-sizing: border-box;
    margin: 0;
    width: 100%;
    max-width: 1232px;
    padding: 0 32px;
    height: 102px;
    justify-content: space-between;
    align-items: center;
    display: flex;
    overflow: hidden;
}

header .logo-wrapper {
    display: flex;
    align-items: center;
}

header a.logo {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 22px;
    font-weight: bold;
    text-decoration: none;
    border: none;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

header a.logo:hover {
    border: none;
    color: inherit;
}

header .logo img {
    margin: 0 12px 0 0;
    padding: 0;
}

header ul.logo-links {
    list-style: none;
    display: flex;
    align-items: flex-end;
    margin: 0 0 0 0.5rem;
    padding: 0;
}

header ul.logo-links li {
    padding: 2px 0 0;
    margin: 0 0 0 32px;
}

header ul.logo-links li a {
    font-size: 1rem;
}

header nav ul {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin: 0 0 0 2rem;
    padding: 0;
}

header nav ul li:last-child {
    margin-left: 0;
}


/* SHORTENER */

main {
    width: 800px;
    max-width: 100%;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 1rem;
    margin-top: 1rem;
}

main #shorturl {
    display: flex;
    align-items: center;
    margin: 1rem 0 3rem;
}

main #shorturl h1 {
    margin: 0;
    border-bottom: 2px dotted transparent;
    font-weight: 300;
    font-size: 2rem;
}

main #shorturl h1.link {
    cursor: pointer;
    border-bottom-color: var(--underline-color);
    transition: opacity 0.3s ease-in-out;
    --keyframe-slidey-offset: -10px;
    animation: fadein 0.2s ease-in-out, slidey 0.2s ease-in-out;
}

main #shorturl h1.link:hover {
    opacity: 0.8;
}

.clipboard {
    width: 35px;
    height: 35px;
    display: flex;
    margin-right: 1rem;
}

.clipboard button {
    width: 100%;
    height: 100%;
    display: flex;
    margin: 0;
    padding: 7px;
    box-shadow: none;
    outline: none;
    border: none;
    background: none;
    border-radius: 100%;
    background-color: var(--copy-icon-bg-color);
    transition: transform 0.4s ease-out;
    box-shadow: 0 2px 1px var(--copy-icon-shadow-color);
    cursor: pointer;
    --keyframe-slidey-offset: -10px;
    animation: slidey 0.2s ease-in-out;
}

.clipboard.small {
    width: 24px;
    height: 24px;
}

.clipboard.small button {
    width: 24px;
    height: 24px;
    padding: 5px;
}

.clipboard button:hover,
.clipboard button:focus {
    transform: translateY(-2px) scale(1.02, 1.02);
}

.clipboard button:focus {
    outline: 3px solid var(--focus-outline-color);
}

.clipboard svg {
    stroke: var(--copy-icon-color);
    width: 100%;
    height: auto;
}

.clipboard svg.copy {
    stroke-width: 2.5;
}

.clipboard svg.check {
    display: none;
    padding: 3px;
    stroke-width: 3;
    --keyframe-slidey-offset: -10px;
    animation: slidey 0.2s ease-in-out;
}

.clipboard.copied button {
    background-color: transparent;
    box-shadow: none;
}

.clipboard.copied button {
    display: none;
}

.clipboard.copied svg.check {
    display: block;
}

main #shorturl h1 span {
    border-bottom: 1px dotted #999;
}

main form {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
}

main form input#target {
    position: relative;
    width: 100%;
    height: 72px;
    display: flex;
    padding: 0 84px 0 40px;
    font-size: 20px;
}

main form input#target::placeholder {
    font-size: 17px;
}

main form p.error {
    font-size: 13px;
    margin-left: 0.5rem;
}

main form .target-wrapper p.error {
    font-size: 15px;
    margin-left: 1rem;
    margin-bottom: 0;
}

main form .target-wrapper {
    position: relative;
    width: 100%;
    height: auto;
}

main form button.submit {
    box-sizing: content-box;
    position: absolute;
    cursor: pointer;
    width: 28px;
    height: auto;
    right: 0;
    top: 16px;
    padding: 4px;
    margin: 0 2rem 0;
    background: none;
    box-shadow: none;
    outline: none;
    border: none;
}

main form button.submit:focus,
main form button.submit:hover {
    outline: none;
}

main form button.submit svg.send {
    width: 100%;
    fill: #aaa;
    animation: fadein 0.3s ease-in-out;
    transition: fill 0.2s ease-in-out;
}

main form button.submit:hover svg.send {
    fill: var(--send-icon-hover-color);
}

main form button.submit svg.spinner {
    display: none;
    fill: none;
    stroke: var(--send-spinner-icon-color);
    stroke-width: 2;
}

main form.htmx-request button.submit svg.send {
    display: none;
}

main form.htmx-request button.submit svg.spinner {
    display: block;
}

main form label#advanced {
    margin-top: 2rem;
    align-self: flex-start;
}

main form label#advanced input {
    width: 1.1rem;
    height: 1.1rem;
    margin-bottom: 2px;
}

#advanced-options {
    display: flex;
    flex-direction: column;
    margin-top: 1.5rem;
}

#advanced-options.hidden {
    display: none;
}

.advanced-input-wrapper {
    width: 100%;
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.advanced-input-wrapper label {
    flex: 1 1 0;
    padding-right: 1rem;
}

.advanced-input-wrapper label.expire-in {
    flex: 1 1 34%;
}

.advanced-input-wrapper label.description {
    flex: 1 1 65%;
}

.advanced-input-wrapper label:last-child {
    padding-right: 0;
}

.advanced-input-wrapper label input,
.advanced-input-wrapper label select {
    width: 100%;
    margin-top: 0.5rem;
}


/* MAIN TABLE */

#main-table-wrapper {
    width: 1200px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    align-items: flex-start;
    padding: 0 1rem;
    margin: 7rem 0 7.5rem;
}

#main-table-wrapper h2 {
    font-weight: 300;
    margin-bottom: 1rem;
}

#main-table-wrapper table thead,
#main-table-wrapper table tbody,
#main-table-wrapper table tfoot {
    min-width: 1000px;
}

#main-table-wrapper tr {
    padding: 0 0.5rem;
}

#main-table-wrapper th,
#main-table-wrapper td {
    padding: 1rem;
}

#main-table-wrapper td {
    font-size: 1rem;
}

#main-table-wrapper table .original-url {
    flex: 7 7 0;
}

#main-table-wrapper table .created-at {
    flex: 2.5 2.5 0;
}

#main-table-wrapper table .short-link {
    flex: 3 3 0;
}

#main-table-wrapper.admin-table-wrapper table .short-link {
    overflow: visible;
}

#main-table-wrapper table .views {
    flex: 1 1 0;
    justify-content: flex-end;
}

#main-table-wrapper table .actions {
    flex: 3 3 0;
    justify-content: flex-end;
    overflow: visible;
}

#main-table-wrapper table .actions a.button,
#main-table-wrapper table .actions button {
    margin-right: 0.5rem;
}

#main-table-wrapper table .actions a.button:last-child,
#main-table-wrapper table .actions button:last-child {
    margin-right: 0;
}

#main-table-wrapper table .users-id {
    flex: 3 3 0;
    justify-content: flex-end;
}

#main-table-wrapper table .users-email {
    flex: 9 9 0;
}

#main-table-wrapper table .users-created-at {
    flex: 4 4 0;
}

#main-table-wrapper table .users-updated-at {
    flex: 4 4 0;
}

#main-table-wrapper table .users-verified {
    flex: 3 3 0;
    overflow: visible;
}

#main-table-wrapper table .users-role {
    flex: 2 2 0;
    overflow: visible;
}

#main-table-wrapper table .users-links-count {
    flex: 3 3 0;
    justify-content: flex-end;
    overflow: visible;
}

#main-table-wrapper table .users-actions {
    flex: 2 2 0;
}

#main-table-wrapper table .domains-id {
    flex: 2 2 0;
    justify-content: flex-end;
}

#main-table-wrapper table .domains-address {
    flex: 7 7 0;
}

#main-table-wrapper table .domains-homepage {
    flex: 5 5 0;
}

#main-table-wrapper table .domains-created-at {
    flex: 3 3 0;
}

#main-table-wrapper table .domains-links-count {
    flex: 3 3 0;
    justify-content: flex-end;
    overflow: visible;
}

#main-table-wrapper table .domains-actions {
    flex: 2 2 0;
}

#main-table-wrapper table td.original-url,
#main-table-wrapper table td.created-at,
#main-table-wrapper.admin-table-wrapper table td.short-link,
#main-table-wrapper table td.users-email,
#main-table-wrapper table td.domains-address,
#main-table-wrapper table td.users-created-at,
#main-table-wrapper table td.users-verified {
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

table .short-link-wrapper {
    display: flex;
    align-items: center;
}

#main-table-wrapper table td .description {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 14px;
    color: #888;
}

#main-table-wrapper table td .description a {
    color: #aaa;
    border-bottom-color: #aaa;
}

#main-table-wrapper table td .description svg {
    stroke: #aaa;
    stroke-width: 2;
    width: 0.85em;
    margin-right: 0.25rem;
}

#main-table-wrapper table td .description span {
    color: #aaa;
}

#main-table-wrapper table td .description a:hover {
    border-bottom-color: transparent;
}


/* Ensure consistent link text sizing across all screen sizes */

#main-table-wrapper table td.original-url a,
#main-table-wrapper table td.short-link a {
    font-size: inherit !important;
    line-height: 1.4;
    word-break: break-all;
    overflow-wrap: break-word;
    text-align: left;
    display: block;
    width: 100%;
}

#main-table-wrapper table .status {
    font-size: 11px;
    font-weight: bold;
    padding: 4px 12px;
    border-radius: 12px;
    margin-top: 0.25rem;
}

#main-table-wrapper table .status:first-child {
    margin-top: 0;
}

#main-table-wrapper table .status.gray {
    background-color: var(--table-status-gray-bg-color);
}

#main-table-wrapper table .status.green {
    background-color: hsl(102.4, 100%, 93.3%);
}

#main-table-wrapper table .status.red {
    background-color: hsl(0, 100%, 96.7%);
}

#main-table-wrapper table tr.no-data {
    flex: 1 1 auto;
    justify-content: center;
    animation: fadein 0.3s ease-in-out;
}

#main-table-wrapper table.htmx-request tbody tr {
    opacity: 0.5;
}

#main-table-wrapper table tr.loading-placeholder {
    opacity: 0.6 !important;
}

#main-table-wrapper table tr.loading-placeholder td,
#main-table-wrapper table tr.no-data td {
    flex: 0 0 auto;
    font-size: 18px;
    font-weight: 300;
}

#main-table-wrapper table tr.loading-placeholder svg.spinner {
    width: 1rem;
    height: auto;
    margin-right: 0.5rem;
    stroke-width: 1.5;
}

#main-table-wrapper table thead tr.controls {
    justify-content: space-between;
}

#main-table-wrapper table thead tr.controls.with-filters {
    align-items: flex-end;
}

#main-table-wrapper table tfoot tr.controls {
    justify-content: flex-end;
}

#main-table-wrapper table th.search {
    flex: 1 1 auto;
    align-items: center;
}

#main-table-wrapper table th.filters {
    flex: 1 1 auto;
    flex-direction: column;
    align-items: start;
}

#main-table-wrapper table th.filters>div {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

#main-table-wrapper table th.filters>div:last-child {
    margin-bottom: 0;
}

#main-table-wrapper table th.nav {
    flex: 0 0 auto;
    align-items: center;
}

#main-table-wrapper table tr.controls .checkbox {
    margin-left: 1rem;
    font-size: 15px;
}

#main-table-wrapper table .limit,
#main-table-wrapper table .pagination {
    display: flex;
    align-items: center;
}

#main-table-wrapper table button.nav {
    margin-right: 0.75rem;
}

#main-table-wrapper table button.nav:last-child {
    margin-right: 0;
}

#main-table-wrapper table .nav-divider {
    height: 20px;
    width: 1px;
    opacity: 0.4;
    background-color: #888;
    margin: 0 1.5rem;
}

#main-table-wrapper table tbody tr:hover {
    background-color: var(--table-tr-hover-bg-color);
}

#main-table-wrapper table tbody td.right-fade:after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 16px;
    background: linear-gradient(to left, white, rgba(255, 255, 255, 0.001));
}

#main-table-wrapper table tbody tr:hover td.right-fade:after {
    background: linear-gradient(to left, var(--table-tr-hover-bg-color), rgba(255, 255, 255, 0.001));
}

#main-table-wrapper table .clipboard {
    margin-right: 0.5rem;
}

#main-table-wrapper table .clipboard svg.check {
    width: 24px;
}

#main-table-wrapper table tr.edit {
    background-color: #fafafa;
}

#main-table-wrapper table tr.edit td {
    width: 100%;
    padding: 2rem 1.5rem;
    flex-basis: auto;
}

#main-table-wrapper table tr.edit td form {
    width: 100;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

#main-table-wrapper table tr.edit td form>div {
    width: 100%;
    display: flex;
    align-items: start;
}

#main-table-wrapper table tr.edit label {
    margin: 0 0.5rem 1rem;
}

#main-table-wrapper table tr.edit label:first-child {
    margin-left: 0;
}

#main-table-wrapper table tr.edit label:last-child {
    margin-right: 0;
}

#main-table-wrapper table tr.edit input {
    height: 44px;
    padding: 0 1.5rem;
    font-size: 15px;
}

#main-table-wrapper table tr.edit input,
#main-table-wrapper table tr.edit input+p {
    width: 240px;
    max-width: 100%;
    font-size: 14px;
    text-wrap: wrap;
    text-align: left;
}

#main-table-wrapper table tr.edit input[name="target"],
#main-table-wrapper table tr.edit input[name="description"],
#main-table-wrapper table tr.edit input[name="meta_title"],
#main-table-wrapper table tr.edit input[name="meta_image"],
#main-table-wrapper table tr.edit textarea[name="meta_description"],
#main-table-wrapper table tr.edit input[name="target"]+p,
#main-table-wrapper table tr.edit input[name="description"]+p,
#main-table-wrapper table tr.edit input[name="meta_title"]+p,
#main-table-wrapper table tr.edit input[name="meta_image"]+p,
#main-table-wrapper table tr.edit textarea[name="meta_description"]+p {
    width: 420px;
}

#main-table-wrapper table tr.edit textarea {
    min-height: 80px;
    padding: 1rem 1.5rem;
    font-size: 15px;
    font-family: inherit;
    resize: vertical;
}


/* Metadata form styling for shortener */

.advanced-input-wrapper .meta-title,
.advanced-input-wrapper .meta-description,
.advanced-input-wrapper .meta-image {
    flex: 1;
}

.advanced-input-wrapper textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

#main-table-wrapper table tr.edit button {
    height: 38px;
    margin-right: 1rem;
}

#main-table-wrapper table tr.edit button:last-child {
    margin-right: 0;
}

#main-table-wrapper table tr.edit form {
    --keyframe-slidey-offset: -5px;
    animation: fadein 0.3s ease-in-out, slidey 0.32s ease-in-out;
}

#main-table-wrapper table tr.edit form.no-animation {
    animation: none;
}

#main-table-wrapper table tr.edit {
    display: none;
}

#main-table-wrapper table tr.edit.show {
    display: flex;
}

#main-table-wrapper table tr.edit td.loading {
    display: none;
}

#main-table-wrapper table tr.edit.htmx-request td.loading {
    display: block;
}

#main-table-wrapper table tr.edit td.loading svg {
    width: 16px;
    height: 16px;
}

#main-table-wrapper table tr.edit form.htmx-request button .reload {
    display: none;
}

#main-table-wrapper table tr.edit form button .loader {
    display: none;
}

#main-table-wrapper table tr.edit form.htmx-request button .loader {
    display: inline-block;
}

#main-table-wrapper table tr.edit form .response p {
    margin: 2rem 0 0;
}

#main-table-wrapper table tr.edit p.no-data {
    width: 100%;
    text-align: center;
}

.dialog .ban-checklist {
    display: flex;
    align-items: center;
}

.dialog .ban-checklist label {
    margin: 1rem 1.5rem 1rem 0;
}

.dialog .ban-checklist label:last-child {
    margin-right: 0;
}

#main-table-wrapper tr.category {
    justify-content: space-between;
    align-items: center;
}

#main-table-wrapper th.category-total {
    flex: 1 1 auto;
}

#main-table-wrapper th.category-total p {
    margin: 0;
    font-size: 15px;
    font-weight: normal
}

#main-table-wrapper th.category-tab {
    flex: 2 2 auto;
    justify-content: flex-end;
}


/* ADMIN */

table .search-input-wrapper {
    margin-right: 1rem;
}

input.search.admin {
    max-width: 200px;
}

.content.admin-create form {
    display: flex;
    flex-direction: column;
}

.content.admin-create .checkbox-wrapper {
    display: flex;
    align-items: center;
}

.content.admin-create .checkbox-wrapper label {
    margin-right: 1rem;
}

.content.admin-create .buttons {
    justify-content: center;
}

.content.admin-create .buttons button {
    flex: 1 1 auto;
}


/* FOOTER */

footer {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 1rem 0;
    font-size: 13px;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-links {
    margin: 0;
    font-size: 13px;
    color: var(--text-color);
}

footer button.link {
    display: inline-block;
    font-size: 13px;
}

footer button.link .spinner {
    display: none;
    width: 1em;
    margin: 0 0 -2px;
}

footer button.link.htmx-request .spinner {
    display: inline;
}


/* 🚀 GITHUB BUILD STATUS INDICATOR */

.build-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.build-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    background: rgba(0, 0, 0, 0.05);
    color: #6b7280;
}

.build-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.build-icon {
    display: flex;
    align-items: center;
    opacity: 0.8;
}

.build-text {
    font-size: 11px;
    letter-spacing: 0.025em;
}


/* Status-specific styling */

.build-link.production {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #10b981;
}

.build-link.production:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.build-link.development {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #3b82f6;
}

.build-link.development:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.build-link.dirty {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-color: #f59e0b;
}

.build-link.dirty:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.build-link.unknown {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border-color: #6b7280;
}

.build-link.unknown:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}


/* SETTINGS */

#settings {
    width: 600px;
    max-width: 100%;
    padding: 0 16px;
    overflow: hidden;
}

h1.settings-welcome {
    font-size: 28px;
    font-weight: 300;
}

h1.settings-welcome span {
    border-bottom: 2px dotted #999;
    padding-bottom: 2px;
    font-weight: normal;
}


/* SETTINGS - DOMAIN */

#domains-table {
    margin-top: 1rem;
}

#domains-table .domain {
    flex: 2 2 0;
}

#domains-table .homepage {
    flex: 2 2 0;
}

#domains-table .actions {
    flex: 1 1 0;
    justify-content: flex-end;
    padding-right: 1rem;
}

#domains-table .no-entry {
    flex: 1 1 0;
    opacity: 0.8;
}

.add-domain-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin: 1.5rem 0 2rem;
}

.add-domain-wrapper>.spinner {
    width: 20px;
    display: none;
    margin: 1rem 0 0 1rem;
}

.add-domain-wrapper.htmx-request>button {
    display: none;
}

.add-domain-wrapper.htmx-request>.spinner {
    display: block;
}

form#add-domain {
    margin-top: 1rem;
}

form#add-domain .buttons-wrapper {
    display: flex;
}

form#add-domain button {
    margin-right: 1rem
}

form#add-domain .spinner {
    width: 20px;
    display: none;
}

form#add-domain.htmx-request .buttons-wrapper {
    display: none;
}

form#add-domain.htmx-request .spinner {
    display: block;
}

form#add-domain .error {
    font-size: 0.85rem;
}


/* SETTINGS - API */

#apikey-wrapper {
    margin-bottom: 1.5rem;
}

#apikey {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

#apikey p {
    font-weight: bold;
    border-bottom: 1px dotted #999;
    transition: opacity 0.2s ease-in-out;
    cursor: pointer;
    line-break: anywhere;
}

#apikey p:hover {
    opacity: 0.8;
}

form#generate-apikey .spinner {
    display: none;
}

form#generate-apikey.htmx-request svg {
    display: none;
}

form#generate-apikey.htmx-request .spinner {
    display: block;
}


/* SETTINGS - CHANGE PASSWORD */

#change-password-wrapper {
    margin-bottom: 1.5rem;
}

form#change-password {
    margin-top: 1.5rem;
}

form#change-password button {
    margin-top: 1rem;
}

form#change-password .spinner {
    display: none;
}

form#change-password.htmx-request svg {
    display: none;
}

form#change-password.htmx-request .spinner {
    display: block;
}


/* SETTINGS - CHANGE EMAIL */

#change-email-wrapper {
    margin-bottom: 1.5rem;
}

form#change-email {
    margin-top: 1.5rem;
}

form#change-email button {
    margin-top: 1rem;
}

form#change-email .spinner {
    display: none;
}

form#change-email.htmx-request svg {
    display: none;
}

form#change-email.htmx-request .spinner {
    display: block;
}


/* SETTINGS - DELETE ACCOUNT */

#delete-account-wrapper {
    margin-bottom: 1.5rem;
}

form#delete-account {
    margin-top: 1.5rem;
}

form#delete-account button {
    margin-top: 1rem;
}

form#delete-account .spinner {
    display: none;
}

form#delete-account.htmx-request svg {
    display: none;
}

form#delete-account.htmx-request .spinner {
    display: block;
}


/* STATS */

#stats-section {
    width: 1200px;
    max-width: 100%;
    padding: 0 16px;
}

.loading-stats {
    width: 100%;
    flex: 1 1 0;
    margin-top: -5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-stats .spinner {
    width: 1.25rem;
    margin-right: 0.5rem;
}

.stats-info {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.stats-info h2 {
    font-weight: 300;
    font-size: 24px;
}

.stats-info p {
    font-size: 14px;
}

.stats-info h2,
.stats-info p {
    margin: 0
}

#stats {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 6px 15px var(--table-shadow-color);
    overflow: hidden;
    padding: 0;
}

.stats-head {
    width: 100%;
    display: flex;
    align-items: center;
    background-color: var(--table-bg-color);
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
}

.total-number {
    font-weight: bold;
}

.stats-nav {
    display: flex;
    align-items: center;
}

.stats-nav button {
    margin-right: 0.75rem;
}

.stats-nav button:last-child {
    margin-right: 0;
}

.stats-period {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding: 0.75rem 1.5rem;
}

.stats-period h2 {
    font-size: 24px;
    font-weight: 300;
    margin: 1rem 0 0;
}

.stats-period span.total-in-period {
    font-weight: bold;
    border-bottom: 1px dotted var(--underline-color);
}

p.last-update {
    font-size: 14px;
    color: var(--secondary-text-color);
    margin: 0.75rem 0 0;
}

#stats canvas {
    width: 100%;
    margin: 2rem 0;
}

.stats-columns-wrapper {
    display: flex;
    align-items: flex-start;
}

.stats-columns-wrapper>div {
    flex: 1 1 50%;
}

svg.map {
    width: 100%;
}

svg.map path {
    fill: hsl(200, 15%, 92%);
    stroke: #fff;
    transition: all 0.1s ease-in-out;
}

svg.map path.color-1 {
    fill: hsl(261, 46%, 90%);
}

svg.map path.color-2 {
    fill: hsl(261, 46%, 86%);
}

svg.map path.color-3 {
    fill: hsl(261, 46%, 82%);
}

svg.map path.color-4 {
    fill: hsl(261, 46%, 76%);
}

svg.map path.color-5 {
    fill: hsl(261, 46%, 72%);
}

svg.map path.color-6 {
    fill: hsl(261, 46%, 68%);
}

svg.map path.active {
    stroke: hsl(261, 46%, 50%);
    stroke-width: 1.5;
}

#map-tooltip {
    position: fixed;
}

#map-tooltip.visible::before,
#map-tooltip.visible::after {
    display: block !important;
}

#map-tooltip:before {
    border-top-color: rgba(255, 255, 255, 0.95);
}

#map-tooltip:after {
    box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

.stats-back-to-home {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.stats-error {
    width: 100%;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.stats-error p {
    margin-top: -3rem;
    display: flex;
    align-items: center;
}

.stats-error p svg {
    width: 1.2rem;
    margin: 0 0.5rem 0.1rem 0;
}

.stats-error .stats-back-to-home {
    margin-top: 0
}


/* 404 - NOT FOUND */

#notfound {
    width: 800px;
    align-items: center;
}

#notfound h2 {
    font-size: 28px;
    font-weight: 300;
    text-align: center;
}


/* BANNED */

#banned {
    width: 1200px;
    align-items: center;
    text-align: center
}

#banned h2 {
    font-weight: normal;
}

#banned h4 {
    font-weight: normal;
    margin: 0;
}


/* REPORT */

#report {
    width: 600px;
}

#report form {
    display: flex;
    flex-direction: column;
    margin-top: 2rem;
}

#report form .inputs-wrapper {
    display: flex;
    align-items: flex-end;
}

#report form button {
    margin: 0 0 0.2rem 1rem;
}

#report form .spinner {
    display: none;
}

#report form.htmx-request svg {
    display: none;
}

#report form.htmx-request .spinner {
    display: block;
}

#report-email .spinner {
    display: none;
}

#report-email .htmx-request svg {
    display: none;
}

#report-email .htmx-request .spinner {
    display: block;
}

.eye-icon svg {
    stroke-width: 0.5;
}


/* RESET PASSWORD */

#reset-password form {
    width: 100%;
    display: flex;
    flex-direction: column;
}

#reset-password form .inputs-wrapper {
    display: flex;
    align-items: flex-end;
    margin-top: 2rem;
}

#reset-password form label {
    flex: 0 0 280px;
}

#reset-password form label input {
    width: 100%;
}

#reset-password form button {
    margin: 0 0 0.2rem 1rem;
}

#new-password h2 {
    margin-bottom: 0.5rem;
}

#new-password p {
    margin-bottom: 1.5rem;
}

#new-password-form label {
    margin-bottom: 1.5rem;
}

#new-password-form label input {
    width: 280px;
}

#new-password form {
    width: 420px;
    max-width: 100%;
    flex: 1 1 auto;
    display: flex;
    padding: 0 16px;
    flex-direction: column;
}

#new-password form label {
    margin-bottom: 2rem;
}

#new-password form input {
    width: 100%;
    height: 72px;
    margin-top: 1rem;
    padding: 0 3rem;
    font-size: 16px;
}

#new-password form button {
    height: 56px;
    padding: 0 1rem 2px;
    margin: 0;
}


/* VERIFY USER */


/* VERIFY CHANGE EMAIL */


/* RESET PASSWORD TOKEN */

.verify-page {
    width: 600px;
    align-items: center;
}

.verify-page h2,
.verify-page h3 {
    display: flex;
    align-items: center;
    text-align: center;
    font-weight: normal;
}

.verify-page h2 svg,
.verify-page h3 svg {
    width: 1.15em;
    height: auto;
    margin-right: 0.5rem;
}


/* URL INFO */

#url-info {
    width: 1200px;
    align-items: center;
    text-align: center;
    padding: 0 16px;
}

#url-info h3 {
    font-weight: normal;
    margin: 0;
}

#url-info p {
    line-break: anywhere;
}


/* PROTECTED */

#protected {
    width: 600px;
}

#protected form {
    width: 100%;
    margin-top: 1rem;
}

#protected form .inputs-wrapper {
    width: 100%;
    display: flex;
    align-items: flex-end;
}

#protected form label {
    flex: 0 0 280px;
}

#protected form label input {
    width: 100%;
}

#protected form button {
    margin: 0 0 0.2rem 1rem;
}

#protected form .spinner {
    display: none;
}

#protected form.htmx-request svg {
    display: none;
}

#protected form.htmx-request .spinner {
    display: block;
}


/* TERMS */

#terms {
    width: 600px;
}


/* ERROR PAGE */

#error-page {
    align-items: center;
    text-align: center;
}

#error-page h2 {
    margin: 0;
}

#error-page .back-to-home {
    margin-top: 2rem;
}


/* RESPONSIVE STYLES */

@media only screen and (max-width: 768px) {
    html,
    body {
        font-size: 14px;
    }
    input[type="text"],
    input[type="email"],
    input[type="password"],
    select {
        font-size: 14px;
        padding: 0 16px;
        height: 38px;
        letter-spacing: 0.04em;
        border-bottom-width: 4px;
    }
    label input {
        margin-top: 0.25rem;
    }
    input[type="text"]::placeholder,
    input[type="email"]::placeholder,
    input[type="password"]::placeholder {
        font-size: 13px;
        letter-spacing: 0.04em;
    }
    table tr {
        padding: 0 0.25rem;
    }
    table th,
    table td {
        padding: 0.5rem;
    }
    table td {
        font-size: 14px;
    }
    /* Ensure consistent link text sizing on mobile */
    #main-table-wrapper table td.original-url a,
    #main-table-wrapper table td.short-link a {
        font-size: 14px !important;
        line-height: 1.4;
        word-break: break-all;
        overflow-wrap: break-word;
        text-align: left;
    }
    /* Ensure consistent cell heights and alignment */
    #main-table-wrapper table td.original-url,
    #main-table-wrapper table td.short-link {
        vertical-align: top;
    }
    table tr.loading-placeholder td {
        font-size: 16px;
    }
    a.button,
    button {
        height: 32px;
        padding: 0 22px;
        font-size: 12px;
    }
    a.button.action,
    button.action {
        padding: 4px;
        width: 20px;
        height: 20px;
    }
    button.nav {
        height: 26px;
        padding: 0 7px;
        font-size: 11px;
    }
    .dialog .box {
        min-width: 300px;
        padding: 2rem 1.25rem;
    }
    .dialog.qrcode .box {
        padding: 1.5rem;
        max-width: 280px;
        /* Improve touch target for mobile dismissal */
        position: relative;
    }
    /* Mobile QR button styling */
    .qr-button {
        height: 48px;
        font-size: 16px;
        touch-action: manipulation;
    }
    .qr-buttons {
        gap: 1rem;
    }
    /* Mobile instruction text styling */
    .qr-instructions {
        font-size: 15px;
        padding: 1.25rem 0;
    }
    .qr-instructions .instruction-arrow {
        font-size: 28px;
    }
    /* Mobile Drops Section - Enhanced */
    .drops-section {
        margin: 1.5rem 0;
        padding: 1.5rem 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }
    .drops-section .section-header {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }
    .drops-section .section-title h2 {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .drops-section .section-description {
        font-size: 1rem;
        color: #64748b;
        line-height: 1.5;
        max-width: 300px;
        margin: 0 auto;
    }
    .drops-section .section-actions {
        align-self: stretch;
    }
    .drops-section .section-actions .button {
        width: 100%;
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }
    .drops-section .section-actions .button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    .drops-section .section-actions .button svg {
        width: 20px;
        height: 20px;
    }
    .drops-grid {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }
    .drop-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.8);
        overflow: hidden;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    .drop-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
    .drop-card-header {
        height: 100px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .drop-cover-default .drop-icon {
        width: 32px;
        height: 32px;
        color: rgba(255, 255, 255, 0.9);
    }
    .drop-status {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
    }
    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
    }
    .status-badge.active {
        background: rgba(34, 197, 94, 0.9);
        color: white;
    }
    .status-badge.inactive {
        background: rgba(148, 163, 184, 0.9);
        color: white;
    }
    .drop-card-content {
        padding: 1.25rem;
    }
    .drop-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }
    .drop-description {
        font-size: 0.95rem;
        color: #64748b;
        line-height: 1.5;
        margin-bottom: 1.25rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .drop-stats {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.25rem;
    }
    .drop-stats .stat {
        flex: 1;
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.8);
    }
    .stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        color: #1e293b;
        display: block;
        margin-bottom: 0.25rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .stat-label {
        font-size: 0.75rem;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.75px;
        font-weight: 600;
    }
    .drop-url {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 1rem;
        border-radius: 12px;
        font-size: 0.875rem;
        word-break: break-all;
        border: 1px solid #e2e8f0;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        margin-bottom: 1rem;
    }
    .drop-url .url-prefix {
        color: #64748b;
        font-weight: 500;
    }
    .drop-url .url-slug {
        color: #3b82f6;
        font-weight: 700;
    }
    .drop-card-actions {
        padding: 1rem 1.25rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-top: 1px solid #e2e8f0;
        display: flex;
        gap: 0.75rem;
        justify-content: space-between;
    }
    .drop-card-actions .button {
        flex: 1;
        padding: 0.875rem;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border: 1.5px solid transparent;
        background: white;
        color: #64748b;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    .drop-card-actions .button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .drop-card-actions .button.edit:hover {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-color: #3b82f6;
        color: #3b82f6;
    }
    .drop-card-actions .button.qrcode:hover {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #22c55e;
        color: #22c55e;
    }
    .drop-card-actions .button.stats:hover {
        background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        border-color: #f59e0b;
        color: #f59e0b;
    }
    .drop-card-actions .button.delete:hover {
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        border-color: #ef4444;
        color: #ef4444;
    }
    .drop-card-actions .button svg {
        width: 18px;
        height: 18px;
    }
    /* Enhanced Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
        background: white;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }
    .empty-state .empty-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
    .empty-state .empty-icon svg {
        width: 40px;
        height: 40px;
        color: white;
    }
    .empty-state h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.75rem;
    }
    .empty-state p {
        font-size: 1rem;
        color: #64748b;
        line-height: 1.6;
        margin-bottom: 2rem;
        max-width: 280px;
        margin-left: auto;
        margin-right: auto;
    }
    .empty-state .button {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
    }
    .empty-state .button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    .empty-state .button svg {
        width: 20px;
        height: 20px;
    }
    .slug-prefix {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
    /* Mobile Drop Landing Page */
    .drop-header {
        padding: 1rem;
    }
    .drop-main {
        padding: 1rem;
    }
    .drop-title {
        font-size: 2rem;
    }
    .drop-description p {
        font-size: 1rem;
    }
    .drop-signup-section {
        padding: 1.5rem;
    }
    .form-input {
        padding: 0.875rem;
        font-size: 16px;
        /* Prevents zoom on iOS */
    }
    .signup-button {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
        min-height: 50px;
    }
    .dialog .loading {
        width: 20px;
        height: 20px;
        margin: 2rem 0;
    }
    .dialog .content .buttons {
        margin-top: 1rem;
    }
    header {
        padding: 12px 16px;
        height: auto;
        min-height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
    }
    header a.logo {
        font-size: 18px;
        flex-shrink: 0;
    }
    header ul.logo-links {
        display: none;
    }
    header .logo img {
        margin-right: 8px;
        width: 20px;
        height: 20px;
    }
    header .logo-wrapper {
        flex-shrink: 0;
        min-width: 0;
    }
    header nav {
        flex-shrink: 0;
        margin-left: auto;
    }
    header nav ul {
        display: flex;
        flex-wrap: nowrap;
        gap: 0.25rem;
        align-items: center;
    }
    header nav ul li {
        margin-left: 0;
        flex-shrink: 0;
    }
    header nav ul li a.button {
        height: 32px;
        padding: 0 0.75rem;
        font-size: 12px;
        white-space: nowrap;
    }
    header nav ul li a.nav {
        font-size: 12px;
        padding: 0.25rem 0.5rem;
    }
    form#login-signup label {
        margin-bottom: 1.5rem;
    }
    form#login-signup input {
        height: 58px;
        margin-top: 0.75rem;
        padding: 0 2rem;
        font-size: 15px;
    }
    form#login-signup .buttons-wrapper {
        margin-bottom: 1rem;
    }
    form#login-signup .buttons-wrapper button {
        height: 44px;
    }
    form#login-signup a.forgot-password {
        font-size: 13px;
    }
    .login-signup-message {
        margin-top: 1.5rem;
    }
    .login-signup-message h1 {
        font-size: 20px;
    }
    main #shorturl {
        margin-bottom: 1.5rem;
    }
    main #shorturl h1 {
        font-size: 1.6rem;
    }
    .clipboard {
        width: 30px;
        height: 30px;
        margin-right: 0.5rem;
    }
    .clipboard svg.check {
        padding: 2px;
    }
    main form input#target {
        height: 58px;
        padding: 0 58px 0 26px;
        font-size: 15px;
    }
    main form input#target::placeholder {
        font-size: 14px;
    }
    main form p.error {
        font-size: 12px;
        margin-left: 0.25rem;
    }
    main form .target-wrapper p.error {
        font-size: 13px;
        margin-left: 0.5rem;
    }
    main form button.submit {
        width: 22px;
        top: 13px;
        margin: 0 1rem 0;
    }
    main form label#advanced {
        margin-top: 1.5rem;
    }
    main form label#advanced input {
        margin-bottom: 3px;
    }
    #main-table-wrapper {
        margin: 4rem 0 4.5rem;
    }
    #main-table-wrapper h2 {
        margin-bottom: 0.5rem;
    }
    #main-table-wrapper table thead,
    #main-table-wrapper table tbody,
    #main-table-wrapper table tfoot {
        min-width: 800px;
    }
    #main-table-wrapper tr {
        padding: 0 0.25rem;
    }
    #main-table-wrapper th,
    #main-table-wrapper td {
        padding: 0.75rem;
    }
    #main-table-wrapper table .actions a.button,
    #main-table-wrapper table .actions button {
        margin-right: 0.3rem;
    }
    #main-table-wrapper table td p.description {
        font-size: 12px;
    }
    #main-table-wrapper table tr.no-data td {
        font-size: 16px;
    }
    #main-table-wrapper.admin-table-wrapper table th.nav {
        flex-direction: column;
        align-items: flex-end;
    }
    #main-table-wrapper.admin-table-wrapper table th .nav-divider {
        display: none;
    }
    #main-table-wrapper.admin-table-wrapper table th .limit {
        margin-bottom: 1rem;
    }
    table .tab a {
        padding: 0.3rem 0.9rem;
    }
    #main-table-wrapper th.category-total p {
        font-size: 13px;
    }
    #main-table-wrapper table thead tr.controls.with-filters {
        align-items: flex-start;
    }
    #main-table-wrapper table th select,
    input.table-input {
        height: 28px;
        font-size: 12px;
        padding: 0 1rem;
    }
    #main-table-wrapper table th select {
        background-position: right 0.7em top 50%, 0 0;
    }
    .search-input-wrapper button {
        padding: 2px;
        margin-right: 0.15rem;
    }
    #main-table-wrapper table th input.search.admin {
        max-width: 150px;
        padding: 0 1.5rem 0 1rem;
    }
    #main-table-wrapper table th select.table-input {
        max-width: 120px;
    }
    #main-table-wrapper table th button.table {
        height: 28px;
    }
    #main-table-wrapper table th input::placeholder {
        font-size: 12px;
    }
    #main-table-wrapper table tr.controls .checkbox {
        font-size: 13px;
    }
    #main-table-wrapper table button.nav {
        margin-right: 0.5rem;
    }
    #main-table-wrapper table .nav-divider {
        height: 18px;
        margin: 0 1rem;
    }
    #main-table-wrapper table tbody td.right-fade:after {
        width: 14px;
    }
    #main-table-wrapper table tr.edit td {
        padding: 1.25rem 1rem;
    }
    #main-table-wrapper table tr.edit label {
        margin: 0 0.25rem 0.5rem;
    }
    #main-table-wrapper table tr.edit input {
        height: 38px;
        padding: 0 1rem;
        font-size: 13px;
    }
    #main-table-wrapper table tr.edit input,
    #main-table-wrapper table tr.edit input+p {
        width: 200px;
    }
    #main-table-wrapper table tr.edit input[name="target"],
    #main-table-wrapper table tr.edit input[name="description"],
    #main-table-wrapper table tr.edit input[name="target"]+p,
    #main-table-wrapper table tr.edit input[name="description"]+p {
        width: 320px;
    }
    #main-table-wrapper table tr.edit button {
        height: 32px;
        margin-right: 0.5rem;
    }
    #main-table-wrapper table tr.edit td.loading svg {
        width: 14px;
        height: 14px;
    }
    #main-table-wrapper table tr.edit form .response p {
        margin: 1rem 0 0;
    }
    .dialog .ban-checklist label {
        margin: 0.5rem 1rem 0.5rem 0;
    }
    footer {
        padding: 0.75rem 0;
        font-size: 12px;
    }
    footer button.link {
        font-size: 12px;
    }
    /* Mobile build status */
    .footer-content {
        gap: 8px;
    }
    .footer-links {
        font-size: 12px;
        text-align: center;
        line-height: 1.4;
    }
    .build-link {
        padding: 3px 6px;
        font-size: 10px;
    }
    .build-text {
        font-size: 10px;
    }
    .build-icon svg {
        width: 10px;
        height: 10px;
    }
    h1.settings-welcome {
        font-size: 18px;
    }
    .add-domain-wrapper {
        margin: 1rem 0 1rem;
    }
    .add-domain-wrapper>.spinner {
        width: 18px;
        margin: 0.5rem 0 0 0.5rem;
    }
    form#add-domain {
        margin-top: 0.75rem;
    }
    form#add-domain button {
        margin-right: 0.5rem
    }
    .stats-info {
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
    }
    .stats-info h2 {
        font-size: 18px;
        margin-bottom: 0.25rem;
    }
    .stats-info p {
        font-size: 11px;
        line-break: anywhere;
    }
    .stats-head {
        padding: 0rem 1rem;
    }
    .stats-head p {
        font-size: 0.9rem;
    }
    .stats-nav button {
        margin-right: 0.5rem;
    }
    .stats-period {
        padding: 0.5rem 1rem;
    }
    .stats-period h2 {
        font-size: 18px;
        margin: 0.5rem 0 0;
    }
    p.last-update {
        font-size: 12px;
    }
    #stats canvas {
        margin: 1rem 0;
    }
    .stats-columns-wrapper {
        flex-direction: column;
    }
    .stats-columns-wrapper>div {
        flex-basis: 100%;
    }
    #notfound h2 {
        font-size: 20px;
    }
    #report form {
        margin-top: 1.5rem;
    }
    #report form .inputs-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }
    #report form button {
        margin: 0.75rem 0 0.2rem 0;
    }
    #reset-password form .inputs-wrapper {
        flex-direction: column;
        align-items: flex-start;
        margin-top: 1rem;
    }
    #reset-password form label {
        flex-basis: 0;
        width: 280px;
    }
    #reset-password form button {
        margin: 0.75rem 0 0.2rem 0;
    }
    #new-password form label {
        margin-bottom: 1.5rem;
    }
    #new-password form input {
        height: 58px;
        margin-top: 0.75rem;
        padding: 0 2rem;
        font-size: 15px;
    }
    #new-password form button {
        height: 44px;
    }
    .verify-page h2,
    .verify-page h3 {
        display: flex;
        flex-direction: column;
    }
    #protected form {
        margin-top: 0.5rem;
    }
    #protected form .inputs-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }
    #protected form label {
        flex-basis: 0;
        width: 280px;
    }
    #protected form button {
        margin: 0.75rem 0 0.2rem 0;
    }
}

@media only screen and (max-width: 640px) {
    table tr.loading-placeholder {
        justify-content: flex-start;
    }
    .inputs {
        flex-direction: column;
        margin-bottom: 0.75rem;
    }
    .inputs label {
        margin: 0 0 0.75rem;
    }
    .inputs label:last-child {
        margin: 0;
    }
    .advanced-input-wrapper {
        flex-direction: column;
        margin-bottom: 0;
    }
    .advanced-input-wrapper label {
        width: 100%;
        margin-bottom: 0.75rem;
        padding-right: 0;
    }
    .advanced-input-wrapper label input,
    .advanced-input-wrapper label select {
        margin-top: 0.5rem;
    }
    form#add-domain .spinner {
        width: 18px;
    }
    #apikey-wrapper {
        max-width: 100%;
    }
    #apikey p {
        font-size: 0.85rem;
    }
    #apikey .clipboard {
        width: 22px;
        height: 22px;
    }
}


/* Extra small screens - Mobile portrait */

@media only screen and (max-width: 480px) {
    header {
        padding: 8px 12px;
        min-height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
    }
    header a.logo {
        font-size: 16px;
    }
    header .logo img {
        width: 18px;
        height: 18px;
        margin-right: 6px;
    }
    header nav {
        margin-left: auto;
    }
    header nav ul {
        gap: 0.2rem;
    }
    header nav ul li a.button {
        height: 28px;
        padding: 0 0.4rem;
        font-size: 10px;
        white-space: nowrap;
    }
    header nav ul li a.nav {
        font-size: 10px;
        padding: 0.15rem 0.3rem;
    }
    /* Ensure main content doesn't overflow */
    main {
        padding: 0 0.75rem;
        margin-top: 0.5rem;
    }
    main form input#target {
        padding: 0 70px 0 20px;
        font-size: 16px;
        height: 60px;
    }
    main form input#target::placeholder {
        font-size: 14px;
    }
    main form button.submit {
        right: 0;
        top: 12px;
        margin: 0 1rem 0;
    }
    /* Fix advanced options on small screens */
    .advanced-input-wrapper {
        flex-direction: column;
        gap: 0.5rem;
    }
    .advanced-input-wrapper label {
        width: 100%;
        padding-right: 0;
        margin-bottom: 0.5rem;
    }
    /* Ensure consistent link text sizing on extra small screens */
    #main-table-wrapper table td.original-url a,
    #main-table-wrapper table td.short-link a {
        font-size: 13px !important;
        line-height: 1.3;
        word-break: break-all;
        overflow-wrap: break-word;
        text-align: left;
    }
}


/* Ensure metadata fields don't overflow */

input.table-input[name*="meta_"],
textarea.table-input[name*="meta_"] {
    min-width: 150px;
    max-width: 100%;
}


/* Table improvements for very small screens */

#main-table-wrapper {
    padding: 0 0.5rem;
    margin: 2rem 0 3rem;
}

#main-table-wrapper table th,
#main-table-wrapper table td {
    padding: 0.75rem 0.5rem;
}


/* Ensure table doesn't cause horizontal scroll */

table {
    min-width: 100%;
    overflow-x: auto;
}


/* Better button spacing in table actions */

#main-table-wrapper table .actions a.button,
#main-table-wrapper table .actions button {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}


}