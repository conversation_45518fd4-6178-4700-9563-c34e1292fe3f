<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">

  <!-- PRIORITY: Primary meta tags (must come first) -->
  <title>{{meta_title}}</title>
  <meta name="title" content="{{meta_title}}">
  <meta name="description" content="{{meta_description}}">

  <!-- PRIORITY: Open Graph meta tags (Facebook, LinkedIn, etc.) -->
  <meta property="og:title" content="{{meta_title}}">
  <meta property="og:description" content="{{meta_description}}">
  <meta property="og:image" content="{{meta_image}}">
  <meta property="og:image:secure_url" content="{{meta_image}}">
  <meta property="og:url" content="{{meta_url}}">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="{{meta_title}}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="{{meta_title}}">
  <meta property="og:image:type" content="image/jpeg">
  <meta property="og:locale" content="en_US">

  <!-- PRIORITY: Twitter Card meta tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{meta_title}}">
  <meta name="twitter:description" content="{{meta_description}}">
  <meta name="twitter:image" content="{{meta_image}}">
  <meta name="twitter:image:alt" content="{{meta_title}}">
  <meta name="twitter:url" content="{{meta_url}}">
  <meta name="twitter:domain" content="{{domain}}">

  <!-- Cache control for social media crawlers -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <meta name="keywords" content="{{meta_title}}, link, share">

  <!-- LinkedIn specific -->
  <meta name="linkedin:owner" content="{{meta_title}}">

  <!-- WhatsApp and Telegram specific -->
  <meta name="telegram:channel" content="{{meta_title}}">

  <!-- iOS and iMessage specific meta tags -->
  <meta name="apple-mobile-web-app-title" content="{{meta_title}}">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="format-detection" content="telephone=no">
  <meta name="apple-touch-fullscreen" content="yes">
  <link rel="apple-touch-icon" href="{{meta_image}}">
  <link rel="apple-touch-icon-precomposed" href="{{meta_image}}">

  <!-- iMessage App Links -->
  <meta property="al:ios:url" content="{{meta_url}}">
  <meta property="al:ios:app_name" content="{{meta_title}}">
  <meta property="al:web:url" content="{{meta_url}}">

  <!-- Additional meta tags for better compatibility -->
  <meta name="author" content="{{meta_title}}">
  <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
  <meta name="googlebot" content="noindex, nofollow, noarchive, nosnippet">
  <meta name="bingbot" content="noindex, nofollow, noarchive, nosnippet">

  <!-- Prevent caching by social media platforms -->
  <meta name="revisit-after" content="1 day">
  <meta http-equiv="last-modified" content="{{timestamp}}">

  <!-- Conditional redirect - only for non-zero delays -->
  {{#if redirect_delay}}
  <meta http-equiv="refresh" content="{{redirect_delay}};url={{target}}">
  {{/if}}

  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
      color: #333;
      text-align: center;
    }
    .container {
      max-width: 600px;
      margin: 50px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .preview-image {
      width: 100%;
      height: 315px; /* 1.91:1 aspect ratio for social media */
      object-fit: cover;
      object-position: center;
      background: #eee;
      display: block;
    }

    /* iOS specific image optimization */
    @media screen and (-webkit-min-device-pixel-ratio: 2) {
      .preview-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
    }
    .content {
      padding: 20px;
    }
    .title {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 10px 0;
      color: #1a1a1a;
    }
    .description {
      font-size: 16px;
      line-height: 1.5;
      color: #666;
      margin: 0 0 20px 0;
    }
    .redirect-info {
      font-size: 14px;
      color: #888;
      margin-top: 20px;
    }
    .btn {
      display: inline-block;
      padding: 12px 24px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: 500;
    }
    .btn:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <div class="container">
    {{#if meta_image}}
      <img src="{{meta_image}}" alt="{{meta_title}}" class="preview-image" onerror="this.style.display='none'">
    {{/if}}
    <div class="content">
      <h1 class="title">{{meta_title}}</h1>
      <p class="description">{{meta_description}}</p>
      <a href="{{target}}" class="btn">Continue to Link</a>
      <p class="redirect-info">You will be redirected automatically...</p>
    </div>
  </div>

  {{#if redirect_delay}}
  <script>
    // Fallback redirect for browsers that don't support meta refresh
    setTimeout(function() {
      window.location.href = "{{target}}";
    }, {{redirect_delay}}000);
  </script>
  {{/if}}
</body>
</html>
