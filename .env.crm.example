# 🚀 CRM DATABASE CONFIGURATION FOR RENDER
# Copy this to your .env file and update with your actual values

# CRM Database Configuration (Separate from main Kutt database)
CRM_DB_CLIENT=pg
CRM_DB_HOST=dpg-d0qvadbipnbc73eppoh0-a.virginia-postgres.render.com
CRM_DB_PORT=5432
CRM_DB_NAME=b2b_crm
CRM_DB_USER=b2b_admin
CRM_DB_PASSWORD=acKb8qN3utIVlhDOTnZqmgehb7X04t0Q
CRM_DB_SSL=true
CRM_DB_POOL_MIN=2
CRM_DB_POOL_MAX=10

# Alternative: Use full connection URL
# CRM_DATABASE_URL=postgresql://b2b_admin:<EMAIL>/b2b_crm

# 📱 TWILIO CONFIGURATION (for SMS marketing)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
TWILIO_MESSAGING_SERVICE_SID=your_messaging_service_sid

# 🎯 CRM FEATURES
CRM_ENABLED=true
SMS_MARKETING_ENABLED=true
EVENT_TRACKING_ENABLED=true
ANALYTICS_ENABLED=true

# 🔒 COMPLIANCE SETTINGS
GDPR_COMPLIANCE_ENABLED=true
TCPA_COMPLIANCE_ENABLED=true
DATA_RETENTION_DAYS=2555  # 7 years for compliance

# 📊 ANALYTICS CONFIGURATION
COHORT_ANALYSIS_ENABLED=true
ATTRIBUTION_MODELING_ENABLED=true
CUSTOMER_JOURNEY_TRACKING=true

# 🚀 PERFORMANCE SETTINGS
CRM_CACHE_TTL=300  # 5 minutes
BATCH_SIZE=1000
EVENT_PROCESSING_DELAY=5000  # 5 seconds
