<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <meta name="theme-color" content="#ffffff">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-touch-fullscreen" content="yes">
    <title>{{title}} - BOUNCE2BOUNCE</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="/css/themes.css" as="style">
    <link rel="preload" href="/css/navigation.css" as="style">
    <link rel="preload" href="/js/theme-manager.js" as="script">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- LineIcons for PlainAdmin -->
    <link href="https://cdn.lineicons.com/4.0/lineicons.css" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/themes.css">
    <link rel="stylesheet" href="/css/navigation.css">
    <link rel="stylesheet" href="/css/plainadmin-dashboard.css">
    <link rel="stylesheet" href="/css/mobile-performance.css">
    <link rel="stylesheet" href="/css/mobile-responsive.css">
    <link rel="stylesheet" href="/css/styles.css">
    {{#if additionalCSS}}
        {{#each additionalCSS}}
            <link rel="stylesheet" href="{{this}}">
        {{/each}}
    {{/if}}

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="BOUNCE2BOUNCE">
</head>
<body>
    <!-- Laylo-Style Layout with Sidebar -->
    <div class="laylo-layout">
        <!-- Mobile Navigation Overlay -->
        <div class="laylo-mobile-overlay" id="mobileOverlay"></div>

        <!-- Mobile Navigation Menu -->
        <nav class="laylo-mobile-nav" id="mobileNav">
            <!-- Mobile Nav Header -->
            <div class="laylo-mobile-nav-header">
                <button class="laylo-mobile-close" id="mobileClose">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
                <button class="laylo-mobile-share">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                        <polyline points="16,6 12,2 8,6"/>
                        <line x1="12" y1="2" x2="12" y2="15"/>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation Items -->
            <div class="laylo-mobile-nav-items">
                <a href="/dashboard" class="laylo-mobile-nav-item {{#if (eq currentPage 'dashboard')}}active{{/if}}">
                    <svg class="laylo-mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span class="laylo-mobile-nav-text">Home</span>
                </a>

                <a href="/profile" class="laylo-mobile-nav-item {{#if (eq currentPage 'profile')}}active{{/if}}">
                    <svg class="laylo-mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span class="laylo-mobile-nav-text">Profile</span>
                </a>

                <a href="/messages" class="laylo-mobile-nav-item {{#if (eq currentPage 'messages')}}active{{/if}}">
                    <svg class="laylo-mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                    </svg>
                    <span class="laylo-mobile-nav-text">Messages</span>
                </a>

                <a href="/fans" class="laylo-mobile-nav-item {{#if (eq currentPage 'fans')}}active{{/if}}">
                    <svg class="laylo-mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                    <span class="laylo-mobile-nav-text">Fans</span>
                </a>

                <a href="/settings" class="laylo-mobile-nav-item {{#if (eq currentPage 'settings')}}active{{/if}}">
                    <svg class="laylo-mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    <span class="laylo-mobile-nav-text">Settings</span>
                </a>
            </div>

            <!-- Mobile User Section -->
            <div class="laylo-mobile-user-section">
                <div class="laylo-mobile-user-info">
                    <div class="laylo-mobile-user-avatar">
                        {{#if user.profile_picture}}
                            <img src="{{user.profile_picture}}" alt="Profile" class="laylo-mobile-user-image">
                        {{else}}
                            <div class="laylo-mobile-user-logo">
                                <span class="laylo-mobile-logo-text">
                                    {{#if user.first_name}}
                                        {{substring user.first_name 0 1}}{{#if user.last_name}}{{substring user.last_name 0 1}}{{/if}}
                                    {{else}}
                                        B2B
                                    {{/if}}
                                </span>
                            </div>
                        {{/if}}
                    </div>
                    <div class="laylo-mobile-user-details">
                        <div class="laylo-mobile-user-name">
                            {{#if user.first_name}}
                                {{user.first_name}}{{#if user.last_name}} {{user.last_name}}{{/if}}
                            {{else if user.username}}
                                {{user.username}}
                            {{else}}
                                BOUNCE2BOUNCE
                            {{/if}}
                        </div>
                        <div class="laylo-mobile-user-phone">
                            {{#if user.phone}}
                                {{user.phone}}
                            {{else}}
                                {{user.email}}
                            {{/if}}
                        </div>
                    </div>
                </div>

                <!-- Mobile Drop Button -->
                <a href="/drops/create" class="laylo-mobile-drop-btn">
                    <span class="laylo-mobile-drop-icon">+</span>
                    Create a Drop
                </a>
            </div>
        </nav>

        <!-- Desktop Sidebar Navigation -->
        <nav class="laylo-sidebar">
            <!-- Laylo Logo -->
            <div class="laylo-logo">
                <div class="laylo-logo-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M8 12h8M12 8v8" stroke="white" stroke-width="2"/>
                    </svg>
                </div>
                <span class="laylo-logo-text">Laylo</span>
            </div>

            <!-- Create a Drop Button -->
            <a href="/drops/create" class="laylo-create-btn">
                <span class="laylo-create-icon">+</span>
                Create a Drop
            </a>

            <!-- Navigation Menu -->
            <div class="laylo-nav-menu">
                <a href="/dashboard" class="laylo-nav-item {{#if (eq currentPage 'dashboard')}}active{{/if}}">
                    <svg class="laylo-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span class="laylo-nav-text">Home</span>
                </a>

                <a href="/profile" class="laylo-nav-item {{#if (eq currentPage 'profile')}}active{{/if}}">
                    <svg class="laylo-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span class="laylo-nav-text">Profile</span>
                </a>

                <a href="/messages" class="laylo-nav-item {{#if (eq currentPage 'messages')}}active{{/if}}">
                    <svg class="laylo-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                    </svg>
                    <span class="laylo-nav-text">Messages</span>
                </a>

                <a href="/fans" class="laylo-nav-item {{#if (eq currentPage 'fans')}}active{{/if}}">
                    <svg class="laylo-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                    <span class="laylo-nav-text">Fans</span>
                </a>
            </div>

            <!-- Bottom Section -->
            <div class="laylo-sidebar-bottom">
                <a href="/settings" class="laylo-nav-item {{#if (eq currentPage 'settings')}}active{{/if}}">
                    <svg class="laylo-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    <span class="laylo-nav-text">Settings</span>
                </a>

                <!-- User Info Section -->
                <div class="laylo-user-info">
                    <div class="laylo-user-avatar">
                        {{#if user.profile_picture}}
                            <img src="{{user.profile_picture}}" alt="Profile" class="laylo-user-image">
                        {{else}}
                            <span class="laylo-user-initials">
                                {{#if user.first_name}}
                                    {{substring user.first_name 0 1}}{{#if user.last_name}}{{substring user.last_name 0 1}}{{/if}}
                                {{else}}
                                    B2B
                                {{/if}}
                            </span>
                        {{/if}}
                    </div>
                    <div class="laylo-user-details">
                        <div class="laylo-user-name">
                            {{#if user.first_name}}
                                {{user.first_name}}{{#if user.last_name}} {{user.last_name}}{{/if}}
                            {{else if user.username}}
                                {{user.username}}
                            {{else}}
                                BOUNCE2BOUNCE
                            {{/if}}
                        </div>
                        <div class="laylo-user-url">
                            {{#if user.username}}
                                bounce2bounce.com/{{user.username}}
                            {{else}}
                                bounce2bounce.com/user
                            {{/if}}
                        </div>
                    </div>
                </div>

                <!-- Credits Info -->
                <div class="laylo-credits">
                    <div class="laylo-credits-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                        </svg>
                    </div>
                    <div class="laylo-credits-text">
                        <div class="laylo-credits-count">1,070 credits used</div>
                        <div class="laylo-credits-info">Save up to 12% when you purchase or subscribe to credits</div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="laylo-main">
            <!-- Header with Mobile Trigger -->
            <div class="laylo-main-header">
                <!-- Mobile Navigation Trigger -->
                <div class="laylo-mobile-trigger" id="mobileTrigger"
                     onclick="handleMobileTriggerClick(event)"
                     ontouchstart="handleMobileTriggerTouch(event)">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </div>
            </div>

            {{{body}}}
        </main>
    </div>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>



    <!-- Scripts -->
    <script src="/scripts/main.js"></script>
    <script src="/js/theme-manager.js"></script>
    <script src="/js/navigation.js"></script>
    <script src="/js/mobile-navigation.js"></script>
    {{#if additionalJS}}
        {{#each additionalJS}}
            <script src="{{this}}"></script>
        {{/each}}
    {{/if}}

    <!-- Ultra-Responsive Mobile Navigation Inline Handlers -->
    <script>
        // INSTANT mobile navigation handlers - zero delays
        let lastTouchTime = 0;
        let touchInProgress = false;

        function handleMobileTriggerClick(event) {
            const timeSinceTouch = Date.now() - lastTouchTime;

            // Only handle click if no recent touch (desktop/mouse users)
            if (timeSinceTouch > 300) {
                console.log('🚀 INSTANT inline click');
                event.preventDefault();
                event.stopPropagation();
                toggleMobileNavigationInstant();
            }
        }

        function handleMobileTriggerTouch(event) {
            console.log('🚀 INSTANT inline touch');
            event.preventDefault();
            event.stopPropagation();

            touchInProgress = true;
            lastTouchTime = Date.now();

            // INSTANT visual feedback
            const trigger = event.currentTarget;
            trigger.style.transform = 'scale(0.96)';
            trigger.style.opacity = '0.7';
            trigger.style.transition = 'none';

            // INSTANT haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(25);
            }

            // Toggle navigation immediately on touchend
            const handleTouchEnd = (e) => {
                e.preventDefault();
                e.stopPropagation();

                if (touchInProgress) {
                    // Reset visual feedback
                    trigger.style.transform = '';
                    trigger.style.opacity = '';
                    trigger.style.transition = '';

                    // Toggle navigation INSTANTLY
                    toggleMobileNavigationInstant();

                    touchInProgress = false;
                }

                // Remove the touchend listener
                trigger.removeEventListener('touchend', handleTouchEnd);
            };

            // Add touchend listener for this specific touch
            trigger.addEventListener('touchend', handleTouchEnd, { passive: false, once: true });
        }

        function toggleMobileNavigationInstant() {
            console.log('🚀 INSTANT toggleMobileNavigation');
            const overlay = document.getElementById('mobileOverlay');
            const nav = document.getElementById('mobileNav');

            console.log('🚀 Elements found:', {
                overlay: !!overlay,
                nav: !!nav,
                overlayClasses: overlay ? overlay.className : 'not found',
                navClasses: nav ? nav.className : 'not found'
            });

            if (overlay && nav) {
                const isActive = nav.classList.contains('active');
                console.log('🚀 Current state:', isActive ? 'open' : 'closed');

                if (isActive) {
                    // Close navigation INSTANTLY
                    overlay.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                    console.log('🚀 Navigation closed INSTANTLY');
                } else {
                    // Open navigation INSTANTLY
                    overlay.classList.add('active');
                    nav.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                    console.log('🚀 Navigation opened INSTANTLY');

                    // Force visibility for debugging
                    nav.style.transform = 'translateY(0)';
                    nav.style.zIndex = '9999';
                    overlay.style.opacity = '1';
                    overlay.style.visibility = 'visible';
                    overlay.style.zIndex = '9998';
                }

                console.log('🚀 After toggle:', {
                    overlayClasses: overlay.className,
                    navClasses: nav.className,
                    overlayStyle: overlay.style.cssText,
                    navStyle: nav.style.cssText
                });
            } else {
                console.error('🚀 Mobile navigation elements not found');
                console.log('🚀 Available elements:', document.querySelectorAll('[id*="mobile"]'));
            }
        }

        // Legacy function for compatibility
        function toggleMobileNavigation() {
            toggleMobileNavigationInstant();
        }
    </script>

    <!-- Theme initialization script -->
    <script>
        // Prevent flash of unstyled content
        document.addEventListener('DOMContentLoaded', function() {
            // Remove any loading states
            document.body.classList.remove('loading');

            // Initialize active navigation states
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('[data-nav]');

            navLinks.forEach(link => {
                const navType = link.getAttribute('data-nav');
                const href = link.getAttribute('href');

                if (href && isActivePath(currentPath, href, navType)) {
                    link.classList.add('active');
                }
            });

            function isActivePath(currentPath, linkPath, navType) {
                // Special handling for different nav types
                switch (navType) {
                    case 'home':
                        return currentPath === '/' || currentPath === '/dashboard';
                    case 'fans':
                        return currentPath.includes('/analytics');
                    default:
                        return currentPath.startsWith(linkPath);
                }
            }
        });

        // Handle navigation events
        document.addEventListener('navigationchange', function(e) {
            console.log('Navigation state changed:', e.detail);
        });

        document.addEventListener('themechange', function(e) {
            console.log('Theme changed:', e.detail);
        });
    </script>
</body>
</html>
