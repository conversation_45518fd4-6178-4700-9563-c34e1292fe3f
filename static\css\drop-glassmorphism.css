/* ===== GLASSMORPHISM DROP LANDING PAGE STYLING ===== */


/* Modern glass effect styling for drop landing pages */


/* Completely separate from dashboard TailwindCSS styling */


/* ===== GLASSMORPHISM FOUNDATION ===== */

.glassmorphism-container {
    /* Enhanced backdrop blur with multiple layers */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    /* Glass transparency with subtle tinting */
    background: rgba(255, 255, 255, 0.15);
    /* Modern border with glass effect */
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    /* Sophisticated shadow system */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glassmorphism-container:hover {
    /* Enhanced glass effect on hover */
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 6px 20px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.4);
}


/* ===== GLASSMORPHISM FORM ELEMENTS ===== */

.glass-input {
    /* Glass input field styling */
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    /* Typography and spacing */
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.8);
    /* Smooth transitions */
    transition: all 0.3s ease;
    /* Remove default styling */
    outline: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-input:focus {
    /* Enhanced focus state */
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.glass-input::placeholder {
    color: rgba(0, 0, 0, 0.5);
    font-weight: 400;
}


/* ===== GLASSMORPHISM BUTTON STYLING ===== */

.glass-button {
    /* Glass button foundation */
    position: relative;
    overflow: hidden;
    /* Glass effect */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    /* Border and shape */
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    /* Typography */
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.025em;
    /* Spacing and sizing */
    padding: 18px 32px;
    min-height: 56px;
    /* Advanced shadow system */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Cursor and interaction */
    cursor: pointer;
    user-select: none;
}

.glass-button::before {
    /* Shimmer effect overlay */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
}

.glass-button:hover::before {
    /* Shimmer animation on hover */
    left: 100%;
}

.glass-button:hover {
    /* Enhanced hover state */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18), 0 6px 20px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.5), inset 0 -1px 0 rgba(0, 0, 0, 0.08);
}

.glass-button:active {
    /* Active press state */
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15), 0 3px 12px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glass-button .button-text {
    /* Button text styling */
    position: relative;
    z-index: 2;
    color: inherit;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}


/* ===== GLASSMORPHISM CARD STYLING ===== */

.glass-card {
    /* Main content card with glass effect */
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(25px) saturate(200%);
    -webkit-backdrop-filter: blur(25px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 24px;
    /* Advanced shadow system */
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.1), 0 8px 32px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Smooth transitions */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    /* Positioning and overflow */
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    /* Subtle gradient overlay */
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
    z-index: 1;
}


/* ===== GLASSMORPHISM HEADER STYLING ===== */

.glass-header {
    /* Header with subtle glass effect */
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    /* Positioning */
    position: relative;
    z-index: 10;
}


/* ===== GLASSMORPHISM FOOTER STYLING ===== */

.glass-footer {
    /* Footer with minimal glass effect */
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}


/* ===== GLASSMORPHISM PLATFORM LINKS ===== */

.platform-links {
    /* Platform links container */
    margin: 32px 0;
    padding: 24px;
}

.platform-links-grid {
    /* Grid layout for platform links */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
    max-width: 600px;
    margin: 0 auto;
}

.platform-link {
    /* Individual platform link styling */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* Glass button base styling inherited */
    padding: 14px 20px;
    min-height: 48px;
    /* Typography */
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.8);
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.platform-link:hover {
    /* Enhanced hover state for platform links */
    color: rgba(0, 0, 0, 0.9);
    text-decoration: none;
}

.platform-icon {
    /* Platform icon styling */
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.platform-link:hover .platform-icon {
    opacity: 1;
}


/* ===== RESPONSIVE GLASSMORPHISM ===== */

@media (max-width: 768px) {
    .glassmorphism-container {
        /* Reduced blur for mobile performance */
        backdrop-filter: blur(15px) saturate(150%);
        -webkit-backdrop-filter: blur(15px) saturate(150%);
        border-radius: 16px;
    }
    .glass-card {
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
        border-radius: 20px;
    }
    .glass-button {
        border-radius: 12px;
        padding: 16px 28px;
        min-height: 52px;
    }
    .glass-input {
        border-radius: 10px;
        padding: 14px 18px;
    }
}


/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

@media (prefers-reduced-motion: reduce) {
    .glassmorphism-container,
    .glass-input,
    .glass-button,
    .glass-card {
        transition: none;
    }
    .glass-button::before {
        display: none;
    }
}


/* ===== HIGH CONTRAST MODE SUPPORT ===== */

@media (prefers-contrast: high) {
    .glassmorphism-container,
    .glass-card {
        background: rgba(255, 255, 255, 0.9);
        border-color: rgba(0, 0, 0, 0.3);
    }
    .glass-input {
        background: rgba(255, 255, 255, 0.95) !important;
        border-color: rgba(0, 0, 0, 0.4);
    }
    .glass-button {
        background: rgba(255, 255, 255, 0.9);
        border-color: rgba(0, 0, 0, 0.4);
    }
}