<section id="drops-section" class="drops-section">
  <div class="section-header">
    <div class="section-title">
      <h2>Drops</h2>
      <p class="section-description">Create landing pages to capture leads and build your audience</p>
    </div>
    <div class="section-actions">
      <button type="button" class="button primary" id="create-drop-btn">
        <span>{{> icons/plus}}</span>
        Create Drop
      </button>
    </div>
  </div>

  <div id="drops-container" class="drops-container">
    {{#if drops}}
      <div class="drops-grid">
        {{#each drops}}
          {{> drops/card this}}
        {{/each}}
      </div>
    {{else}}
      <div class="empty-state">
        <div class="empty-icon">
          {{> icons/drop}}
        </div>
        <h3>No drops yet</h3>
        <p>Create your first drop to start capturing leads and building your audience</p>
        <button type="button" class="button primary" id="create-first-drop-btn">
          <span>{{> icons/plus}}</span>
          Create Your First Drop
        </button>
      </div>
    {{/if}}
  </div>

  <!-- Loading state -->
  <div id="drops-loading" class="drops-loading" style="display: none;">
    <div class="loading-spinner">
      {{> icons/spinner}}
    </div>
    <p>Loading drops...</p>
  </div>
</section>


