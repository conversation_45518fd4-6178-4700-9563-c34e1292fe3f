<!-- Modern Drop Edit Page -->
<div class="space-y-6">
    <!-- Header with <PERSON> Button -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <button type="button" onclick="window.history.back()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back
            </button>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Drop</h1>
                <p class="text-sm text-gray-500">{{drop.title}}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex items-center space-x-3">
            <a href="/drop/{{drop.slug}}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                View Live
            </a>

            <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">Status:</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{#if drop.is_active}}bg-green-100 text-green-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                    {{#if drop.is_active}}Active{{else}}Inactive{{/if}}
                </span>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Form Section (2/3 width on desktop) -->
        <div class="lg:col-span-2">
            <!-- Tab Navigation -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="page">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Page
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="messaging">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                Messaging
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="advanced">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Advanced
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="sharing">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                                Sharing
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="analytics">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
                                </svg>
                                Analytics
                            </div>
                        </button>
                    </nav>
                </div>

                <!-- Form Content -->
                <form id="drop-edit-form" class="drop-form">
                    <!-- PAGE TAB CONTENT -->
                    <div class="tab-content active p-6 space-y-6" data-tab-content="page">
                        <!-- Details Section -->
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Details</h3>
                                <p class="text-sm text-gray-500 mb-4">Configure the basic information for your drop page</p>

                                <div class="space-y-4">
                                    <div>
                                        <label for="edit-title" class="block text-sm font-medium text-gray-700">Title *</label>
                                        <input type="text" id="edit-title" name="title" required maxlength="255" value="{{drop.title}}" placeholder="Enter drop title" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <div>
                                        <label for="edit-description" class="block text-sm font-medium text-gray-700">Description</label>
                                        <textarea id="edit-description" name="description" rows="4" placeholder="Describe what this drop is about" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">{{drop.description}}</textarea>
                                    </div>

                                    <div>
                                        <label for="edit-slug" class="block text-sm font-medium text-gray-700">URL Slug</label>
                                        <div class="mt-1 flex rounded-md shadow-sm">
                                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                                {{domain}}/drop/
                                            </span>
                                            <input type="text" id="edit-slug" name="slug" value="{{drop.slug}}" class="flex-1 block w-full rounded-none rounded-r-md border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Styling Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Styling</h3>
                                <p class="text-sm text-gray-500 mb-4">Customize the appearance of your drop page</p>

                                <div class="space-y-6">
                                    <!-- Cover Image -->
                                    <div>
                                        <label for="edit-cover-image" class="block text-sm font-medium text-gray-700">Cover Image URL</label>
                                        <input type="url" id="edit-cover-image" name="cover_image" value="{{drop.cover_image}}" placeholder="https://example.com/image.jpg" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <!-- Background Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Background</h4>
                                        <div class="space-y-4">
                                            <div>
                                                <label for="edit-background-type" class="block text-sm font-medium text-gray-700">Background Type</label>
                                                <select id="edit-background-type" name="background_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <option value="gradient" {{#if (eq drop.background_type "gradient")}}selected{{/if}}>Gradient</option>
                                                    <option value="solid" {{#if (eq drop.background_type "solid")}}selected{{/if}}>Solid Color</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="edit-background-color" class="block text-sm font-medium text-gray-700">Background Color</label>
                                                <input type="color" id="edit-background-color" name="background_color" value="{{drop.background_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Card Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Card Background</h4>
                                        <div>
                                            <label for="edit-card-background-type" class="block text-sm font-medium text-gray-700">Card Style</label>
                                            <select id="edit-card-background-type" name="card_background_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                <option value="solid_white" {{#if (eq drop.card_background_type "solid_white")}}selected{{/if}}>Solid White</option>
                                                <option value="solid_dark" {{#if (eq drop.card_background_type "solid_dark")}}selected{{/if}}>Solid Dark</option>
                                                <option value="translucent_light" {{#if (eq drop.card_background_type "translucent_light")}}selected{{/if}}>Translucent Light</option>
                                                <option value="translucent_dark" {{#if (eq drop.card_background_type "translucent_dark")}}selected{{/if}}>Translucent Dark</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Text Colors -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Text Colors</h4>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div>
                                                <label for="edit-title-color" class="block text-sm font-medium text-gray-700">Title Color</label>
                                                <input type="color" id="edit-title-color" name="title_color" value="{{drop.title_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>

                                            <div>
                                                <label for="edit-description-color" class="block text-sm font-medium text-gray-700">Subtitle Color</label>
                                                <input type="color" id="edit-description-color" name="description_color" value="{{drop.description_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Button Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Button Styling</h4>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div>
                                                <label for="edit-button-color" class="block text-sm font-medium text-gray-700">Button Background</label>
                                                <input type="color" id="edit-button-color" name="button_color" value="{{drop.button_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>

                                            <div>
                                                <label for="edit-button-text-color" class="block text-sm font-medium text-gray-700">Button Text Color</label>
                                                <input type="color" id="edit-button-text-color" name="button_text_color" value="{{drop.button_text_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Platform Links Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Platform Links</h3>
                                <p class="text-sm text-gray-500 mb-4">Add links to your social media and streaming platforms</p>

                                <div class="space-y-4">
                                    <div>
                                        <label for="edit-button-text" class="block text-sm font-medium text-gray-700">Button Text</label>
                                        <input type="text" id="edit-button-text" name="button_text" value="{{drop.button_text}}" placeholder="Get Notified" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-website-link" class="block text-sm font-medium text-gray-700">Website</label>
                                            <input type="url" id="edit-website-link" name="website_link" value="{{drop.website_link}}" placeholder="https://yourwebsite.com" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-instagram-link" class="block text-sm font-medium text-gray-700">Instagram</label>
                                            <input type="url" id="edit-instagram-link" name="instagram_link" value="{{drop.instagram_link}}" placeholder="https://instagram.com/yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-twitter-link" class="block text-sm font-medium text-gray-700">Twitter/X</label>
                                            <input type="url" id="edit-twitter-link" name="twitter_link" value="{{drop.twitter_link}}" placeholder="https://twitter.com/yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-spotify-link" class="block text-sm font-medium text-gray-700">Spotify URL</label>
                                            <input type="url" id="edit-spotify-link" name="spotify_link" value="{{drop.spotify_link}}" placeholder="https://open.spotify.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-apple-music-url" class="block text-sm font-medium text-gray-700">Apple Music URL</label>
                                            <input type="url" id="edit-apple-music-url" name="apple_music_url" value="{{drop.apple_music_url}}" placeholder="https://music.apple.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-youtube-link" class="block text-sm font-medium text-gray-700">YouTube URL</label>
                                            <input type="url" id="edit-youtube-link" name="youtube_link" value="{{drop.youtube_link}}" placeholder="https://youtube.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-soundcloud-url" class="block text-sm font-medium text-gray-700">SoundCloud URL</label>
                                            <input type="url" id="edit-soundcloud-url" name="soundcloud_url" value="{{drop.soundcloud_url}}" placeholder="https://soundcloud.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-tiktok-link" class="block text-sm font-medium text-gray-700">TikTok</label>
                                            <input type="url" id="edit-tiktok-link" name="tiktok_link" value="{{drop.tiktok_link}}" placeholder="https://tiktok.com/@yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Collection Settings -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Form Collection</h3>
                                <p class="text-sm text-gray-500 mb-4">Configure what information to collect from fans</p>

                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label for="edit-collect-email" class="text-sm font-medium text-gray-900">Collect Email</label>
                                            <p class="text-sm text-gray-500">Require email address for signups</p>
                                        </div>
                                        <input type="checkbox" id="edit-collect-email" name="collect_email" {{#if drop.collect_email}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label for="edit-collect-phone" class="text-sm font-medium text-gray-900">Collect Phone</label>
                                            <p class="text-sm text-gray-500">Require phone number for signups</p>
                                        </div>
                                        <input type="checkbox" id="edit-collect-phone" name="collect_phone" {{#if drop.collect_phone}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>

                            <!-- Status Control -->
                            <div class="border-t border-gray-200 pt-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">Drop Status</h3>
                                        <p class="text-sm text-gray-500">Control whether your drop is live and accessible to fans</p>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="edit-is-active" name="is_active" {{#if drop.is_active}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                        <label for="edit-is-active" class="ml-2 block text-sm text-gray-900">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MESSAGING TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="messaging">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Messaging</h3>
                            <p class="mt-1 text-sm text-gray-500">Configure email templates, SMS messages, and notification settings.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- ADVANCED TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="advanced">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Advanced Settings</h3>
                            <p class="mt-1 text-sm text-gray-500">Advanced configuration options and integrations.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- SHARING TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="sharing">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Sharing & QR Codes</h3>
                            <p class="mt-1 text-sm text-gray-500">Share your drop and generate QR codes for easy access.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- ANALYTICS TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="analytics">
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Drop Analytics</h3>
                                <p class="text-sm text-gray-500 mb-4">Track your drop's performance and fan engagement</p>
                            </div>

                            <!-- Stats Cards -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Page Views</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-views">{{drop.view_count}}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Total Fans</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-fans">{{drop.signup_count}}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-conversion">0%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Signups -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Fan Signups</h4>
                                <div id="recent-signups" class="space-y-2">
                                    <p class="text-sm text-gray-500">Loading recent signups...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex items-center justify-end space-x-3">
                <button type="button" id="cancel-btn" onclick="window.history.back()" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Cancel
                </button>
                <button type="submit" id="save-btn" form="drop-edit-form" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <span id="save-btn-text">Save Changes</span>
                    <svg id="save-btn-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Preview Section (1/3 width on desktop) -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg overflow-hidden sticky top-6">
                <!-- Preview Header -->
                <div class="px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-sm font-medium text-gray-900">Live Preview</h3>
                        <div class="flex items-center space-x-2">
                            <button type="button" id="preview-desktop" class="preview-mode-btn active inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                Desktop
                            </button>
                            <button type="button" id="preview-mobile" class="preview-mode-btn inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
                                </svg>
                                Mobile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Preview Content -->
                <div class="p-4">
                    <div id="preview-container" class="border border-gray-200 rounded-lg overflow-hidden">
                        <iframe id="preview-iframe" src="/drop/{{drop.slug}}?preview=true" class="w-full h-96 border-0"></iframe>
                    </div>

                    <!-- Preview Actions -->
                    <div class="mt-4 flex items-center justify-between text-xs text-gray-500">
                        <span>Updates automatically as you type</span>
                        <button type="button" onclick="refreshPreview()" class="text-primary-600 hover:text-primary-500">
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Drop Edit JavaScript - Enhanced Real-Time Preview System -->
<script>
// Global variables for preview elements
let previewIframe;
let previewDocument;
let previewTitle, previewDescription, previewButtonText, previewUrlSlug;
let previewCoverImage, previewCover;

// Form input elements
let titleInput, descriptionInput, slugInput, buttonTextInput, coverImageInput;
let backgroundColorInput, cardColorInput, titleColorInput, descriptionColorInput, buttonColorInput, formFieldColorInput;
let collectEmailToggle, collectPhoneToggle;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing modern drop edit system...');

    // Initialize form elements
    initializeFormElements();

    // Initialize tab functionality
    initializeTabSystem();

    // Initialize preview system
    initializePreviewSystem();

    // Initialize form submission
    initializeFormSubmission();

    // Initialize real-time updates
    initializeRealTimeUpdates();

    console.log('✅ Modern drop edit system initialized successfully');

    // Debug: Log all form fields and their values
    setTimeout(() => {
        console.log('🔍 FORM FIELDS DEBUG');
        console.log('='.repeat(40));

        const form = document.getElementById('drop-edit-form');
        if (form) {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            console.log('📋 All form fields and values:');
            Object.entries(data).forEach(([key, value]) => {
                console.log(`   ${key}: ${value}`);
            });

            // Check specific styling fields
            console.log('\n🎨 Styling fields specifically:');
            const stylingFields = ['background_type', 'card_background_type', 'background_color', 'button_color', 'button_text_color', 'title_color', 'description_color'];
            stylingFields.forEach(field => {
                const element = document.querySelector(`[name="${field}"]`);
                if (element) {
                    console.log(`   ✅ ${field}: ${element.value} (element found)`);
                } else {
                    console.log(`   ❌ ${field}: element not found`);
                }
            });
        } else {
            console.log('❌ Form not found');
        }
    }, 1000);
});

function initializeFormElements() {
    // Get all form input elements
    titleInput = document.getElementById('edit-title');
    descriptionInput = document.getElementById('edit-description');
    slugInput = document.getElementById('edit-slug');
    buttonTextInput = document.getElementById('edit-button-text');
    coverImageInput = document.getElementById('edit-cover-image');

    // Color inputs
    backgroundColorInput = document.getElementById('edit-background-color');
    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const cardBackgroundTypeSelect = document.getElementById('edit-card-background-type');
    titleColorInput = document.getElementById('edit-title-color');
    descriptionColorInput = document.getElementById('edit-description-color');
    buttonColorInput = document.getElementById('edit-button-color');
    const buttonTextColorInput = document.getElementById('edit-button-text-color');

    // Toggle inputs
    collectEmailToggle = document.getElementById('edit-collect-email');
    collectPhoneToggle = document.getElementById('edit-collect-phone');

    console.log('📝 Form elements initialized');
}

function initializeTabSystem() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => {
                btn.classList.remove('border-primary-500', 'text-primary-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-primary-500', 'text-primary-600');

            // Show corresponding content
            const targetContent = document.querySelector(`[data-tab-content="${targetTab}"]`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Load analytics if analytics tab is clicked
            if (targetTab === 'analytics') {
                loadAnalytics();
            }
        });
    });

    console.log('🗂️ Tab system initialized');
}

function initializePreviewSystem() {
    previewIframe = document.getElementById('preview-iframe');

    // Wait for iframe to load
    previewIframe.addEventListener('load', function() {
        try {
            previewDocument = previewIframe.contentDocument || previewIframe.contentWindow.document;

            // Get preview elements
            previewTitle = previewDocument.querySelector('.drop-title');
            previewDescription = previewDocument.querySelector('.drop-description p');
            previewButtonText = previewDocument.querySelector('.signup-button .button-text');
            previewUrlSlug = previewDocument.querySelector('.drop-url-slug');
            previewCoverImage = previewDocument.querySelector('.drop-cover-image img');
            previewCover = previewDocument.querySelector('.drop-cover-image');

            console.log('🖼️ Preview elements found:', {
                title: !!previewTitle,
                description: !!previewDescription,
                buttonText: !!previewButtonText,
                urlSlug: !!previewUrlSlug,
                coverImage: !!previewCoverImage
            });

            // Initialize preview content
            updatePreviewContent();
            updatePreviewColors();

        } catch (error) {
            console.warn('⚠️ Preview iframe access restricted (cross-origin), using refresh method');
        }
    });

    // Preview mode switching
    const previewButtons = document.querySelectorAll('.preview-mode-btn');

    previewButtons.forEach(button => {
        button.addEventListener('click', function() {
            previewButtons.forEach(btn => btn.classList.remove('active', 'bg-primary-50', 'text-primary-600', 'border-primary-300'));
            this.classList.add('active', 'bg-primary-50', 'text-primary-600', 'border-primary-300');

            const mode = this.id.replace('preview-', '');
            const container = document.getElementById('preview-container');

            if (mode === 'mobile') {
                container.style.maxWidth = '375px';
                container.style.margin = '0 auto';
                previewIframe.style.height = '667px';
            } else {
                container.style.maxWidth = '100%';
                container.style.margin = '0';
                previewIframe.style.height = '400px';
            }
        });
    });

    console.log('🖥️ Preview system initialized');
}

function initializeRealTimeUpdates() {
    // Content updates
    if (titleInput) titleInput.addEventListener('input', updatePreviewContent);
    if (descriptionInput) descriptionInput.addEventListener('input', updatePreviewContent);
    if (buttonTextInput) buttonTextInput.addEventListener('input', updatePreviewContent);
    if (coverImageInput) coverImageInput.addEventListener('input', updatePreviewContent);

    // Color updates
    if (backgroundColorInput) backgroundColorInput.addEventListener('input', updatePreviewColors);
    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const cardBackgroundTypeSelect = document.getElementById('edit-card-background-type');
    const buttonTextColorInput = document.getElementById('edit-button-text-color');

    if (backgroundTypeSelect) backgroundTypeSelect.addEventListener('change', updatePreviewColors);
    if (cardBackgroundTypeSelect) cardBackgroundTypeSelect.addEventListener('change', updatePreviewColors);
    if (titleColorInput) titleColorInput.addEventListener('input', updatePreviewColors);
    if (descriptionColorInput) descriptionColorInput.addEventListener('input', updatePreviewColors);
    if (buttonColorInput) buttonColorInput.addEventListener('input', updatePreviewColors);
    if (buttonTextColorInput) buttonTextColorInput.addEventListener('input', updatePreviewColors);

    // Form field toggles
    if (collectEmailToggle) collectEmailToggle.addEventListener('change', updatePreviewFormFields);
    if (collectPhoneToggle) collectPhoneToggle.addEventListener('change', updatePreviewFormFields);

    console.log('⚡ Real-time updates initialized');
}

function updatePreviewContent() {
    try {
        if (previewTitle && titleInput) {
            previewTitle.textContent = titleInput.value || 'Drop Title';
        }
        if (previewDescription && descriptionInput) {
            previewDescription.textContent = descriptionInput.value || 'Drop description';
        }
        if (previewButtonText && buttonTextInput) {
            previewButtonText.textContent = buttonTextInput.value || 'Get Notified';
        }
        if (previewUrlSlug && slugInput) {
            previewUrlSlug.textContent = slugInput.value || 'drop-slug';
        }

        // Handle cover image
        if (previewCoverImage && previewCover && coverImageInput) {
            const coverImageUrl = coverImageInput.value;
            if (coverImageUrl && isValidUrl(coverImageUrl)) {
                previewCoverImage.src = coverImageUrl;
                previewCover.style.display = 'block';
            } else {
                previewCover.style.display = 'none';
            }
        }

        console.log('📝 Preview content updated');
    } catch (error) {
        console.warn('⚠️ Preview content update failed, refreshing iframe');
        refreshPreview();
    }
}

function updatePreviewColors() {
    try {
        if (!previewDocument) return;

        const backgroundColor = backgroundColorInput?.value || '#ffffff';
        const cardColor = cardColorInput?.value || '#ffffff';
        const titleColor = titleColorInput?.value || '#000000';
        const descriptionColor = descriptionColorInput?.value || '#666666';
        const buttonColor = buttonColorInput?.value || '#007bff';
        const formFieldColor = formFieldColorInput?.value || '#ffffff';

        // Apply colors using CSS custom properties
        const documentRoot = previewDocument.documentElement;

        documentRoot.style.setProperty('--drop-background-color', backgroundColor);
        documentRoot.style.setProperty('--drop-card-color', cardColor);
        documentRoot.style.setProperty('--drop-title-color', titleColor);
        documentRoot.style.setProperty('--drop-description-color', descriptionColor);
        documentRoot.style.setProperty('--drop-button-color', buttonColor);
        documentRoot.style.setProperty('--drop-form-field-color', formFieldColor);

        console.log('🎨 Preview colors updated');
    } catch (error) {
        console.warn('⚠️ Preview color update failed, refreshing iframe');
        refreshPreview();
    }
}

function updatePreviewFormFields() {
    try {
        if (!previewDocument) return;

        const emailField = previewDocument.querySelector('.email-field');
        const phoneField = previewDocument.querySelector('.phone-field');

        if (emailField && collectEmailToggle) {
            emailField.style.display = collectEmailToggle.checked ? 'block' : 'none';
        }
        if (phoneField && collectPhoneToggle) {
            phoneField.style.display = collectPhoneToggle.checked ? 'block' : 'none';
        }

        console.log('📱 Preview form fields updated');
    } catch (error) {
        console.warn('⚠️ Preview form fields update failed');
    }
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function refreshPreview() {
    const iframe = document.getElementById('preview-iframe');
    const currentSrc = iframe.src;
    iframe.src = '';
    setTimeout(() => {
        iframe.src = currentSrc;
    }, 100);
}

function initializeFormSubmission() {
    const form = document.getElementById('drop-edit-form');
    const saveBtn = document.getElementById('save-btn');
    const saveBtnText = document.getElementById('save-btn-text');
    const saveBtnSpinner = document.getElementById('save-btn-spinner');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        saveBtn.disabled = true;
        saveBtnText.textContent = 'Saving...';
        saveBtnSpinner.classList.remove('hidden');

        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Convert checkboxes to booleans
            data.is_active = formData.has('is_active');
            data.collect_email = formData.has('collect_email');
            data.collect_phone = formData.has('collect_phone');

            // Debug: Log the data being sent
            console.log('🔍 Form data being sent:', data);
            console.log('🎨 Styling fields:', {
                background_type: data.background_type,
                card_background_type: data.card_background_type,
                background_color: data.background_color,
                button_color: data.button_color,
                button_text_color: data.button_text_color,
                title_color: data.title_color,
                description_color: data.description_color
            });

            const response = await fetch(`/api/drops/{{drop.id}}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Drop updated successfully:', result);
                showToast('Drop updated successfully!', 'success');
                setTimeout(() => {
                    refreshPreview();
                }, 500);
            } else {
                console.error('❌ Update failed. Status:', response.status);
                const errorText = await response.text();
                console.error('❌ Error response:', errorText);
                try {
                    const error = JSON.parse(errorText);
                    showToast(error.message || error.error || 'Failed to update drop', 'error');
                } catch (e) {
                    showToast(`Failed to update drop (${response.status})`, 'error');
                }
            }
        } catch (error) {
            showToast('Failed to update drop', 'error');
        } finally {
            // Reset button state
            saveBtn.disabled = false;
            saveBtnText.textContent = 'Save Changes';
            saveBtnSpinner.classList.add('hidden');
        }
    });
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
}

async function loadAnalytics() {
    try {
        const response = await fetch(`/api/drops/{{drop.id}}/analytics`);
        if (response.ok) {
            const result = await response.json();
            const data = result.data;

            // Update analytics display
            document.getElementById('analytics-views').textContent = data.views || 0;
            document.getElementById('analytics-fans').textContent = data.fans || 0;
            document.getElementById('analytics-conversion').textContent = `${data.conversionRate || 0}%`;

            // Update recent signups
            const signupsContainer = document.getElementById('recent-signups');
            if (data.recentSignups && data.recentSignups.length > 0) {
                signupsContainer.innerHTML = data.recentSignups.map(signup => `
                    <div class="flex items-center justify-between py-2">
                        <span class="text-sm text-gray-900">${signup.email || signup.phone || 'Anonymous'}</span>
                        <span class="text-xs text-gray-500">${new Date(signup.created_at).toLocaleDateString()}</span>
                    </div>
                `).join('');
            } else {
                signupsContainer.innerHTML = '<p class="text-sm text-gray-500">No signups yet</p>';
            }
        }
    } catch (error) {
        console.error('Failed to load analytics:', error);
    }
}
</script>

<style>
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.preview-mode-btn.active {
    background-color: rgb(239 246 255);
    color: rgb(37 99 235);
    border-color: rgb(147 197 253);
}

/* Mobile preview styling */
#preview-container {
    transition: all 0.3s ease;
}

/* Enhanced Modern Color Picker Design */
.modern-color-input {
    width: 100% !important;
    height: 48px !important;
    padding: 0 !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    cursor: pointer !important;
    background: white !important;
    transition: all 0.2s ease !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.modern-color-input:hover {
    border-color: #3b82f6 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
}

.modern-color-input:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.modern-color-input::-webkit-color-swatch-wrapper {
    padding: 0 !important;
    border: none !important;
    border-radius: 10px !important;
}

.modern-color-input::-webkit-color-swatch {
    border: none !important;
    border-radius: 10px !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.modern-color-input::-moz-color-swatch {
    border: none !important;
    border-radius: 10px !important;
}

/* Form styling improvements */
.drop-form input:focus,
.drop-form textarea:focus,
.drop-form select:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Enhanced preview iframe styling */
#preview-iframe {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive grid improvements */
@media (max-width: 640px) {
    .grid.grid-cols-1.sm\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .modern-color-input {
        height: 40px !important;
    }
}

/* Loading state styling */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Toast notification styling */
.toast-notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>