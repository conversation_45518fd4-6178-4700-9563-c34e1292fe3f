<!-- Modern Drop Edit Page -->
<div class="space-y-6">
    <!-- Header with <PERSON> Button -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <button type="button" onclick="window.history.back()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back
            </button>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Drop</h1>
                <p class="text-sm text-gray-500">{{drop.title}}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex items-center space-x-3">
            <a href="/drop/{{drop.slug}}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                View Live
            </a>

            <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">Status:</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{#if drop.is_active}}bg-green-100 text-green-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                    {{#if drop.is_active}}Active{{else}}Inactive{{/if}}
                </span>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Form Section (2/3 width on desktop) -->
        <div class="lg:col-span-2">
            <!-- Tab Navigation -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="page">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Page
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="messaging">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                Messaging
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="advanced">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Advanced
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="sharing">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                                Sharing
                            </div>
                        </button>

                        <button type="button" class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="analytics">
                            <div class="flex items-center">
                                <svg class="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
                                </svg>
                                Analytics
                            </div>
                        </button>
                    </nav>
                </div>

                <!-- Form Content -->
                <form id="drop-edit-form" class="drop-form">
                    <!-- PAGE TAB CONTENT -->
                    <div class="tab-content active p-6 space-y-6" data-tab-content="page">
                        <!-- Details Section -->
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Details</h3>
                                <p class="text-sm text-gray-500 mb-4">Configure the basic information for your drop page</p>

                                <div class="space-y-4">
                                    <div>
                                        <label for="edit-title" class="block text-sm font-medium text-gray-700">Title *</label>
                                        <input type="text" id="edit-title" name="title" required maxlength="255" value="{{drop.title}}" placeholder="Enter drop title" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <div>
                                        <label for="edit-description" class="block text-sm font-medium text-gray-700">Description</label>
                                        <textarea id="edit-description" name="description" rows="4" placeholder="Describe what this drop is about" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">{{drop.description}}</textarea>
                                    </div>

                                    <div>
                                        <label for="edit-slug" class="block text-sm font-medium text-gray-700">URL Slug</label>
                                        <div class="mt-1 flex rounded-md shadow-sm">
                                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                                {{domain}}/drop/
                                            </span>
                                            <input type="text" id="edit-slug" name="slug" value="{{drop.slug}}" class="flex-1 block w-full rounded-none rounded-r-md border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Styling Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Styling</h3>
                                <p class="text-sm text-gray-500 mb-4">Customize the appearance of your drop page</p>

                                <div class="space-y-6">
                                    <!-- Cover Image -->
                                    <div>
                                        <label for="edit-cover-image" class="block text-sm font-medium text-gray-700">Cover Image URL</label>
                                        <input type="url" id="edit-cover-image" name="cover_image" value="{{drop.cover_image}}" placeholder="https://example.com/image.jpg" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <!-- Background Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Background</h4>
                                        <div class="space-y-4">
                                            <div>
                                                <label for="edit-background-type" class="block text-sm font-medium text-gray-700">Background Type</label>
                                                <select id="edit-background-type" name="background_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                    <option value="gradient" {{#if (eq drop.background_type "gradient")}}selected{{/if}}>Gradient</option>
                                                    <option value="solid" {{#if (eq drop.background_type "solid")}}selected{{/if}}>Solid Color</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label for="edit-background-color" class="block text-sm font-medium text-gray-700">Background Color</label>
                                                <input type="color" id="edit-background-color" name="background_color" value="{{drop.background_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>

                                            <!-- Advanced Gradient Editor -->
                                            <div id="gradient-editor" style="display: {{#if (eq drop.background_type "gradient")}}block{{else}}none{{/if}};">
                                                <label class="block text-sm font-medium text-gray-700 mb-3">Advanced Gradient Designer</label>

                                                <!-- Gradient Preview -->
                                                <div class="mb-3">
                                                    <div id="gradient-preview" class="w-full h-16 rounded-md border border-gray-300 shadow-sm" style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);"></div>
                                                </div>

                                                <!-- Gradient Controls -->
                                                <div class="space-y-3">
                                                    <!-- Gradient Type -->
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-600 mb-1">Type</label>
                                                        <div class="flex space-x-1">
                                                            <button type="button" id="gradient-linear" class="gradient-type-btn active flex-1 px-2 py-1 text-xs font-medium border rounded">Linear</button>
                                                            <button type="button" id="gradient-radial" class="gradient-type-btn flex-1 px-2 py-1 text-xs font-medium border rounded">Radial</button>
                                                        </div>
                                                    </div>

                                                    <!-- Gradient Direction -->
                                                    <div id="gradient-direction-section">
                                                        <label class="block text-xs font-medium text-gray-600 mb-1">Direction</label>
                                                        <div class="grid grid-cols-4 gap-1">
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="0">0°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="45">45°</button>
                                                            <button type="button" class="gradient-direction-btn active px-2 py-1 text-xs border rounded" data-angle="90">90°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="135">135°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="180">180°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="225">225°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="270">270°</button>
                                                            <button type="button" class="gradient-direction-btn px-2 py-1 text-xs border rounded" data-angle="315">315°</button>
                                                        </div>
                                                    </div>

                                                    <!-- Color Stops -->
                                                    <div>
                                                        <div class="flex items-center justify-between mb-1">
                                                            <label class="block text-xs font-medium text-gray-600">Colors</label>
                                                            <div class="flex space-x-1">
                                                                <button type="button" id="add-color-stop" class="px-2 py-1 text-xs bg-primary-100 text-primary-700 rounded hover:bg-primary-200">+</button>
                                                                <button type="button" id="remove-color-stop" class="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">-</button>
                                                            </div>
                                                        </div>
                                                        <div id="color-stops-container" class="space-y-1">
                                                            <!-- Color stops will be dynamically generated -->
                                                        </div>
                                                    </div>

                                                    <!-- Quick Presets -->
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-600 mb-1">Presets</label>
                                                        <div class="grid grid-cols-6 gap-1">
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #667eea 0%, #764ba2 100%)" style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);"></button>
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #f093fb 0%, #f5576c 100%)" style="background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);"></button>
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)" style="background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);"></button>
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #43e97b 0%, #38f9d7 100%)" style="background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);"></button>
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #fa709a 0%, #fee140 100%)" style="background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);"></button>
                                                            <button type="button" class="gradient-preset h-6 rounded border hover:border-primary-300" data-gradient="linear-gradient(90deg, #a8edea 0%, #fed6e3 100%)" style="background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);"></button>
                                                        </div>
                                                    </div>

                                                    <!-- CSS Export -->
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-600 mb-1">CSS</label>
                                                        <div class="flex space-x-1">
                                                            <input type="text" id="gradient-css-output" readonly class="flex-1 px-2 py-1 text-xs border border-gray-300 rounded bg-gray-50" placeholder="Generated CSS">
                                                            <button type="button" id="copy-gradient-css" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Copy</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Hidden inputs for form submission -->
                                                <input type="hidden" id="gradient-data" name="gradient_data" value="">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Card Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Card Background</h4>
                                        <div>
                                            <label for="edit-card-background-type" class="block text-sm font-medium text-gray-700">Card Style</label>
                                            <select id="edit-card-background-type" name="card_background_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                                <option value="solid_white" {{#if (eq drop.card_background_type "solid_white")}}selected{{/if}}>Solid White</option>
                                                <option value="solid_dark" {{#if (eq drop.card_background_type "solid_dark")}}selected{{/if}}>Solid Dark</option>
                                                <option value="translucent_light" {{#if (eq drop.card_background_type "translucent_light")}}selected{{/if}}>Translucent Light</option>
                                                <option value="translucent_dark" {{#if (eq drop.card_background_type "translucent_dark")}}selected{{/if}}>Translucent Dark</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Text Colors -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Text Colors</h4>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div>
                                                <label for="edit-title-color" class="block text-sm font-medium text-gray-700">Title Color</label>
                                                <input type="color" id="edit-title-color" name="title_color" value="{{drop.title_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>

                                            <div>
                                                <label for="edit-description-color" class="block text-sm font-medium text-gray-700">Subtitle Color</label>
                                                <input type="color" id="edit-description-color" name="description_color" value="{{drop.description_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Button Settings -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Button Styling</h4>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div>
                                                <label for="edit-button-color" class="block text-sm font-medium text-gray-700">Button Background</label>
                                                <input type="color" id="edit-button-color" name="button_color" value="{{drop.button_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>

                                            <div>
                                                <label for="edit-button-text-color" class="block text-sm font-medium text-gray-700">Button Text Color</label>
                                                <input type="color" id="edit-button-text-color" name="button_text_color" value="{{drop.button_text_color}}" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 modern-color-input">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Platform Links Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Platform Links</h3>
                                <p class="text-sm text-gray-500 mb-4">Add links to your social media and streaming platforms</p>

                                <div class="space-y-4">
                                    <div>
                                        <label for="edit-button-text" class="block text-sm font-medium text-gray-700">Button Text</label>
                                        <input type="text" id="edit-button-text" name="button_text" value="{{drop.button_text}}" placeholder="Get Notified" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-website-link" class="block text-sm font-medium text-gray-700">Website</label>
                                            <input type="url" id="edit-website-link" name="website_link" value="{{drop.website_link}}" placeholder="https://yourwebsite.com" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-instagram-link" class="block text-sm font-medium text-gray-700">Instagram</label>
                                            <input type="url" id="edit-instagram-link" name="instagram_link" value="{{drop.instagram_link}}" placeholder="https://instagram.com/yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-twitter-link" class="block text-sm font-medium text-gray-700">Twitter/X</label>
                                            <input type="url" id="edit-twitter-link" name="twitter_link" value="{{drop.twitter_link}}" placeholder="https://twitter.com/yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-spotify-link" class="block text-sm font-medium text-gray-700">Spotify URL</label>
                                            <input type="url" id="edit-spotify-link" name="spotify_link" value="{{drop.spotify_link}}" placeholder="https://open.spotify.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-apple-music-url" class="block text-sm font-medium text-gray-700">Apple Music URL</label>
                                            <input type="url" id="edit-apple-music-url" name="apple_music_url" value="{{drop.apple_music_url}}" placeholder="https://music.apple.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-youtube-link" class="block text-sm font-medium text-gray-700">YouTube URL</label>
                                            <input type="url" id="edit-youtube-link" name="youtube_link" value="{{drop.youtube_link}}" placeholder="https://youtube.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="edit-soundcloud-url" class="block text-sm font-medium text-gray-700">SoundCloud URL</label>
                                            <input type="url" id="edit-soundcloud-url" name="soundcloud_url" value="{{drop.soundcloud_url}}" placeholder="https://soundcloud.com/..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="edit-tiktok-link" class="block text-sm font-medium text-gray-700">TikTok</label>
                                            <input type="url" id="edit-tiktok-link" name="tiktok_link" value="{{drop.tiktok_link}}" placeholder="https://tiktok.com/@yourusername" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Collection Settings -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Form Collection</h3>
                                <p class="text-sm text-gray-500 mb-4">Configure what information to collect from fans</p>

                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label for="edit-collect-email" class="text-sm font-medium text-gray-900">Collect Email</label>
                                            <p class="text-sm text-gray-500">Require email address for signups</p>
                                        </div>
                                        <input type="checkbox" id="edit-collect-email" name="collect_email" {{#if drop.collect_email}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label for="edit-collect-phone" class="text-sm font-medium text-gray-900">Collect Phone</label>
                                            <p class="text-sm text-gray-500">Require phone number for signups</p>
                                        </div>
                                        <input type="checkbox" id="edit-collect-phone" name="collect_phone" {{#if drop.collect_phone}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>

                            <!-- Status Control -->
                            <div class="border-t border-gray-200 pt-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">Drop Status</h3>
                                        <p class="text-sm text-gray-500">Control whether your drop is live and accessible to fans</p>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="edit-is-active" name="is_active" {{#if drop.is_active}}checked{{/if}} class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                        <label for="edit-is-active" class="ml-2 block text-sm text-gray-900">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MESSAGING TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="messaging">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Messaging</h3>
                            <p class="mt-1 text-sm text-gray-500">Configure email templates, SMS messages, and notification settings.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- ADVANCED TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="advanced">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Advanced Settings</h3>
                            <p class="mt-1 text-sm text-gray-500">Advanced configuration options and integrations.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- SHARING TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="sharing">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Sharing & QR Codes</h3>
                            <p class="mt-1 text-sm text-gray-500">Share your drop and generate QR codes for easy access.</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Coming Soon
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- ANALYTICS TAB CONTENT -->
                    <div class="tab-content p-6" data-tab-content="analytics">
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Drop Analytics</h3>
                                <p class="text-sm text-gray-500 mb-4">Track your drop's performance and fan engagement</p>
                            </div>

                            <!-- Stats Cards -->
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Page Views</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-views">{{drop.view_count}}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Total Fans</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-fans">{{drop.signup_count}}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
                                            <p class="text-lg font-semibold text-gray-900" id="analytics-conversion">0%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Signups -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Fan Signups</h4>
                                <div id="recent-signups" class="space-y-2">
                                    <p class="text-sm text-gray-500">Loading recent signups...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex items-center justify-end space-x-3">
                <!-- Debug Test Button -->
                <button type="button" id="debug-test-btn" class="px-3 py-1 border border-gray-300 rounded text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    🔧 Debug
                </button>

                <button type="button" id="cancel-btn" onclick="window.history.back()" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Cancel
                </button>
                <button type="submit" id="save-btn" form="drop-edit-form" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <span id="save-btn-text">Save Changes</span>
                    <svg id="save-btn-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Preview Section (1/3 width on desktop) -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg overflow-hidden sticky top-6">
                <!-- Preview Header -->
                <div class="px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-sm font-medium text-gray-900">Live Preview</h3>
                        <div class="flex items-center space-x-2">
                            <button type="button" id="preview-desktop" class="preview-mode-btn active inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                Desktop
                            </button>
                            <button type="button" id="preview-mobile" class="preview-mode-btn inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
                                </svg>
                                Mobile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Preview Content -->
                <div class="p-4">
                    <div id="preview-container" class="border border-gray-200 rounded-lg overflow-hidden">
                        <iframe id="preview-iframe" src="/drop/{{drop.slug}}?preview=true" class="w-full h-96 border-0"></iframe>
                    </div>

                    <!-- Preview Actions -->
                    <div class="mt-4 flex items-center justify-between text-xs text-gray-500">
                        <span>Updates automatically as you type</span>
                        <button type="button" onclick="refreshPreview()" class="text-primary-600 hover:text-primary-500">
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Drop Edit JavaScript - Enhanced Real-Time Preview System -->
<script>
// Global variables for preview elements
let previewIframe;
let previewDocument;
let previewTitle, previewDescription, previewButtonText, previewUrlSlug;
let previewCoverImage, previewCover;

// Form input elements
let titleInput, descriptionInput, slugInput, buttonTextInput, coverImageInput;
let backgroundColorInput, cardColorInput, titleColorInput, descriptionColorInput, buttonColorInput, formFieldColorInput;
let collectEmailToggle, collectPhoneToggle;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing modern drop edit system...');

    // Initialize form elements
    initializeFormElements();

    // Initialize tab functionality
    initializeTabSystem();

    // Initialize preview system
    initializePreviewSystem();

    // Initialize form submission
    initializeFormSubmission();

    // Initialize real-time updates
    initializeRealTimeUpdates();

    // Initialize debug test button
    initializeDebugTest();

    // Initialize gradient editor
    initializeGradientEditor();

    console.log('✅ Modern drop edit system initialized successfully');

    // Enhanced form field debugging and validation
    setTimeout(() => {
        console.log('🔍 ENHANCED FORM FIELDS DEBUG');
        console.log('='.repeat(50));

        const form = document.getElementById('drop-edit-form');
        if (!form) {
            console.error('❌ Form element not found!');
            return;
        }

        // Check all form elements
        const allInputs = form.querySelectorAll('input, select, textarea');
        console.log(`📊 Total form elements found: ${allInputs.length}`);

        // Verify styling fields specifically
        const stylingFields = [
            { name: 'background_type', type: 'select', expected: '{{drop.background_type}}' },
            { name: 'card_background_type', type: 'select', expected: '{{drop.card_background_type}}' },
            { name: 'background_color', type: 'color', expected: '{{drop.background_color}}' },
            { name: 'button_color', type: 'color', expected: '{{drop.button_color}}' },
            { name: 'button_text_color', type: 'color', expected: '{{drop.button_text_color}}' },
            { name: 'title_color', type: 'color', expected: '{{drop.title_color}}' },
            { name: 'description_color', type: 'color', expected: '{{drop.description_color}}' }
        ];

        console.log('\n🎨 STYLING FIELDS VERIFICATION');
        console.log('-'.repeat(40));

        let allStylingFieldsFound = true;

        stylingFields.forEach(field => {
            const element = document.querySelector(`[name="${field.name}"]`);
            if (element) {
                const value = element.value;
                const isPopulated = value && value !== '';
                console.log(`   ✅ ${field.name} (${field.type}): "${value}" ${isPopulated ? '✅' : '⚠️ EMPTY'}`);

                // Check if element is visible
                const isVisible = element.offsetParent !== null;
                if (!isVisible) {
                    console.warn(`   ⚠️ ${field.name} element is hidden!`);
                }

                // For select elements, check options
                if (field.type === 'select' && element.tagName === 'SELECT') {
                    console.log(`     📋 Options: ${Array.from(element.options).map(opt => `"${opt.value}"`).join(', ')}`);
                    console.log(`     🎯 Selected: "${element.selectedOptions[0]?.value || 'none'}"`);
                }
            } else {
                console.error(`   ❌ ${field.name}: ELEMENT NOT FOUND!`);
                allStylingFieldsFound = false;
            }
        });

        // Test form data collection
        console.log('\n📦 FORM DATA COLLECTION TEST');
        console.log('-'.repeat(40));

        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            console.log('📊 Collected form data:');
            stylingFields.forEach(field => {
                const value = data[field.name];
                console.log(`   ${field.name}: "${value || 'MISSING'}"`);
            });

            // Check for any missing styling data
            const missingStylingData = stylingFields.filter(field => !data[field.name]);
            if (missingStylingData.length > 0) {
                console.warn('⚠️ Missing styling data:', missingStylingData.map(f => f.name));
            } else {
                console.log('✅ All styling fields have data');
            }

        } catch (error) {
            console.error('❌ Error collecting form data:', error);
        }

        // Summary
        console.log('\n📋 SUMMARY');
        console.log('-'.repeat(40));
        console.log(`Form found: ${form ? '✅' : '❌'}`);
        console.log(`Total elements: ${allInputs.length}`);
        console.log(`Styling fields found: ${allStylingFieldsFound ? '✅' : '❌'}`);

    }, 1500);
});

function initializeFormElements() {
    // Get all form input elements
    titleInput = document.getElementById('edit-title');
    descriptionInput = document.getElementById('edit-description');
    slugInput = document.getElementById('edit-slug');
    buttonTextInput = document.getElementById('edit-button-text');
    coverImageInput = document.getElementById('edit-cover-image');

    // Color inputs
    backgroundColorInput = document.getElementById('edit-background-color');
    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const cardBackgroundTypeSelect = document.getElementById('edit-card-background-type');
    titleColorInput = document.getElementById('edit-title-color');
    descriptionColorInput = document.getElementById('edit-description-color');
    buttonColorInput = document.getElementById('edit-button-color');
    const buttonTextColorInput = document.getElementById('edit-button-text-color');

    // Toggle inputs
    collectEmailToggle = document.getElementById('edit-collect-email');
    collectPhoneToggle = document.getElementById('edit-collect-phone');

    console.log('📝 Form elements initialized');
}

function initializeTabSystem() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => {
                btn.classList.remove('border-primary-500', 'text-primary-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-primary-500', 'text-primary-600');

            // Show corresponding content
            const targetContent = document.querySelector(`[data-tab-content="${targetTab}"]`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Load analytics if analytics tab is clicked
            if (targetTab === 'analytics') {
                loadAnalytics();
            }
        });
    });

    console.log('🗂️ Tab system initialized');
}

function initializePreviewSystem() {
    previewIframe = document.getElementById('preview-iframe');

    // Wait for iframe to load
    previewIframe.addEventListener('load', function() {
        try {
            previewDocument = previewIframe.contentDocument || previewIframe.contentWindow.document;

            // Get preview elements
            previewTitle = previewDocument.querySelector('.drop-title');
            previewDescription = previewDocument.querySelector('.drop-description p');
            previewButtonText = previewDocument.querySelector('.signup-button .button-text');
            previewUrlSlug = previewDocument.querySelector('.drop-url-slug');
            previewCoverImage = previewDocument.querySelector('.drop-cover-image img');
            previewCover = previewDocument.querySelector('.drop-cover-image');

            console.log('🖼️ Preview elements found:', {
                title: !!previewTitle,
                description: !!previewDescription,
                buttonText: !!previewButtonText,
                urlSlug: !!previewUrlSlug,
                coverImage: !!previewCoverImage
            });

            // Initialize preview content
            updatePreviewContent();
            updatePreviewColors();

        } catch (error) {
            console.warn('⚠️ Preview iframe access restricted (cross-origin), using refresh method');
        }
    });

    // Preview mode switching
    const previewButtons = document.querySelectorAll('.preview-mode-btn');

    previewButtons.forEach(button => {
        button.addEventListener('click', function() {
            previewButtons.forEach(btn => btn.classList.remove('active', 'bg-primary-50', 'text-primary-600', 'border-primary-300'));
            this.classList.add('active', 'bg-primary-50', 'text-primary-600', 'border-primary-300');

            const mode = this.id.replace('preview-', '');
            const container = document.getElementById('preview-container');

            if (mode === 'mobile') {
                container.style.maxWidth = '375px';
                container.style.margin = '0 auto';
                previewIframe.style.height = '667px';
            } else {
                container.style.maxWidth = '100%';
                container.style.margin = '0';
                previewIframe.style.height = '400px';
            }
        });
    });

    console.log('🖥️ Preview system initialized');
}

function initializeRealTimeUpdates() {
    // Content updates
    if (titleInput) titleInput.addEventListener('input', updatePreviewContent);
    if (descriptionInput) descriptionInput.addEventListener('input', updatePreviewContent);
    if (buttonTextInput) buttonTextInput.addEventListener('input', updatePreviewContent);
    if (coverImageInput) coverImageInput.addEventListener('input', updatePreviewContent);

    // Color updates
    if (backgroundColorInput) backgroundColorInput.addEventListener('input', updatePreviewColors);
    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const cardBackgroundTypeSelect = document.getElementById('edit-card-background-type');
    const buttonTextColorInput = document.getElementById('edit-button-text-color');

    if (backgroundTypeSelect) backgroundTypeSelect.addEventListener('change', updatePreviewColors);
    if (cardBackgroundTypeSelect) cardBackgroundTypeSelect.addEventListener('change', updatePreviewColors);
    if (titleColorInput) titleColorInput.addEventListener('input', updatePreviewColors);
    if (descriptionColorInput) descriptionColorInput.addEventListener('input', updatePreviewColors);
    if (buttonColorInput) buttonColorInput.addEventListener('input', updatePreviewColors);
    if (buttonTextColorInput) buttonTextColorInput.addEventListener('input', updatePreviewColors);

    // Form field toggles
    if (collectEmailToggle) collectEmailToggle.addEventListener('change', updatePreviewFormFields);
    if (collectPhoneToggle) collectPhoneToggle.addEventListener('change', updatePreviewFormFields);

    console.log('⚡ Real-time updates initialized');
}

function updatePreviewContent() {
    try {
        if (previewTitle && titleInput) {
            previewTitle.textContent = titleInput.value || 'Drop Title';
        }
        if (previewDescription && descriptionInput) {
            previewDescription.textContent = descriptionInput.value || 'Drop description';
        }
        if (previewButtonText && buttonTextInput) {
            previewButtonText.textContent = buttonTextInput.value || 'Get Notified';
        }
        if (previewUrlSlug && slugInput) {
            previewUrlSlug.textContent = slugInput.value || 'drop-slug';
        }

        // Handle cover image
        if (previewCoverImage && previewCover && coverImageInput) {
            const coverImageUrl = coverImageInput.value;
            if (coverImageUrl && isValidUrl(coverImageUrl)) {
                previewCoverImage.src = coverImageUrl;
                previewCover.style.display = 'block';
            } else {
                previewCover.style.display = 'none';
            }
        }

        console.log('📝 Preview content updated');
    } catch (error) {
        console.warn('⚠️ Preview content update failed, refreshing iframe');
        refreshPreview();
    }
}

function updatePreviewColors() {
    try {
        if (!previewDocument) return;

        const backgroundColor = backgroundColorInput?.value || '#ffffff';
        const cardColor = cardColorInput?.value || '#ffffff';
        const titleColor = titleColorInput?.value || '#000000';
        const descriptionColor = descriptionColorInput?.value || '#666666';
        const buttonColor = buttonColorInput?.value || '#007bff';
        const formFieldColor = formFieldColorInput?.value || '#ffffff';

        // Apply colors using CSS custom properties
        const documentRoot = previewDocument.documentElement;

        documentRoot.style.setProperty('--drop-background-color', backgroundColor);
        documentRoot.style.setProperty('--drop-card-color', cardColor);
        documentRoot.style.setProperty('--drop-title-color', titleColor);
        documentRoot.style.setProperty('--drop-description-color', descriptionColor);
        documentRoot.style.setProperty('--drop-button-color', buttonColor);
        documentRoot.style.setProperty('--drop-form-field-color', formFieldColor);

        console.log('🎨 Preview colors updated');
    } catch (error) {
        console.warn('⚠️ Preview color update failed, refreshing iframe');
        refreshPreview();
    }
}

function updatePreviewFormFields() {
    try {
        if (!previewDocument) return;

        const emailField = previewDocument.querySelector('.email-field');
        const phoneField = previewDocument.querySelector('.phone-field');

        if (emailField && collectEmailToggle) {
            emailField.style.display = collectEmailToggle.checked ? 'block' : 'none';
        }
        if (phoneField && collectPhoneToggle) {
            phoneField.style.display = collectPhoneToggle.checked ? 'block' : 'none';
        }

        console.log('📱 Preview form fields updated');
    } catch (error) {
        console.warn('⚠️ Preview form fields update failed');
    }
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function refreshPreview() {
    console.log('🔄 Refreshing preview iframe...');

    const iframe = document.getElementById('preview-iframe');
    if (!iframe) {
        console.error('❌ Preview iframe not found');
        return;
    }

    const currentSrc = iframe.src;
    console.log('🌐 Current preview URL:', currentSrc);

    // Add a timestamp to force refresh and bypass cache
    const url = new URL(currentSrc);
    url.searchParams.set('_refresh', Date.now());
    url.searchParams.set('preview', 'true');

    console.log('🔄 Refreshing with URL:', url.toString());

    // Show loading state
    iframe.style.opacity = '0.5';

    iframe.src = '';
    setTimeout(() => {
        iframe.src = url.toString();

        // Reset opacity when loaded
        iframe.onload = () => {
            iframe.style.opacity = '1';
            console.log('✅ Preview refreshed successfully');
        };

        // Handle load errors
        iframe.onerror = () => {
            iframe.style.opacity = '1';
            console.error('❌ Preview failed to load');
        };
    }, 200);
}

// Advanced Gradient Editor System
let gradientData = {
    type: 'linear',
    angle: 90,
    stops: [
        { color: '#667eea', position: 0 },
        { color: '#764ba2', position: 100 }
    ]
};

function initializeGradientEditor() {
    console.log('🎨 Initializing gradient editor...');

    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const gradientEditor = document.getElementById('gradient-editor');

    // Show/hide gradient editor based on background type
    if (backgroundTypeSelect) {
        backgroundTypeSelect.addEventListener('change', function() {
            console.log('🎨 Background type changed to:', this.value);
            if (this.value === 'gradient') {
                gradientEditor.style.display = 'block';
                initializeColorStops();
                updateGradientPreview();
            } else {
                gradientEditor.style.display = 'none';
            }
        });
    }

    // Initialize gradient type buttons
    const gradientTypeButtons = document.querySelectorAll('.gradient-type-btn');
    console.log('🎨 Found gradient type buttons:', gradientTypeButtons.length);
    gradientTypeButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Gradient type clicked:', this.id);
            gradientTypeButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            gradientData.type = this.id.replace('gradient-', '');

            const directionSection = document.getElementById('gradient-direction-section');
            if (gradientData.type === 'linear') {
                directionSection.style.display = 'block';
            } else {
                directionSection.style.display = 'none';
            }

            updateGradientPreview();
        });
    });

    // Initialize direction buttons
    const directionButtons = document.querySelectorAll('.gradient-direction-btn');
    console.log('🎨 Found direction buttons:', directionButtons.length);
    directionButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Direction clicked:', this.dataset.angle);
            directionButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            gradientData.angle = parseInt(this.dataset.angle);
            updateGradientPreview();
        });
    });

    // Initialize preset buttons
    const presetButtons = document.querySelectorAll('.gradient-preset');
    console.log('🎨 Found preset buttons:', presetButtons.length);
    presetButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Preset clicked:', this.dataset.gradient);
            const gradientCSS = this.dataset.gradient;
            parseGradientFromCSS(gradientCSS);
            updateGradientPreview();
            initializeColorStops();
        });
    });

    // Initialize add/remove color stop buttons
    const addStopBtn = document.getElementById('add-color-stop');
    const removeStopBtn = document.getElementById('remove-color-stop');

    if (addStopBtn) {
        addStopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Adding color stop');
            addColorStop();
        });
    }

    if (removeStopBtn) {
        removeStopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Removing color stop');
            removeColorStop();
        });
    }

    // Initialize copy CSS button
    const copyCSSBtn = document.getElementById('copy-gradient-css');
    if (copyCSSBtn) {
        copyCSSBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎨 Copying CSS');
            copyGradientCSS();
        });
    }

    // Initialize color stops
    initializeColorStops();
    updateGradientPreview();

    console.log('🎨 Gradient editor initialized successfully');
}

function initializeColorStops() {
    const container = document.getElementById('color-stops-container');
    if (!container) return;

    container.innerHTML = '';

    gradientData.stops.forEach((stop, index) => {
        const stopElement = createColorStopElement(stop, index);
        container.appendChild(stopElement);
    });
}

function createColorStopElement(stop, index) {
    const div = document.createElement('div');
    div.className = 'color-stop-item';
    div.innerHTML = `
        <input type="color" class="color-stop-color" value="${stop.color}" data-index="${index}">
        <input type="number" class="color-stop-position" value="${stop.position}" min="0" max="100" data-index="${index}">
        <span class="text-xs text-gray-500">%</span>
    `;

    // Add event listeners
    const colorInput = div.querySelector('.color-stop-color');
    const positionInput = div.querySelector('.color-stop-position');

    colorInput.addEventListener('input', function() {
        gradientData.stops[index].color = this.value;
        updateGradientPreview();
    });

    positionInput.addEventListener('input', function() {
        gradientData.stops[index].position = parseInt(this.value);
        updateGradientPreview();
    });

    return div;
}

function addColorStop() {
    if (gradientData.stops.length >= 4) return;

    const newPosition = gradientData.stops.length > 0 ?
        Math.min(100, Math.max(...gradientData.stops.map(s => s.position)) + 25) : 50;

    gradientData.stops.push({
        color: '#ffffff',
        position: newPosition
    });

    initializeColorStops();
    updateGradientPreview();
}

function removeColorStop() {
    if (gradientData.stops.length <= 2) return;

    gradientData.stops.pop();
    initializeColorStops();
    updateGradientPreview();
}

function updateGradientPreview() {
    const preview = document.getElementById('gradient-preview');
    const cssOutput = document.getElementById('gradient-css-output');

    if (!preview) {
        console.warn('🎨 Gradient preview element not found');
        return;
    }

    const gradientCSS = generateGradientCSS();
    console.log('🎨 Generated gradient CSS:', gradientCSS);
    console.log('🎨 Current gradient data:', JSON.stringify(gradientData, null, 2));

    preview.style.background = gradientCSS;

    if (cssOutput) {
        cssOutput.value = gradientCSS;
    }

    // Update hidden input for form submission - CRITICAL FIX
    const hiddenInput = document.getElementById('gradient-data');
    if (hiddenInput) {
        const gradientDataString = JSON.stringify(gradientData);
        hiddenInput.value = gradientDataString;
        console.log('🎨 Updated hidden input with gradient data:', gradientDataString);

        // Force trigger change event to ensure form recognizes the update
        hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
    } else {
        console.error('🎨 Hidden gradient-data input not found!');
    }

    // Update background color input to match first color stop
    const backgroundColorInput = document.getElementById('edit-background-color');
    if (backgroundColorInput && gradientData.stops.length > 0) {
        backgroundColorInput.value = gradientData.stops[0].color;
        // Trigger change event for background color input too
        backgroundColorInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Update the live preview iframe if available
    updateLivePreviewGradient(gradientCSS);
}

function generateGradientCSS() {
    const sortedStops = [...gradientData.stops].sort((a, b) => a.position - b.position);
    const stopsCSS = sortedStops.map(stop => `${stop.color} ${stop.position}%`).join(', ');

    if (gradientData.type === 'linear') {
        return `linear-gradient(${gradientData.angle}deg, ${stopsCSS})`;
    } else {
        return `radial-gradient(circle, ${stopsCSS})`;
    }
}

function parseGradientFromCSS(css) {
    // Simple parser for preset gradients
    const match = css.match(/linear-gradient\((\d+)deg,\s*(.+)\)/);
    if (match) {
        gradientData.type = 'linear';
        gradientData.angle = parseInt(match[1]);

        const stopsStr = match[2];
        const stops = stopsStr.split(',').map((stop, index) => {
            const colorMatch = stop.trim().match(/^(#[0-9a-fA-F]{6})/);
            return {
                color: colorMatch ? colorMatch[1] : '#ffffff',
                position: index * (100 / (stopsStr.split(',').length - 1))
            };
        });

        gradientData.stops = stops;
    }
}

function copyGradientCSS() {
    const cssOutput = document.getElementById('gradient-css-output');
    if (cssOutput) {
        cssOutput.select();
        document.execCommand('copy');

        // Show feedback
        const btn = document.getElementById('copy-gradient-css');
        const originalText = btn.textContent;
        btn.textContent = 'Copied!';
        setTimeout(() => {
            btn.textContent = originalText;
        }, 1000);
    }
}

function updateLivePreviewGradient(gradientCSS) {
    try {
        const iframe = document.getElementById('preview-iframe');
        if (!iframe || !iframe.contentDocument) {
            console.log('🎨 Preview iframe not accessible, will refresh instead');
            return;
        }

        const previewDocument = iframe.contentDocument;
        const dropPageContainer = previewDocument.querySelector('.drop-page-container');

        if (dropPageContainer) {
            dropPageContainer.style.background = gradientCSS;
            console.log('🎨 Updated live preview gradient:', gradientCSS);
        } else {
            console.warn('🎨 Drop page container not found in preview iframe');
        }
    } catch (error) {
        console.warn('🎨 Could not update live preview gradient (cross-origin):', error.message);
        // Fallback: refresh the preview iframe
        setTimeout(() => {
            refreshPreview();
        }, 500);
    }
}

function initializeFormSubmission() {
    const form = document.getElementById('drop-edit-form');
    const saveBtn = document.getElementById('save-btn');
    const saveBtnText = document.getElementById('save-btn-text');
    const saveBtnSpinner = document.getElementById('save-btn-spinner');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        console.log('🚀 FORM SUBMISSION TRIGGERED');
        console.log('='.repeat(50));

        // Show loading state
        saveBtn.disabled = true;
        saveBtnText.textContent = 'Saving...';
        saveBtnSpinner.classList.remove('hidden');

        // Show immediate feedback
        showToast('🔄 Saving changes...', 'info', 2000);

        try {
            console.log('🚀 Starting form submission...');

            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Convert checkboxes to booleans
            data.is_active = formData.has('is_active');
            data.collect_email = formData.has('collect_email');
            data.collect_phone = formData.has('collect_phone');

            // CRITICAL FIX: Ensure gradient data is captured
            const gradientDataInput = document.getElementById('gradient-data');
            if (gradientDataInput && gradientDataInput.value) {
                data.gradient_data = gradientDataInput.value;
                console.log('🎨 Captured gradient data for submission:', data.gradient_data);
            } else if (data.background_type === 'gradient') {
                // If background type is gradient but no gradient data, use current gradientData
                data.gradient_data = JSON.stringify(gradientData);
                console.log('🎨 Using current gradientData for submission:', data.gradient_data);
            }

            // Enhanced debugging: Log comprehensive form data
            console.log('🔍 FORM SUBMISSION DEBUG');
            console.log('='.repeat(50));
            console.log('📋 Complete form data:', data);
            console.log('📊 Form data count:', Object.keys(data).length, 'fields');

            // Verify styling fields specifically
            const stylingFields = {
                background_type: data.background_type,
                card_background_type: data.card_background_type,
                background_color: data.background_color,
                button_color: data.button_color,
                button_text_color: data.button_text_color,
                title_color: data.title_color,
                description_color: data.description_color,
                gradient_data: data.gradient_data
            };

            console.log('🎨 Styling fields verification:');
            Object.entries(stylingFields).forEach(([key, value]) => {
                const status = value ? '✅' : '❌';
                console.log(`   ${status} ${key}: ${value || 'MISSING'}`);
            });

            // Check for missing required styling fields
            const missingStylingFields = Object.entries(stylingFields)
                .filter(([key, value]) => !value)
                .map(([key]) => key);

            if (missingStylingFields.length > 0) {
                console.warn('⚠️ Missing styling fields:', missingStylingFields);
            }

            // Validate data before sending
            if (!data.title || !data.slug) {
                throw new Error('Title and slug are required');
            }

            console.log('📡 Sending API request...');
            console.log('🌐 URL:', `/api/drops/{{drop.id}}`);
            console.log('📦 Payload size:', JSON.stringify(data).length, 'bytes');

            const response = await fetch(`/api/drops/{{drop.id}}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    // Note: Authentication should be handled automatically via cookies
                },
                credentials: 'same-origin', // Ensure cookies are sent
                body: JSON.stringify(data)
            });

            console.log('📡 Response received:', {
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                headers: Object.fromEntries(response.headers.entries())
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ DROP UPDATE SUCCESS');
                console.log('='.repeat(30));
                console.log('📊 Server response:', result);

                if (result.data) {
                    console.log('🎨 Updated styling data:', {
                        background_type: result.data.background_type,
                        card_background_type: result.data.card_background_type,
                        background_color: result.data.background_color,
                        button_text_color: result.data.button_text_color
                    });
                }

                showToast('✅ Drop styling updated successfully!', 'success');

                // Refresh preview after a short delay
                setTimeout(() => {
                    console.log('🔄 Refreshing preview...');
                    refreshPreview();
                }, 1000);

            } else {
                console.error('❌ DROP UPDATE FAILED');
                console.error('='.repeat(30));
                console.error('📊 Response status:', response.status, response.statusText);

                const errorText = await response.text();
                console.error('📄 Error response body:', errorText);

                let errorMessage = 'Failed to update drop';

                try {
                    const error = JSON.parse(errorText);
                    errorMessage = error.message || error.error || errorMessage;
                    console.error('🚨 Parsed error:', error);
                } catch (parseError) {
                    console.error('🚨 Could not parse error response:', parseError);
                    errorMessage = `${errorMessage} (${response.status}: ${response.statusText})`;
                }

                // Show specific error messages for common issues
                if (response.status === 401) {
                    errorMessage = 'Authentication failed. Please refresh the page and try again.';
                } else if (response.status === 403) {
                    errorMessage = 'You do not have permission to edit this drop.';
                } else if (response.status === 404) {
                    errorMessage = 'Drop not found. It may have been deleted.';
                } else if (response.status >= 500) {
                    errorMessage = 'Server error. Please try again later.';
                }

                showToast(`❌ ${errorMessage}`, 'error');
            }
        } catch (error) {
            console.error('🚨 FORM SUBMISSION ERROR');
            console.error('='.repeat(30));
            console.error('🚨 Error details:', error);
            console.error('🚨 Error stack:', error.stack);

            let errorMessage = 'Network error. Please check your connection and try again.';

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = 'Could not connect to server. Please check your internet connection.';
            } else if (error.message) {
                errorMessage = error.message;
            }

            showToast(`🚨 ${errorMessage}`, 'error');
        } finally {
            // Reset button state
            saveBtn.disabled = false;
            saveBtnText.textContent = 'Save Changes';
            saveBtnSpinner.classList.add('hidden');

            console.log('🔄 Form submission completed (finally block)');
        }
    });

    // Add global error handler for debugging
    window.addEventListener('error', function(e) {
        console.error('🚨 GLOBAL ERROR:', e.error);
        console.error('🚨 Error details:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
        });
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('🚨 UNHANDLED PROMISE REJECTION:', e.reason);
        console.error('🚨 Promise:', e.promise);
    });
}

function showToast(message, type = 'info', duration = 5000) {
    console.log(`🍞 Toast: ${type.toUpperCase()} - ${message}`);

    // Remove any existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `toast-notification fixed top-4 right-4 px-6 py-4 rounded-lg shadow-xl z-50 max-w-md transition-all duration-300 transform translate-x-full`;

    // Style based on type
    if (type === 'success') {
        toast.className += ' bg-green-500 text-white border-l-4 border-green-600';
        toast.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span class="font-medium">${message}</span>
            </div>
        `;
    } else if (type === 'error') {
        toast.className += ' bg-red-500 text-white border-l-4 border-red-600';
        toast.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
                <span class="font-medium">${message}</span>
            </div>
        `;
    } else {
        toast.className += ' bg-blue-500 text-white border-l-4 border-blue-600';
        toast.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <span class="font-medium">${message}</span>
            </div>
        `;
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
        toast.classList.add('translate-x-0');
    }, 100);

    // Auto-remove after duration
    setTimeout(() => {
        if (document.body.contains(toast)) {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }
    }, duration);
}

function initializeDebugTest() {
    const debugBtn = document.getElementById('debug-test-btn');
    if (!debugBtn) return;

    debugBtn.addEventListener('click', async function() {
        console.log('🔧 MANUAL DEBUG TEST STARTED');
        console.log('='.repeat(50));

        try {
            // Test 1: Check current form data
            console.log('1️⃣ Checking current form data...');

            const form = document.getElementById('drop-edit-form');
            if (form) {
                const formData = new FormData(form);
                const currentData = Object.fromEntries(formData);
                console.log('📋 Current form data:', currentData);

                // Check specific styling fields
                const stylingFields = ['background_type', 'card_background_type', 'background_color', 'button_text_color', 'gradient_data'];
                stylingFields.forEach(field => {
                    const element = form.querySelector(`[name="${field}"]`);
                    if (element) {
                        console.log(`✅ ${field}: "${element.value}" (element found)`);
                    } else {
                        console.error(`❌ ${field}: element not found!`);
                    }
                });
            }

            // Test 2: Simple API connectivity test
            console.log('2️⃣ Testing API connectivity...');

            const testData = {
                background_color: '#00FF00', // Bright green to make it obvious
                background_type: 'solid',
                card_background_type: 'solid_white',
                button_color: '#FF0000',
                button_text_color: '#FFFFFF'
            };

            console.log('📤 Sending test data:', testData);

            const response = await fetch(`/api/drops/{{drop.id}}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                credentials: 'same-origin',
                body: JSON.stringify(testData)
            });

            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const result = await response.json();
                console.log('✅ SUCCESS! API response:', result);
                showToast('🔧 Debug test successful! Check if background changed to green.', 'success');

                // Force refresh the preview
                setTimeout(() => {
                    refreshPreview();
                }, 1000);

            } else {
                const errorText = await response.text();
                console.error('❌ API Error:', errorText);
                showToast(`🔧 Debug test failed: ${response.status} ${response.statusText}`, 'error');
            }

        } catch (error) {
            console.error('🚨 Debug test error:', error);
            showToast(`🔧 Debug test error: ${error.message}`, 'error');
        }
    });
}

async function loadAnalytics() {
    try {
        const response = await fetch(`/api/drops/{{drop.id}}/analytics`);
        if (response.ok) {
            const result = await response.json();
            const data = result.data;

            // Update analytics display
            document.getElementById('analytics-views').textContent = data.views || 0;
            document.getElementById('analytics-fans').textContent = data.fans || 0;
            document.getElementById('analytics-conversion').textContent = `${data.conversionRate || 0}%`;

            // Update recent signups
            const signupsContainer = document.getElementById('recent-signups');
            if (data.recentSignups && data.recentSignups.length > 0) {
                signupsContainer.innerHTML = data.recentSignups.map(signup => `
                    <div class="flex items-center justify-between py-2">
                        <span class="text-sm text-gray-900">${signup.email || signup.phone || 'Anonymous'}</span>
                        <span class="text-xs text-gray-500">${new Date(signup.created_at).toLocaleDateString()}</span>
                    </div>
                `).join('');
            } else {
                signupsContainer.innerHTML = '<p class="text-sm text-gray-500">No signups yet</p>';
            }
        }
    } catch (error) {
        console.error('Failed to load analytics:', error);
    }
}
</script>

<style>
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.preview-mode-btn.active {
    background-color: rgb(239 246 255);
    color: rgb(37 99 235);
    border-color: rgb(147 197 253);
}

/* Mobile preview styling */
#preview-container {
    transition: all 0.3s ease;
}

/* Enhanced Modern Color Picker Design */
.modern-color-input {
    width: 100% !important;
    height: 48px !important;
    padding: 0 !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    cursor: pointer !important;
    background: white !important;
    transition: all 0.2s ease !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.modern-color-input:hover {
    border-color: #3b82f6 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
}

.modern-color-input:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.modern-color-input::-webkit-color-swatch-wrapper {
    padding: 0 !important;
    border: none !important;
    border-radius: 10px !important;
}

.modern-color-input::-webkit-color-swatch {
    border: none !important;
    border-radius: 10px !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.modern-color-input::-moz-color-swatch {
    border: none !important;
    border-radius: 10px !important;
}

/* Form styling improvements */
.drop-form input:focus,
.drop-form textarea:focus,
.drop-form select:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Enhanced preview iframe styling */
#preview-iframe {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Advanced Gradient Editor Styling */
.gradient-type-btn {
    background: white;
    border-color: #d1d5db;
    color: #6b7280;
    transition: all 0.2s ease;
}

.gradient-type-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.gradient-direction-btn {
    background: white;
    border-color: #d1d5db;
    color: #6b7280;
    transition: all 0.2s ease;
}

.gradient-direction-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.gradient-direction-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

.gradient-preset {
    transition: all 0.2s ease;
    cursor: pointer;
}

.gradient-preset:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-stop-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background: white;
}

.color-stop-color {
    width: 24px;
    height: 24px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
}

.color-stop-position {
    flex: 1;
    padding: 2px 6px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    font-size: 11px;
}

.color-stop-remove {
    padding: 2px 6px;
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
    border-radius: 3px;
    font-size: 10px;
    cursor: pointer;
}

.color-stop-remove:hover {
    background: #fecaca;
}

/* Responsive grid improvements */
@media (max-width: 640px) {
    .grid.grid-cols-1.sm\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .modern-color-input {
        height: 40px !important;
    }
}

/* Loading state styling */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Toast notification styling */
.toast-notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>