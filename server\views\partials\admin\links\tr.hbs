<tr id="tr-{{id}}" {{#if swap_oob}}hx-swap-oob="true"{{/if}}>
  <td class="original-url right-fade">
    <a href="{{target}}" target="_blank" rel="noopener noreferrer">
      {{target}}
    </a>
    <p class="description">
      by&nbsp;
      {{~#if user_id~}}
        <a 
          aria-label="View user" 
          data-tooltip="View user" 
          hx-get="/api/users/admin"
          hx-target="closest table"
          hx-swap="outerHTML" 
          hx-sync="this:replace"
          hx-indicator="closest table"
          hx-vals='{"search":"{{email}}"}'
          onclick="setTab(event, 'tab-links')"
        >
          {{email}}
        </a>
        {{#ifEquals @root.query.user email}}
        {{else}}
          &nbsp;(
          <a 
            aria-label="View links by this user" 
            data-tooltip="View links by this user" 
            hx-get="/api/links/admin"
            hx-target="closest table"
            hx-swap="outerHTML" 
            hx-sync="this:replace"
            hx-indicator="closest table"
            hx-vals='{"user":"{{email}}"}'
          >
            view links
          </a>)
        {{/ifEquals}}
      {{~else~}}
        <a 
          aria-label="View anonymous links" 
          data-tooltip="View anonymous links" 
          hx-get="/api/links/admin"
          hx-target="closest table"
          hx-swap="outerHTML" 
          hx-sync="this:replace"
          hx-indicator="closest table"
          hx-vals='{"anonymous":"true"}'
        >
          Anonymous
        </a>
      {{~/if~}}
      &nbsp;{{~#if description~}}· {{description}}{{~/if}}
    </p>
  </td>
  <td class="created-at">
    {{relative_created_at}}
    {{#if relative_expire_in}}
      <p class="description">
        Expires in {{relative_expire_in}}
      </p>
    {{/if}}
  </td>
  <td class="short-link right-fade">
    <div class="short-link-wrapper">
      <div class="clipboard small">
        <button 
          aria-label="Copy" 
          hx-on:click="handleShortURLCopyLink(this);"
          data-url="{{link.url}}"
        >
          {{> icons/copy}}
        </button>
        {{> icons/check}}
      </div>
      <a href="{{link.url}}">/{{link.address}}</a>
    </div>
    <p class="description">
      <a 
        aria-label="View links by this domain" 
        data-tooltip="View links by this domain" 
        hx-get="/api/links/admin"
        hx-target="closest table"
        hx-swap="outerHTML" 
        hx-sync="this:replace"
        hx-vals='{"domain":"{{domain}}"}'
        hx-indicator="closest table"
      >{{domain}}</a>
    </p>
  </td>
  <td class="views">
    {{visit_count}}
  </td>
    {{> admin/links/actions}}
</tr>
<tr class="edit">
  <td class="loading">
    {{> icons/spinner}}
  </td>
</tr>