{"name": "kutt", "version": "3.2.3", "description": "Modern URL shortener.", "main": "./server/server.js", "scripts": {"dev": "node --watch-path=./server --watch-path=./custom server/server.js", "start": "node server/server.js --production", "build": "node scripts/generate-build-info.js", "prebuild": "node scripts/generate-build-info.js", "prestart": "node scripts/generate-build-info.js", "migrate": "knex migrate:latest", "migrate:make": "knex migrate:make", "migrate:crm": "knex migrate:latest --knexfile knexfile.crm.js", "migrate:crm:make": "knex migrate:make --knexfile knexfile.crm.js", "setup:crm": "node scripts/setup-crm.js", "verify:db": "node scripts/verify-database.js", "docs:build": "cd docs/api && node generate && cd ../.."}, "repository": {"type": "git", "url": "git+https://github.com/thedevs-network/kutt.git"}, "keywords": ["url-shortener"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/thedevs-network/kutt/issues"}, "homepage": "https://github.com/thedevs-network/kutt#readme", "dependencies": {"bcryptjs": "2.4.3", "better-sqlite3": "11.8.1", "bull": "4.16.5", "cookie-parser": "1.4.7", "cors": "2.8.5", "date-fns": "2.30.0", "dotenv": "16.4.7", "envalid": "8.0.0", "express": "4.21.2", "express-rate-limit": "7.5.0", "express-validator": "6.14.2", "geoip-lite": "1.4.10", "hbs": "4.2.0", "helmet": "7.1.0", "ioredis": "5.4.2", "isbot": "5.1.19", "jsonwebtoken": "9.0.2", "knex": "3.1.0", "ms": "2.1.3", "mysql2": "3.12.0", "nanoid": "3.3.8", "nodemailer": "6.9.16", "passport": "0.7.0", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "passport-localapikey-update": "0.6.0", "pg": "8.13.1", "pg-query-stream": "4.7.1", "rate-limit-redis": "4.2.0", "useragent": "2.3.0", "libphonenumber-js": "1.11.14", "twilio": "5.3.5"}, "devDependencies": {"@types/bcryptjs": "2.4.2", "@types/cookie-parser": "1.4.3", "@types/cors": "2.8.12", "@types/express": "4.17.14", "@types/hbs": "4.0.4", "@types/jsonwebtoken": "7.2.8", "@types/ms": "0.7.31", "@types/node": "18.11.9", "@types/nodemailer": "6.4.6", "@types/pg": "8.11.10", "redoc": "2.2.0"}}