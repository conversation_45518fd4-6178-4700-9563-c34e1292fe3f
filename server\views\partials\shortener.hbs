<main>
  <div id="shorturl">
    {{#if link}}
      <div class="clipboard">
        <button
          type="button"
          aria-label="Copy"
          hx-on:click="handleShortURLCopyLink(this);"
          data-url="{{url}}"
        >
        {{> icons/copy}}
        </button>
        {{> icons/check}}
      </div>
      <h1
        class="link"
        hx-on:click="handleShortURLCopyLink(this);"
        data-url="{{url}}"
      >
        {{link}}
      </h1>
    {{/if}}
    {{#unless link}}
        <h1>Cut your links <span>shorter</span>.</h1>
    {{/unless}}
  </div>
  <form
    id="shortener-form"
    hx-post="/api/links"
    hx-trigger="submit queue:none"
    hx-target="closest main"
    hx-swap="outerHTML"
    autocomplete="off"
  >
    <div class="target-wrapper {{#if errors.target}}error{{/if}}">
      <input
        id="target"
        name="target"
        type="text"
        placeholder="Paste your long URL"
        aria-label="target"
        autofocus="true"
        data-lpignore="true"
        hx-preserve="true"
      />
      <button class="submit">
        {{> icons/send}}
        {{> icons/spinner}}
      </button>
      {{#if errors.target}}<p class="error">{{errors.target}}</p>{{/if}}
      {{#unless errors}}
        {{#if error}}
          <p class="error">{{error}}</p>
        {{/if}}
      {{/unless}}
    </div>
    <label id="advanced" class="checkbox">
      <input
        name="show_advanced"
        type="checkbox"
        hx-on:change="htmx.toggleClass('#advanced-options', 'hidden')"
        {{#if show_advanced}}checked="true"{{/if}}
      />
      Show advanced options
    </label>
    <section id="advanced-options" class="{{#unless show_advanced}}hidden{{/unless}}">
      <div class="advanced-input-wrapper">
        <label class="{{#if errors.domain}}error{{/if}}">
          Domain:
          <select
            id="domain"
            name="domain"
            hx-preserve="true"
            hx-on:change="
              const elm = document.querySelector('#customurl-label span');
              if (!elm) return;
              elm.textContent = event.target.value + '/';
            "
          >
            <option value={{default_domain}}>{{default_domain}}</option>
            {{#each domains}}
              <option value={{address}}>{{address}}</option>
            {{/each}}
          </select>
          {{#if errors.domain}}<p class="error">{{errors.domain}}</p>{{/if}}
        </label>
        <label id="customurl-label" class="{{#if errors.customurl}}error{{/if}}">
          <span id="customurl-label-value" hx-preserve="true">{{default_domain}}/</span>
          <input
            type="text"
            id="customurl"
            name="customurl"
            placeholder="Custom address..."
            hx-preserve="true"
            autocomplete="off"
          />
          {{#if errors.customurl}}<p class="error">{{errors.customurl}}</p>{{/if}}
        </label>
        <label class="{{#if errors.password}}error{{/if}}">
          Password:
          <input
            type="password"
            id="password"
            name="password"
            placeholder="Password..."
            hx-preserve="true"
            autocomplete="off"
          />
          {{#if errors.password}}<p class="error">{{errors.password}}</p>{{/if}}
        </label>
      </div>
      <div class="advanced-input-wrapper">
        <label class="expire-in {{#if errors.expire_in}}error{{/if}}">
          Expire in:
          <input
            type="text"
            id="expire_in"
            name="expire_in"
            placeholder="2 minutes/hours/days"
            hx-preserve="true"
          />
          {{#if errors.expire_in}}<p class="error">{{errors.expire_in}}</p>{{/if}}
        </label>
        <label class="description {{#if errors.description}}error{{/if}}">
          Description:
          <input
            type="text"
            id="description"
            name="description"
            placeholder="Description..."
            hx-preserve="true"
          />
          {{#if errors.description}}<p class="error">{{errors.description}}</p>{{/if}}
        </label>
      </div>
      <div class="advanced-input-wrapper">
        <label class="show-preview {{#if errors.show_preview}}error{{/if}}">
          <input
            type="checkbox"
            id="show_preview"
            name="show_preview"
            value="true"
            hx-preserve="true"
          />
          Redirect Page
          {{#if errors.show_preview}}<p class="error">{{errors.show_preview}}</p>{{/if}}
        </label>
        <label class="metadata-title {{#if errors.meta_title}}error{{/if}}">
          Metadata Title:
          <input
            type="text"
            id="meta_title"
            name="meta_title"
            placeholder="Custom title for social media..."
            hx-preserve="true"
          />
          {{#if errors.meta_title}}<p class="error">{{errors.meta_title}}</p>{{/if}}
        </label>
        <label class="metadata-description {{#if errors.meta_description}}error{{/if}}">
          Metadata Description:
          <textarea
            id="meta_description"
            name="meta_description"
            placeholder="Custom description for social media..."
            hx-preserve="true"
            rows="3"
          ></textarea>
          {{#if errors.meta_description}}<p class="error">{{errors.meta_description}}</p>{{/if}}
        </label>
      </div>
      <div class="advanced-input-wrapper">
        <label class="metadata-image {{#if errors.meta_image}}error{{/if}}">
          Metadata Image URL:
          <input
            type="url"
            id="meta_image"
            name="meta_image"
            placeholder="Image URL for social media preview..."
            hx-preserve="true"
          />
          {{#if errors.meta_image}}<p class="error">{{errors.meta_image}}</p>{{/if}}
        </label>
      </div>
    </section>
  </form>
</main>