/* ===== MODERN DROP EDIT PAGE STYLES ===== */


/* ===== AWARD-WINNING TAB NAVIGATION SYSTEM ===== */

.tab-navigation {
    /* PREMIUM: Glass morphism background with gradient */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    /* AWARD-WINNING: Generous padding for premium feel */
    padding: 24px 32px 0 32px;
    margin-bottom: 0;
    /* STUNNING: Layered shadows for depth */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.tab-navigation::before {
    /* PREMIUM: Subtle gradient overlay */
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
}

.tab-nav-container {
    display: flex;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.tab-btn {
    /* AWARD-WINNING: Premium button design */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
    /* GENEROUS: Premium spacing */
    padding: 16px 20px 18px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    border-radius: 12px 12px 0 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    /* RESEARCH-BASED: Optimal touch target size */
    min-height: 56px;
    /* FIXED: Minimum width to prevent icon squishing */
    min-width: 120px;
    /* PREMIUM: Subtle shadow */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.tab-btn::before {
    /* STUNNING: Hover gradient overlay */
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
    border-radius: 12px 12px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-btn:hover {
    color: #475569;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(59, 130, 246, 0.2);
}

.tab-btn:hover::before {
    opacity: 1;
}

.tab-btn.active {
    /* AWARD-WINNING: Active state with premium styling */
    background: linear-gradient(135deg, #ffffff 0%, rgba(248, 250, 252, 0.95) 100%);
    color: #3b82f6;
    border-color: rgba(59, 130, 246, 0.3);
    border-bottom-color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15), 0 6px 20px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    z-index: 10;
    position: relative;
}

.tab-btn.active::before {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.04) 100%);
    opacity: 1;
}

.tab-btn.active::after {
    /* PREMIUM: Active indicator line */
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #6366f1 100%);
    border-radius: 2px 2px 0 0;
}

.tab-btn svg {
    /* FIXED: Large icons with consistent sizing */
    width: 24px !important;
    height: 24px !important;
    /* FIXED: Prevent shrinking */
    flex-shrink: 0;
    opacity: 0.8;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.tab-btn:hover svg {
    opacity: 0.9;
    transform: scale(1.05);
}

.tab-btn.active svg {
    opacity: 1;
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}


/* DESKTOP: Large icons with proper sizing */

@media (min-width: 769px) {
    .tab-btn svg {
        width: 28px !important;
        height: 28px !important;
        /* FIXED: Ensure icons don't shrink */
        flex-shrink: 0;
    }
    .tab-btn:hover svg {
        transform: scale(1.08);
    }
    .tab-btn.active svg {
        transform: scale(1.15);
    }
}

.tab-btn span {
    position: relative;
    z-index: 1;
    letter-spacing: 0.025em;
}


/* ===== TAB CONTENT SYSTEM ===== */

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


/* ===== SECTION HEADERS ===== */

.section-header {
    margin-bottom: 24px;
}

.section-header h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.section-description {
    margin: 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
}


/* ===== TAB PLACEHOLDER STYLING ===== */

.tab-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 40px;
}

.placeholder-content {
    text-align: center;
    max-width: 400px;
}

.placeholder-icon {
    width: 48px;
    height: 48px;
    color: #94a3b8;
    margin-bottom: 16px;
}

.placeholder-content h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #334155;
}

.placeholder-content p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
}

.coming-soon-badge {
    display: inline-block;
    padding: 4px 12px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    font-size: 12px;
    font-weight: 600;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}


/* ===== PLATFORM LINKS STYLING ===== */

.platform-links-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.platform-links-container .form-group {
    margin-bottom: 0;
}


/* ===== ORIGINAL STYLES ===== */

.drop-edit-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 120px);
}

.drop-edit-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 20px;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: 1px solid #d1d5db;
    padding: 8px 16px;
    border-radius: 8px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 14px;
}

.back-button:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.back-button svg {
    width: 16px;
    height: 16px;
}

.drop-edit-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #111827;
}

.drop-edit-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    align-items: start;
}

.drop-edit-form-container {
    /* AWARD-WINNING: Premium container design */
    background: linear-gradient(135deg, #ffffff 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 0 0 20px 20px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-top: none;
    overflow: hidden;
    /* STUNNING: Layered shadows for depth */
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
}

.drop-edit-form-container::before {
    /* PREMIUM: Subtle inner glow */
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%);
}

.form-section {
    /* RESEARCH-BASED: 8px grid system with optimal spacing */
    padding: 32px 40px;
    border-bottom: 1px solid rgba(243, 244, 246, 0.8);
    position: relative;
    /* OPTIMIZED: Consistent spacing between sections */
    margin-bottom: 8px;
}

.form-section::before {
    /* SUBTLE: Section separator gradient */
    content: '';
    position: absolute;
    bottom: 0;
    left: 40px;
    right: 40px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.1) 50%, transparent 100%);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section:last-child::before {
    display: none;
}

.form-section h2 {
    /* RESEARCH-BASED: Optimal title spacing */
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}


/* ===== MODERN COLOR PICKER DESIGN ===== */

.form-group input[type="color"] {
    /* RESEARCH-BASED: Modern color picker design */
    width: 80px;
    height: 48px;
    padding: 0;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    background: white;
    transition: all 0.2s ease;
    /* MODERN: Remove default styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* PREMIUM: Beautiful shadow */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.form-group input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 10px;
}

.form-group input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 10px;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.form-group input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: 10px;
}

.form-group input[type="color"]:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-group input[type="color"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* ===== ENHANCED STYLING SECTIONS ===== */

.styling-section {
    margin-bottom: 32px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.subsection-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}


/* ===== MODERN COLOR PICKER GRID SYSTEM ===== */

.color-picker-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    /* RESEARCH-BASED: Optimal spacing for color pickers */
    margin-top: 8px;
}

.color-picker-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.color-picker-label {
    display: block;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
    cursor: pointer;
    /* RESEARCH-BASED: Optimal label spacing */
    margin-bottom: 0;
    padding: 8px 0;
    transition: color 0.2s ease;
}

.color-picker-label:hover {
    color: #1f2937;
}

.modern-color-input {
    /* ENHANCED: Modern color input styling */
    width: 100%;
    height: 48px;
    padding: 0;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    background: white;
    transition: all 0.2s ease;
    /* MODERN: Remove default styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* PREMIUM: Beautiful shadow */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.modern-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 10px;
}

.modern-color-input::-webkit-color-swatch {
    border: none;
    border-radius: 10px;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.modern-color-input::-moz-color-swatch {
    border: none;
    border-radius: 10px;
}

.modern-color-input:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-color-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.slug-input-wrapper {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.slug-prefix {
    background: #f9fafb;
    padding: 12px;
    color: #6b7280;
    font-size: 14px;
    border-right: 1px solid #e5e7eb;
    white-space: nowrap;
}

.slug-input-wrapper input {
    border: none;
    border-radius: 0;
    flex: 1;
}

.slug-input-wrapper input:focus {
    box-shadow: none;
}

.slug-input-wrapper:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6b7280;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 12px;
}


/* ===== AWARD-WINNING MODERN TOGGLE SWITCHES ===== */

.modern-setting-group {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    /* RESEARCH-BASED: Optimal card padding using 8px grid */
    padding: 24px;
    /* OPTIMIZED: Consistent spacing between cards */
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-setting-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
}

.modern-setting-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

.setting-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 24px;
}

.setting-info {
    flex: 1;
}


/* ===== WORLD-CLASS STATUS INDICATOR SYSTEM ===== */

.setting-control-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.setting-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.world-class-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    /* PREMIUM: Glass morphism effect */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.world-class-status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    pointer-events: none;
}

.world-class-status-indicator.live {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3), 0 2px 4px rgba(16, 185, 129, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.world-class-status-indicator.inactive {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3), 0 2px 4px rgba(239, 68, 68, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-pulse-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    position: relative;
    flex-shrink: 0;
}

.world-class-status-indicator.live .status-pulse-dot {
    animation: pulse-live 2s infinite;
}

.world-class-status-indicator.inactive .status-pulse-dot {
    animation: pulse-inactive 2s infinite;
}

@keyframes pulse-live {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

@keyframes pulse-inactive {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

.status-text {
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.world-class-status-indicator:hover {
    transform: translateY(-1px);
}

.world-class-status-indicator.live:hover {
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4), 0 4px 8px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.world-class-status-indicator.inactive:hover {
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), 0 4px 8px rgba(239, 68, 68, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.setting-description {
    margin: 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
}


/* PREMIUM MODERN TOGGLE SWITCH - OPTIMIZED */

.modern-toggle {
    position: relative;
    display: inline-block;
    cursor: pointer;
    /* PREMIUM: Subtle hover area */
    padding: 4px;
    border-radius: 32px;
    transition: all 0.3s ease;
}

.modern-toggle input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-slider {
    position: relative;
    display: block;
    /* OPTIMIZED: Perfect proportions */
    width: 54px;
    height: 32px;
    /* PREMIUM: Beautiful gradient background */
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-radius: 32px;
    /* OPTIMIZED: Smooth cubic-bezier animation */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* PREMIUM: Layered shadows for depth */
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* OPTIMIZED: Subtle border */
    border: 1px solid rgba(203, 213, 225, 0.6);
}

.toggle-thumb {
    position: absolute;
    /* OPTIMIZED: Perfect positioning */
    top: 3px;
    left: 3px;
    /* OPTIMIZED: Ideal thumb size */
    width: 24px;
    height: 24px;
    /* PREMIUM: Gradient thumb with glass effect */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 50%;
    /* OPTIMIZED: Smooth animation */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* PREMIUM: Beautiful drop shadow */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    /* OPTIMIZED: Clean border */
    border: 1px solid rgba(226, 232, 240, 0.8);
}


/* PREMIUM CHECKED STATE - OPTIMIZED */

.modern-toggle input[type="checkbox"]:checked+.toggle-slider {
    /* PREMIUM: Beautiful blue gradient */
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    /* OPTIMIZED: Enhanced shadows for active state */
    box-shadow: inset 0 2px 4px rgba(59, 130, 246, 0.2), 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 6px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* PREMIUM: Blue border */
    border-color: rgba(59, 130, 246, 0.8);
}

.modern-toggle input[type="checkbox"]:checked+.toggle-slider .toggle-thumb {
    /* OPTIMIZED: Perfect thumb movement (54px - 24px - 6px = 24px) */
    transform: translateX(24px);
    /* PREMIUM: Enhanced thumb for active state */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    /* OPTIMIZED: Beautiful active shadow */
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    /* PREMIUM: Subtle blue border */
    border-color: rgba(59, 130, 246, 0.2);
}


/* PREMIUM HOVER STATES - OPTIMIZED */

.modern-toggle:hover {
    /* PREMIUM: Subtle container hover effect */
    background: rgba(59, 130, 246, 0.05);
}

.modern-toggle:hover .toggle-slider {
    /* OPTIMIZED: Subtle scale and enhanced shadows */
    transform: scale(1.02);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.modern-toggle:hover input[type="checkbox"]:not(:checked)+.toggle-slider {
    /* PREMIUM: Enhanced gray gradient on hover */
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: rgba(148, 163, 184, 0.8);
}

.modern-toggle:hover input[type="checkbox"]:checked+.toggle-slider {
    /* PREMIUM: Enhanced blue gradient on hover */
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: inset 0 2px 4px rgba(59, 130, 246, 0.3), 0 0 0 4px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15);
}


/* PREMIUM FOCUS STATES - OPTIMIZED */

.modern-toggle input[type="checkbox"]:focus+.toggle-slider {
    /* OPTIMIZED: Beautiful focus ring */
    outline: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.3), 0 2px 6px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-toggle input[type="checkbox"]:focus:checked+.toggle-slider {
    /* PREMIUM: Enhanced focus for checked state */
    box-shadow: inset 0 2px 4px rgba(59, 130, 246, 0.2), 0 0 0 4px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15);
}


/* PREMIUM DISABLED STATE - OPTIMIZED */

.modern-toggle input[type="checkbox"]:disabled+.toggle-slider {
    /* PREMIUM: Disabled gradient */
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    opacity: 0.6;
    cursor: not-allowed;
    /* OPTIMIZED: Reduced shadows */
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.05);
    border-color: rgba(203, 213, 225, 0.4);
}

.modern-toggle input[type="checkbox"]:disabled+.toggle-slider .toggle-thumb {
    /* PREMIUM: Disabled thumb styling */
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    opacity: 0.8;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(203, 213, 225, 0.4);
}

.modern-toggle input[type="checkbox"]:disabled:checked+.toggle-slider {
    /* PREMIUM: Disabled checked state */
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
    opacity: 0.6;
}


/* STATUS INDICATOR */

.status-indicator {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.status-indicator.inactive {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}


/* LEGACY CHECKBOX SUPPORT */

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    font-size: 14px;
    color: #374151;
}


/* Enterprise Setting Groups */

.setting-group {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    background: #f9fafb;
}

.setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.setting-status {
    flex-shrink: 0;
}

.setting-description {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e5e7eb;
}

.setting-description p {
    margin: 0;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
}


/* 🚀 ENTERPRISE-GRADE FORM ACTIONS */

.form-actions {
    /* DESKTOP: Traditional right-aligned layout */
    padding: 24px;
    background: linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    border-top: 1px solid #e5e7eb;
    /* FUTURE-PROOF: Smooth transitions */
    transition: all 0.3s ease;
    /* ENTERPRISE: Subtle shadow for depth */
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}


/* 🎯 RESEARCH-BASED ENTERPRISE BUTTONS */

.button {
    /* ACCESSIBILITY: 44px minimum touch target */
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    border: 2px solid;
    /* ACCESSIBILITY: Minimum touch target */
    min-height: 48px;
    min-width: 120px;
    /* ENTERPRISE: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    /* ACCESSIBILITY: Focus outline */
    outline: none;
    position: relative;
    overflow: hidden;
}


/* ACCESSIBILITY: Focus state */

.button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}


/* SECONDARY BUTTON - Research-based neutral design */

.button.secondary {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #475569;
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.button.secondary:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #94a3b8;
    color: #334155;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.button.secondary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.06);
}


/* PRIMARY BUTTON - Research-based blue system */

.button.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3), 0 2px 4px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.button.primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4), 0 4px 8px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.button.primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3), inset 0 2px 4px rgba(0, 0, 0, 0.1);
}


/* LOADING STATE */

.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.button-spinner {
    display: none;
    align-items: center;
    justify-content: center;
}

.button-text {
    display: flex;
    align-items: center;
    gap: 8px;
}

.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}


/* 🎯 CHANGES DETECTED STATE - Smart highlighting */

.button.primary.changes-detected {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4), 0 3px 8px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: changesDetectedPulse 2s ease-in-out infinite;
}

.button.primary.changes-detected:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #059669;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.5), 0 4px 10px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes changesDetectedPulse {
    0%,
    100% {
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4), 0 3px 8px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6), 0 4px 10px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}


/* 🔴 RED CANCEL BUTTON - Changes detected */

.button.secondary.changes-detected {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #ef4444;
    color: white;
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), 0 3px 8px rgba(239, 68, 68, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: cancelButtonPulse 2s ease-in-out infinite;
}

.button.secondary.changes-detected:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-color: #dc2626;
    color: white;
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.5), 0 4px 10px rgba(239, 68, 68, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes cancelButtonPulse {
    0%,
    100% {
        box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), 0 3px 8px rgba(239, 68, 68, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 8px 20px rgba(239, 68, 68, 0.6), 0 4px 10px rgba(239, 68, 68, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}

.button-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}


/* Preview Panel */

.drop-preview-panel {
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    position: sticky;
    top: 20px;
    height: fit-content;
}

.drop-preview-panel h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

.preview-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.preview-drop {
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.preview-header h4 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
}

.preview-header p {
    margin: 0 0 20px 0;
    opacity: 0.9;
    font-size: 14px;
}

.preview-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.preview-form input {
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
}

.preview-form button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    color: white;
    font-size: 14px;
}


/* Error and Success Messages */

.error-message,
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.success-message {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}


/* Mobile Responsive */

@media (max-width: 768px) {
    /* MODERN: Mobile tab navigation */
    .tab-navigation {
        padding: 0 16px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    .tab-nav-container {
        min-width: max-content;
        gap: 0;
    }
    .tab-btn {
        padding: 12px 16px;
        min-width: 80px;
        justify-content: center;
        font-size: 13px;
        white-space: nowrap;
    }
    .tab-btn span {
        display: none;
    }
    .tab-btn svg {
        width: 18px;
        height: 18px;
    }
    /* Show text on active tab */
    .tab-btn.active span {
        display: inline;
        margin-left: 4px;
    }
    .platform-links-container {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    .section-header h2 {
        font-size: 18px;
    }
    .drop-edit-container {
        padding: 16px;
    }
    .drop-edit-content {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    .drop-preview-panel {
        order: 2;
        position: static;
        margin-top: 20px;
    }
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    .drop-edit-header h1 {
        font-size: 24px;
    }
    .form-row {
        grid-template-columns: 1fr;
    }
    /* 🚀 FIXED MOBILE BUTTON SYSTEM */
    .form-actions {
        /* FIXED: Normal flow instead of fixed positioning */
        position: static;
        /* ENTERPRISE: Modern gradient background */
        background: linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%);
        /* ACCESSIBILITY: Proper spacing and layout */
        padding: 20px;
        margin: 24px 0 0 0;
        display: flex;
        flex-direction: row;
        gap: 12px;
        /* ENTERPRISE: Premium shadow system */
        border-top: 1px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        /* FUTURE-PROOF: Smooth transitions */
        transition: all 0.3s ease;
    }
    /* RESEARCH-BASED: Optimal button sizing for mobile */
    .button {
        /* ACCESSIBILITY: Full-width touch targets */
        flex: 1;
        /* ENTERPRISE: Optimal mobile sizing */
        padding: 16px 20px;
        font-size: 16px;
        font-weight: 700;
        min-height: 52px;
        /* MODERN: Better border radius for mobile */
        border-radius: 14px;
        /* ACCESSIBILITY: Better touch feedback */
        -webkit-tap-highlight-color: transparent;
    }
    /* ENTERPRISE: Secondary button gets less visual weight */
    .button.secondary {
        flex: 0.8;
        order: 1;
    }
    /* ENTERPRISE: Primary button gets more visual weight */
    .button.primary {
        flex: 1.2;
        order: 2;
    }
    .slug-prefix {
        font-size: 12px;
        padding: 10px;
    }
    .error-message,
    .success-message {
        position: static;
        margin: 16px 0;
    }
    /* Mobile Preview Optimizations */
    .preview-header {
        padding: 16px;
        position: relative;
    }
    .preview-header h3 {
        font-size: 14px;
    }
    .preview-controls {
        gap: 4px;
    }
    .preview-device-btn {
        width: 28px;
        height: 28px;
    }
    .preview-container {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;
    }
    .preview-viewport {
        transform: scale(0.8);
        transform-origin: top center;
        margin-bottom: -100px;
    }
    .preview-viewport.mobile {
        max-width: 300px;
        transform: scale(0.9);
        margin-bottom: -50px;
    }
    .preview-drop {
        min-height: 400px;
    }
    .preview-content {
        padding: 20px 16px;
    }
    .preview-info h4 {
        font-size: 20px;
    }
    .preview-info p {
        font-size: 14px;
    }
    .preview-input,
    .preview-form button {
        padding: 12px;
        font-size: 14px;
    }
    .preview-info-panel {
        padding: 12px 16px;
    }
    /* Collapsible Preview */
    .preview-toggle {
        display: block;
        width: 100%;
        background: #f9fafb;
        border: none;
        padding: 12px 16px;
        text-align: left;
        font-weight: 500;
        color: #374151;
        border-bottom: 1px solid #e5e7eb;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .preview-toggle:hover {
        background: #f3f4f6;
    }
    .preview-toggle-icon {
        transition: transform 0.2s ease;
    }
    .preview-toggle.collapsed .preview-toggle-icon {
        transform: rotate(-90deg);
    }
    .preview-collapsible {
        display: block;
    }
    .preview-collapsible.collapsed {
        display: none;
    }
}

@media (max-width: 480px) {
    .drop-edit-container {
        padding: 12px;
        padding-bottom: 80px;
        /* Space for sticky actions */
    }
    .form-section {
        padding: 16px;
        margin-bottom: 16px;
    }
    /* 🚀 SMALL MOBILE: Enhanced button system */
    .form-actions {
        /* ENTERPRISE: Optimized for small screens */
        padding: 14px 16px calc(14px + env(safe-area-inset-bottom));
        gap: 10px;
        /* ACCESSIBILITY: Slightly smaller but still compliant */
        box-shadow: 0 -6px 24px rgba(0, 0, 0, 0.1), 0 -3px 12px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    .button {
        /* SMALL MOBILE: Optimized sizing */
        padding: 14px 16px;
        font-size: 15px;
        min-height: 48px;
        border-radius: 12px;
    }
    .drop-preview-panel {
        padding: 0;
        margin-top: 16px;
        border-radius: 8px;
    }
    .preview-header {
        padding: 12px;
    }
    .preview-header h3 {
        font-size: 13px;
    }
    .preview-container {
        padding: 12px;
        max-height: 300px;
    }
    .preview-viewport {
        transform: scale(0.7);
        margin-bottom: -120px;
    }
    .preview-viewport.mobile {
        max-width: 280px;
        transform: scale(0.8);
        margin-bottom: -80px;
    }
    .preview-content {
        padding: 16px 12px;
    }
    .preview-info h4 {
        font-size: 18px;
    }
    .preview-info p {
        font-size: 13px;
    }
    .preview-input,
    .preview-form button {
        padding: 10px;
        font-size: 13px;
    }
    .preview-info-panel {
        padding: 10px 12px;
    }
    .url-display {
        font-size: 11px;
        padding: 6px 8px;
    }
    /* Hide desktop preview controls on very small screens */
    .preview-device-btn[data-device="desktop"] {
        display: none;
    }
    /* Compact form styling */
    .form-group {
        margin-bottom: 16px;
    }
    .form-group label {
        font-size: 13px;
        margin-bottom: 6px;
    }
    .form-input,
    .form-textarea {
        padding: 12px;
        font-size: 14px;
    }
    .checkbox-label {
        font-size: 13px;
    }
    .form-help {
        font-size: 11px;
    }
}


/* Enhanced Preview Panel Styles */

.drop-preview-panel {
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 0;
    position: sticky;
    top: 20px;
    height: fit-content;
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;
}

.preview-title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.preview-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

.preview-device-indicator {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-controls {
    display: flex;
    gap: 8px;
}

.preview-device-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preview-device-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

.preview-device-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.preview-container {
    padding: 24px;
    background: #f8fafc;
}

.preview-viewport {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.preview-viewport.desktop {
    max-width: 100%;
    margin: 0 auto;
    min-height: 600px;
}

.preview-viewport.mobile {
    max-width: 375px;
    margin: 0 auto;
    min-height: 500px;
    /* Simulate mobile device frame */
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.preview-drop {
    min-height: 500px;
    background: #ffffff;
    color: #000000;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    transition: all 0.3s ease;
}


/* Device-specific preview styling - JavaScript handles exact values */

.preview-viewport.mobile {
    /* Mobile device frame styling */
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.preview-viewport.desktop {
    /* Desktop frame styling */
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}


/* Base preview styling with transitions for smooth device switching */

.preview-drop,
.preview-info h4,
.preview-info p,
.preview-input,
.preview-form button,
.preview-content,
.preview-drop-header,
.preview-footer,
.preview-brand-text {
    transition: all 0.3s ease;
}


/* GLASSMORPHISM PREVIEW INTEGRATION */


/* Preview now uses the same glassmorphism CSS as the actual drop landing page */


/* Ensure glassmorphism effects work in preview iframe */

.preview-viewport iframe {
    /* Enable backdrop-filter support in iframe */
    isolation: isolate;
}


/* Preview-specific overrides to ensure proper display within viewport */

.preview-viewport .drop-container {
    min-height: 500px;
    height: auto;
    border-radius: inherit;
    overflow: hidden;
}

.preview-viewport .brand-link {
    pointer-events: none;
    /* Disable clicks in preview */
}

.preview-viewport .signup-button {
    pointer-events: none;
    /* Disable clicks in preview */
    cursor: default;
}

.preview-viewport .form-input {
    pointer-events: none;
    /* Disable interaction in preview */
}


/* Ensure preview matches live page structure exactly */

.preview-container,
.preview-viewport {
    /* Page background (behind the card) */
    background-color: inherit !important;
}

.preview-viewport .drop-container {
    /* Card background and structure to match live page */
    /* Remove background override - let CSS variables work */
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    overflow: hidden;
}


/* RESEARCH-BASED PREVIEW SYSTEM - MOBILE-FIRST RESPONSIVE */


/* Mobile preview - force mobile layout */

.preview-viewport.mobile {
    /* Force mobile breakpoint behavior */
    width: 375px;
    max-width: 375px;
    /* Simulate mobile width */
}

.preview-viewport.mobile .drop-page-wrapper {
    /* RESEARCH-BASED: Match new mobile system with reduced padding */
    padding: 10px !important;
    min-height: 100% !important;
    /* BEAUTIFUL: Modern gradient background */
    background: linear-gradient(135deg, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 0%, color-mix(in srgb, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 85%, #ffffff) 100%) !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    box-sizing: border-box !important;
}

.preview-viewport.mobile .drop-container {
    /* RESEARCH-BASED: Match new mobile container */
    width: 100% !important;
    margin: 0 !important;
    min-height: 500px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
}


/* Desktop preview - force desktop layout */

.preview-viewport.desktop {
    /* Force desktop breakpoint behavior */
    width: 100%;
    min-width: 1024px;
    /* Ensure desktop breakpoint matches new system */
}

.preview-viewport.desktop .drop-page-wrapper {
    /* RESEARCH-BASED: Match new desktop system with reduced padding */
    padding: 40px 20px !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
    /* BEAUTIFUL: Modern gradient background */
    background: linear-gradient(135deg, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 0%, color-mix(in srgb, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 85%, #ffffff) 100%) !important;
}

.preview-viewport.desktop .drop-container {
    /* RESEARCH-BASED: Match new desktop container */
    max-width: 700px !important;
    width: 100% !important;
    margin: 0 !important;
    border-radius: 24px !important;
    /* BEAUTIFUL: Modern layered shadows */
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 20px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    /* Modern: Subtle border */
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    min-height: auto !important;
}


/* Mobile responsive overrides for preview to match shared CSS exactly */

.preview-viewport.mobile .drop-header {
    padding: 12px 16px !important;
}

.preview-viewport.mobile .brand-text {
    font-size: 16px !important;
}

.preview-viewport.mobile .drop-main {
    padding: 24px 16px !important;
}

.preview-viewport.mobile .drop-title {
    /* MODERN: Match new mobile typography */
    font-size: 32px !important;
    margin-bottom: 24px !important;
}

.preview-viewport.mobile .drop-description p {
    /* ENHANCED: Match new mobile typography */
    font-size: 18px !important;
    margin-bottom: 32px !important;
}

.preview-viewport.mobile .drop-cover-image {
    /* LARGER: Match new mobile image sizing */
    width: 280px !important;
    height: 280px !important;
}

.preview-viewport.mobile .form-input {
    padding: 14px !important;
    font-size: 16px !important;
    /* Prevent zoom on iOS */
}

.preview-viewport.mobile .signup-button {
    padding: 14px !important;
    font-size: 16px !important;
}

.preview-viewport.mobile .drop-cover-image {
    width: 150px !important;
    height: 150px !important;
}

.preview-viewport.mobile .drop-footer {
    padding: 16px !important;
}


/* Desktop responsive overrides for preview */

.preview-viewport.desktop .drop-header {
    padding: 16px 24px !important;
}

.preview-viewport.desktop .brand-text {
    font-size: 18px !important;
}

.preview-viewport.desktop .drop-main {
    padding: 40px 24px !important;
}

.preview-viewport.desktop .drop-title {
    /* STUNNING: Match new desktop typography */
    font-size: 48px !important;
    margin-bottom: 40px !important;
}

.preview-viewport.desktop .drop-description p {
    /* PREMIUM: Match new desktop typography */
    font-size: 22px !important;
    margin-bottom: 48px !important;
}

.preview-viewport.desktop .drop-cover-image {
    /* IMPRESSIVE: Match new desktop image sizing */
    width: 360px !important;
    height: 360px !important;
}

.preview-viewport.desktop .form-input {
    padding: 16px !important;
    font-size: 16px !important;
}

.preview-viewport.desktop .signup-button {
    padding: 16px 32px !important;
    font-size: 16px !important;
}

.preview-viewport.desktop .drop-cover-image {
    width: 200px !important;
    height: 200px !important;
}

.preview-viewport.desktop .drop-footer {
    padding: 24px !important;
}


/* Ensure header and footer don't override background */

.preview-viewport .drop-header,
.preview-viewport .drop-footer {
    background: transparent !important;
}


/* Remove button overrides - let CSS variables work */

.preview-viewport .signup-button {
    border: none !important;
}


/* CSS variables will handle background colors automatically */


/* Ensure all text elements inherit the text color but allow JS override */

.preview-viewport .drop-title,
.preview-viewport .drop-description p,
.preview-viewport .brand-text,
.preview-viewport .signup-count,
.preview-viewport .drop-footer p {
    color: inherit;
}


/* Ensure cover image cropping matches between preview and actual page - Perfect square */

.preview-viewport .drop-cover-image {
    margin-bottom: 32px;
    border-radius: 12px;
    overflow: hidden;
    width: 200px;
    height: 200px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

.preview-viewport .drop-cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.preview-info-panel {
    padding: 20px 24px;
    border-top: 1px solid #f3f4f6;
    background: #f9fafb;
}

.preview-url {
    margin-bottom: 0;
}

.preview-url label {
    display: block;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.url-display {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    text-decoration: none;
    transition: all 0.2s ease;
}

.url-display.clickable-url {
    cursor: pointer;
    gap: 8px;
}

.url-display.clickable-url:hover {
    background: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.url-prefix {
    color: #6b7280;
}

.url-slug {
    color: #3b82f6;
    font-weight: 600;
}

.external-link-icon {
    color: #6b7280;
    opacity: 0.7;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.url-display.clickable-url:hover .external-link-icon {
    color: #3b82f6;
    opacity: 1;
    transform: scale(1.1);
}


/* Hide preview toggle on desktop */

.preview-toggle {
    display: none;
}


/* Show preview toggle only on mobile */

@media (max-width: 768px) {
    .preview-toggle {
        display: flex;
    }
    .preview-toggle-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .preview-quick-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: #3b82f6;
        color: white;
        border-radius: 4px;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    .preview-quick-link:hover {
        background: #2563eb;
        transform: scale(1.05);
    }
    .preview-header {
        display: none;
    }
    .preview-collapsible:not(.collapsed) .preview-header {
        display: flex;
    }
}


/* Notification Banner Styles */

.notification-banner {
    position: fixed;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
}

.notification-banner.show {
    top: 20px;
    opacity: 1;
}

.notification-banner.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(21, 128, 61, 0.95) 100%);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: white;
}

.notification-banner.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(185, 28, 28, 0.95) 100%);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: white;
}

.banner-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    gap: 12px;
}

.banner-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.banner-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
}

.banner-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    opacity: 0.8;
    flex-shrink: 0;
}

.banner-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}


/* Mobile responsive banners */

@media (max-width: 768px) {
    /* AWARD-WINNING: Mobile tab navigation */
    .tab-navigation {
        padding: 16px 20px 0 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        /* MOBILE: Subtle shadow adjustment */
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    .tab-nav-container {
        min-width: max-content;
        gap: 6px;
    }
    .tab-btn {
        /* MOBILE: Compact but premium styling */
        padding: 14px 18px 16px 18px;
        min-width: 85px;
        justify-content: center;
        font-size: 13px;
        white-space: nowrap;
        min-height: 52px;
        border-radius: 10px 10px 0 0;
        gap: 8px;
    }
    .tab-btn span {
        display: none;
    }
    .tab-btn svg {
        width: 20px;
        height: 20px;
    }
    /* Show text on active tab */
    .tab-btn.active span {
        display: inline;
        margin-left: 6px;
        font-size: 12px;
    }
    .tab-btn.active {
        min-width: 105px;
    }
    .form-section {
        /* MOBILE: Optimized spacing using 8px grid */
        padding: 24px 20px;
    }
    .form-section::before {
        left: 20px;
        right: 20px;
    }
    .platform-links-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    .section-header h2 {
        font-size: 18px;
    }
    /* MOBILE: Modern toggle adjustments */
    .modern-setting-group {
        padding: 20px;
        border-radius: 12px;
    }
    /* MOBILE: Color picker grid adjustments */
    .color-picker-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    .color-picker-group {
        gap: 6px;
    }
    .color-picker-label {
        padding: 6px 0;
        font-size: 13px;
    }
    .modern-color-input {
        height: 44px;
    }
    .setting-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    .setting-control {
        justify-content: space-between;
        align-items: center;
    }
    /* PREMIUM MOBILE: Optimized proportions */
    .toggle-slider {
        width: 50px;
        height: 30px;
    }
    .toggle-thumb {
        width: 22px;
        height: 22px;
        top: 3px;
        left: 3px;
    }
    .modern-toggle input[type="checkbox"]:checked+.toggle-slider .toggle-thumb {
        /* OPTIMIZED MOBILE: Perfect movement (50px - 22px - 6px = 22px) */
        transform: translateX(22px);
    }
    .setting-title {
        font-size: 15px;
    }
    .setting-description {
        font-size: 13px;
    }
    /* MOBILE: World-class status indicator adjustments */
    .setting-control-column {
        gap: 8px;
    }
    .world-class-status-indicator {
        font-size: 11px;
        padding: 5px 10px;
        gap: 6px;
    }
    .status-pulse-dot {
        width: 6px;
        height: 6px;
    }
    .drop-edit-container {
        padding: 16px;
        /* FIXED: Normal padding since buttons are no longer fixed */
    }
    .drop-edit-content {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    /* 🚀 MOBILE UX: Preview at top for easy access when collapsed */
    .drop-preview-panel {
        order: 1;
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        margin-bottom: 20px;
        border-radius: 0 0 16px 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .drop-edit-main {
        order: 2;
    }
    .notification-banner {
        left: 16px;
        right: 16px;
        transform: none;
        min-width: auto;
        max-width: none;
    }
    .notification-banner.show {
        top: 16px;
    }
    .banner-content {
        padding: 14px 16px;
    }
    .banner-message {
        font-size: 13px;
    }
}