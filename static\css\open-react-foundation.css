/* ===== OPEN REACT TEMPLATE FOUNDATION ===== */


/* Modern foundation styles based on Open React Template */


/* Completely separate from dashboard TailwindCSS styling */


/* ===== OPEN REACT TEMPLATE TYPOGRAPHY SYSTEM ===== */

:root {
    /* Open React Template Font System */
    --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    --font-nacelle: 'Nacelle', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    /* Open React Template Typography Scale */
    --text-xs: 0.8125rem;
    --text-xs--line-height: 1.5384;
    --text-sm: 0.875rem;
    --text-sm--line-height: 1.5715;
    --text-base: 0.9375rem;
    --text-base--line-height: 1.5333;
    --text-base--letter-spacing: -0.0125em;
    --text-lg: 1.125rem;
    --text-lg--line-height: 1.5;
    --text-lg--letter-spacing: -0.0125em;
    --text-xl: 1.25rem;
    --text-xl--line-height: 1.5;
    --text-xl--letter-spacing: -0.0125em;
    --text-2xl: 1.5rem;
    --text-2xl--line-height: 1.415;
    --text-2xl--letter-spacing: -0.0268em;
    --text-3xl: 1.75rem;
    --text-3xl--line-height: 1.3571;
    --text-3xl--letter-spacing: -0.0268em;
    --text-4xl: 2.5rem;
    --text-4xl--line-height: 1.1;
    --text-4xl--letter-spacing: -0.0268em;
    --text-5xl: 3.5rem;
    --text-5xl--line-height: 1;
    --text-5xl--letter-spacing: -0.0268em;
    --text-6xl: 4rem;
    --text-6xl--line-height: 1;
    --text-6xl--letter-spacing: -0.0268em;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-7xl--letter-spacing: -0.0268em;
    /* Open React Template Animation System */
    --animate-shine: shine 5s ease-in-out 500ms infinite;
    --animate-gradient: gradient 6s linear infinite;
    /* Open React Template Color System */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    /* Open React Template Gradient System */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}


/* ===== OPEN REACT TEMPLATE ANIMATIONS ===== */

@keyframes shine {
    0% {
        top: 0;
        transform: scaleY(5);
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    20% {
        top: 100%;
        transform: scaleY(10);
        opacity: 0;
    }
    100% {
        top: 100%;
        transform: scaleY(1);
        opacity: 0;
    }
}

@keyframes gradient {
    to {
        background-position: 200% center;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}


/* ===== OPEN REACT TEMPLATE HERO SECTION ===== */

.open-hero-section {
    /* Hero section foundation */
    position: relative;
    padding: 80px 0 120px;
    overflow: hidden;
    /* Modern gradient background */
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.open-hero-container {
    /* Hero container layout */
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
    z-index: 2;
}

.open-hero-content {
    /* Hero content layout */
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    animation: fadeInUp 0.8s ease-out;
}

.open-hero-title {
    /* Hero title styling */
    font-family: var(--font-nacelle);
    font-size: var(--text-5xl);
    line-height: var(--text-5xl--line-height);
    letter-spacing: var(--text-5xl--letter-spacing);
    font-weight: 700;
    margin: 0 0 24px;
    /* Dynamic color integration */
    color: var(--drop-title-color, var(--color-gray-900));
    /* Text gradient effect */
    background: linear-gradient(135deg, var(--drop-title-color, var(--color-gray-900)) 0%, color-mix(in srgb, var(--drop-title-color, var(--color-gray-900)) 80%, #667eea) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.open-hero-description {
    /* Hero description styling */
    font-family: var(--font-inter);
    font-size: var(--text-xl);
    line-height: var(--text-xl--line-height);
    letter-spacing: var(--text-xl--letter-spacing);
    font-weight: 400;
    margin: 0 0 40px;
    /* Dynamic color integration */
    color: var(--drop-description-color, var(--color-gray-600));
    /* Enhanced readability */
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.open-hero-stats {
    /* Hero stats styling */
    font-family: var(--font-inter);
    font-size: var(--text-sm);
    line-height: var(--text-sm--line-height);
    font-weight: 500;
    margin: 0 0 48px;
    color: var(--color-gray-500);
    /* Subtle animation */
    animation: slideInLeft 0.8s ease-out 0.2s both;
}


/* ===== OPEN REACT TEMPLATE CTA SECTION ===== */

.open-cta-section {
    /* CTA section foundation */
    position: relative;
    padding: 48px 0;
    text-align: center;
}

.open-cta-container {
    /* CTA container layout */
    max-width: 600px;
    margin: 0 auto;
    padding: 0 24px;
}

.open-cta-buttons {
    /* CTA buttons layout */
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;
    margin: 32px 0;
}

.open-primary-button {
    /* Primary button styling */
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* Typography */
    font-family: var(--font-inter);
    font-size: var(--text-base);
    font-weight: 600;
    text-decoration: none;
    /* Layout */
    padding: 16px 32px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    /* Modern gradient background */
    background: linear-gradient(135deg, var(--drop-button-color, #667eea) 0%, color-mix(in srgb, var(--drop-button-color, #667eea) 85%, #764ba2) 100%);
    color: var(--drop-button-text-color, #ffffff);
    /* Advanced shadow system */
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3), 0 4px 12px rgba(102, 126, 234, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Shine effect preparation */
    overflow: hidden;
}

.open-primary-button::before {
    /* Shine effect overlay */
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
}

.open-primary-button:hover::before {
    /* Shine animation on hover */
    left: 100%;
}

.open-primary-button:hover {
    /* Enhanced hover state */
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 6px 16px rgba(102, 126, 234, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.open-primary-button:active {
    /* Active press state */
    transform: translateY(0) scale(0.98);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3), 0 3px 8px rgba(102, 126, 234, 0.2);
}

.open-primary-button .button-content {
    /* Button content styling */
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 8px;
}


/* ===== OPEN REACT TEMPLATE PLATFORM LINKS ===== */

.open-platform-section {
    /* Platform section foundation */
    position: relative;
    padding: 48px 0;
    margin: 32px 0;
}

.open-platform-container {
    /* Platform container layout */
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
}

.open-platform-grid {
    /* Platform grid layout */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
    margin: 24px 0;
}

.open-platform-link {
    /* Platform link styling */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* Typography */
    font-family: var(--font-inter);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    color: var(--color-gray-700);
    /* Layout */
    padding: 12px 20px;
    border-radius: 10px;
    border: 1px solid var(--color-gray-200);
    background: rgba(255, 255, 255, 0.8);
    /* Smooth transitions */
    transition: all 0.3s ease;
}

.open-platform-link:hover {
    /* Enhanced hover state */
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--color-gray-300);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.open-platform-icon {
    /* Platform icon styling */
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.open-platform-link:hover .open-platform-icon {
    opacity: 1;
}


/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .open-hero-section {
        padding: 60px 0 80px;
    }
    .open-hero-title {
        font-size: var(--text-4xl);
        line-height: var(--text-4xl--line-height);
        margin-bottom: 20px;
    }
    .open-hero-description {
        font-size: var(--text-lg);
        line-height: var(--text-lg--line-height);
        margin-bottom: 32px;
    }
    .open-cta-buttons {
        gap: 12px;
    }
    .open-primary-button {
        padding: 14px 28px;
        font-size: var(--text-sm);
    }
    .open-platform-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 12px;
    }
    .open-platform-link {
        padding: 10px 16px;
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .open-hero-container,
    .open-cta-container,
    .open-platform-container {
        padding: 0 16px;
    }
    .open-hero-title {
        font-size: var(--text-3xl);
        line-height: var(--text-3xl--line-height);
    }
    .open-platform-grid {
        grid-template-columns: 1fr 1fr;
    }
}


/* ===== GLASSMORPHISM INTEGRATION ===== */


/* Enhance Open React Template elements with glassmorphism */

.open-platform-link.glass-button {
    /* Override platform link styling for glassmorphism integration */
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.open-platform-link.glass-button:hover {
    /* Enhanced glassmorphism hover state */
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.open-primary-button.glass-button {
    /* Enhance primary button with glassmorphism */
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3), 0 4px 12px rgba(102, 126, 234, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.open-primary-button.glass-button:hover {
    /* Enhanced glassmorphism button hover */
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 6px 16px rgba(102, 126, 234, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.15);
}


/* Form input integration with Open React Template */

.glass-input {
    /* Enhanced form inputs for Open React Template */
    font-family: var(--font-inter) !important;
    font-size: var(--text-base) !important;
    line-height: var(--text-base--line-height) !important;
    letter-spacing: var(--text-base--letter-spacing) !important;
}


/* Success and error messages integration */

.success-message,
.error-message {
    /* Enhanced messages for Open React Template */
    font-family: var(--font-inter);
    font-size: var(--text-sm);
    line-height: var(--text-sm--line-height);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}


/* Cover image integration */

.drop-cover-image img {
    /* Enhanced cover image styling */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drop-cover-image:hover img {
    /* Subtle hover effect for cover image */
    transform: scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}