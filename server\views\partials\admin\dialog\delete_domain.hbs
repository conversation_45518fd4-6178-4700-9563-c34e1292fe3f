<div class="content">
  <h2>Delete domain?</h2>
  <p>
    Are you sure do you want to delete the domain &quot;<b>{{address}}</b>&quot;?<br/>
  </p>
  {{#if hasLink}}
    <div class="delete-domain-checklist">
      <label class="checkbox">
        <input id="delete-domain-links" name="links" type="checkbox" />
        Delete all links too
      </label>
    </div>
  {{/if }}
  <div class="buttons">
    <button type="button" hx-on:click="closeDialog()">Cancel</button>
    <button 
      type="button"
      class="danger confirm" 
      hx-delete="/api/domains/admin/{id}" 
      hx-ext="path-params" 
      hx-vals='{"id":"{{id}}"}' 
      hx-target="closest .content" 
      hx-swap="none" 
      {{#if hasLink}}
        hx-include=".delete-domain-checklist"
      {{/if}}
      hx-indicator="closest .content"
      hx-select-oob="#dialog-error"
    >
      <span>{{> icons/trash}}</span>
      Delete
    </button>
    {{> icons/spinner}}
  </div>
  <div id="dialog-error">
    {{#if error}}
      <p class="error">{{error}}</p>
    {{/if}}
  </div>
</div>