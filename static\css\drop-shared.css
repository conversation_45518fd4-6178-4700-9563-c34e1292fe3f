/*
 * <PERSON><PERSON><PERSON><PERSON> DROP STYLES - MOBILE-FIRST RESPONSIVE DESIGN
 * Beautiful, modern styling with research-based best practices
 * Used by both preview and live drop pages to ensure consistency
 */


/* ===== RESEARCH-BASED ROOT FIXES ===== */

html {
    /* SAFARI FIX: Proper height and overflow handling */
    height: 100%;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    /* MODERN: Better font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    /* RESEARCH-BASED: Proper body setup for mobile */
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    /* SAFARI FIX: Prevent bounce scrolling */
    -webkit-overflow-scrolling: touch;
    /* MODERN: Better box-sizing */
    box-sizing: border-box;
}

*,
*::before,
*::after {
    /* MODERN: Universal box-sizing */
    box-sizing: inherit;
}


/* ===== MODERN MOBILE-FIRST CORE SYSTEM ===== */

.drop-page-wrapper {
    /* RESEARCH-BASED: Avoid 100vh on mobile, use 100% instead */
    min-height: 100%;
    /* BEAUTIFUL: Modern gradient background with CSS variables */
    background: linear-gradient(135deg, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 0%, color-mix(in srgb, var(--preview-drop-background-color, var(--drop-background-color, #f8fafc)) 85%, #ffffff) 100%);
    /* MOBILE-FIRST: Larger padding for better background visibility */
    padding: 20px;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    /* SAFARI FIX: Proper overflow handling */
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    /* RESEARCH-BASED: Ensure full height without viewport issues */
    box-sizing: border-box;
}

.drop-container {
    /* BEAUTIFUL: Modern card design with subtle shadows */
    background: var(--preview-drop-card-color, var(--drop-card-color, #ffffff));
    /* RESEARCH-BASED: Avoid calc() and viewport units on mobile */
    min-height: 500px;
    display: flex;
    flex-direction: column;
    position: relative;
    /* MOBILE-FIRST: Full width within padding */
    width: 100%;
    margin: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 16px;
    /* Modern: Subtle backdrop blur effect */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* SAFARI FIX: Proper overflow and box-sizing */
    overflow: hidden;
    box-sizing: border-box;
}


/* ===== MODERN RESPONSIVE BREAKPOINTS ===== */


/* MOBILE-FIRST: Base styles are mobile (default above) */


/* Mobile styles are already defined in base .drop-container */


/* TABLET: Medium screens (768px and up) */

@media (min-width: 768px) {
    .drop-page-wrapper {
        /* RESEARCH-BASED: Better tablet spacing */
        padding: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
    }
    .drop-container {
        /* RESEARCH-BASED: Better tablet sizing */
        max-width: 600px;
        width: 100%;
        margin: 0;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        min-height: auto;
    }
}


/* DESKTOP: Large screens (1024px and up) */

@media (min-width: 1024px) {
    .drop-page-wrapper {
        /* RESEARCH-BASED: Proper desktop spacing and centering */
        padding: 40px 20px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        /* MODERN: Container max-width for ultra-wide screens */
        max-width: 1400px;
        margin: 0 auto;
    }
    .drop-container {
        /* RESEARCH-BASED: Optimal desktop width */
        max-width: 700px;
        width: 100%;
        margin: 0;
        border-radius: 24px;
        /* BEAUTIFUL: Modern layered shadows */
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 20px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
        /* Modern: Subtle border */
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-height: auto;
    }
}


/* LARGE DESKTOP: Extra large screens (1440px and up) */

@media (min-width: 1440px) {
    .drop-page-wrapper {
        /* RESEARCH-BASED: Better spacing for large screens */
        padding: 80px 60px;
        max-width: 1600px;
    }
    .drop-container {
        /* RESEARCH-BASED: Optimal width for large screens */
        max-width: 800px;
        border-radius: 28px;
    }
}


/* ULTRA-WIDE: Extra large screens (1920px and up) */

@media (min-width: 1920px) {
    .drop-page-wrapper {
        /* RESEARCH-BASED: Maximum container width for ultra-wide */
        max-width: 1920px;
        padding: 100px 80px;
    }
    .drop-container {
        /* RESEARCH-BASED: Maximum content width */
        max-width: 900px;
    }
}


/* ===== MOBILE-FIRST HEADER SYSTEM ===== */

.drop-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    /* MOBILE-FIRST: Proper mobile padding to prevent width issues */
    padding: 12px 16px;
    /* RESEARCH-BASED: Prevent horizontal overflow */
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.drop-brand {
    display: flex;
    align-items: center;
    /* MOBILE: Prevent overflow */
    max-width: 100%;
    overflow: hidden;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
    transition: opacity 0.2s ease;
    /* MOBILE: Prevent text overflow */
    max-width: 100%;
    overflow: hidden;
}

.brand-link:hover {
    opacity: 0.8;
}

.brand-logo {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    /* MOBILE: Prevent shrinking */
    flex-shrink: 0;
}

.brand-text {
    /* MOBILE-FIRST: Smaller font size for mobile */
    font-size: 16px;
    font-weight: 700;
    color: inherit;
    /* MOBILE: Prevent text overflow */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


/* ===== MODERN MOBILE-FIRST MAIN CONTENT SYSTEM ===== */

.drop-main {
    flex: 1;
    /* MODERN: 8px grid system padding */
    padding: 32px 24px;
    text-align: center;
    /* RESEARCH-BASED: Prevent horizontal overflow */
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

.drop-content {
    /* RESEARCH-BASED: Optimal content width for readability */
    max-width: 500px;
    margin: 0 auto;
    /* MOBILE: Ensure content doesn't overflow */
    width: 100%;
    box-sizing: border-box;
}


/* ===== MODERN COVER IMAGE SYSTEM ===== */

.drop-cover-image {
    /* MODERN: 8px grid system spacing */
    margin-bottom: 40px;
    /* STUNNING: Modern border radius with subtle shadow */
    border-radius: 20px;
    overflow: hidden;
    /* MOBILE-FIRST: Larger, more impactful sizing */
    width: 280px;
    height: 280px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    /* BEAUTIFUL: Modern layered shadows for depth */
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.05);
    /* MODERN: Subtle backdrop blur effect */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* SMOOTH: Beautiful transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drop-cover-image:hover {
    /* INTERACTIVE: Subtle hover effect */
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.drop-cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    /* MODERN: Better image rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}


/* ===== MODERN TYPOGRAPHY SYSTEM ===== */

.drop-title {
    /* MODERN: 8px grid system spacing */
    margin: 0 0 24px 0;
    /* MOBILE-FIRST: Larger, more impactful typography */
    font-size: 32px;
    font-weight: 900;
    /* RESEARCH-BASED: Optimal line height for readability */
    line-height: 1.1;
    letter-spacing: -0.02em;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* STUNNING: Enhanced gradient text effect */
    background: linear-gradient(135deg, var(--preview-drop-title-color, var(--drop-title-color, #1f2937)) 0%, color-mix(in srgb, var(--preview-drop-title-color, var(--drop-title-color, #1f2937)) 85%, #6366f1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* Fallback for browsers that don't support background-clip: text */
    color: var(--preview-drop-title-color, var(--drop-title-color, #1f2937));
    /* BEAUTIFUL: Subtle text shadow for depth */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    /* MODERN: Better word spacing */
    word-spacing: 0.1em;
}

.drop-description p {
    /* MODERN: 8px grid system spacing */
    margin: 0 0 32px 0;
    /* RESEARCH-BASED: Larger, more readable typography */
    font-size: 18px;
    /* OPTIMAL: Perfect line height for readability */
    line-height: 1.7;
    font-weight: 450;
    /* FIXED: Proper CSS variable priority for preview synchronization */
    color: var(--drop-description-color, var(--preview-drop-description-color, #6b7280)) !important;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* RESEARCH-BASED: Optimal character spacing */
    letter-spacing: 0.01em;
    /* MODERN: Better word spacing for readability */
    word-spacing: 0.05em;
    /* BEAUTIFUL: Subtle text shadow for depth */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    /* RESPONSIVE: Optimal max-width for readability */
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.drop-stats {
    /* MODERN: 8px grid system spacing */
    margin-bottom: 24px;
}

.signup-count {
    display: inline-block;
    /* MODERN: 8px grid system padding */
    padding: 12px 20px;
    /* BEAUTIFUL: Modern glass morphism effect */
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    /* RESEARCH-BASED: Optimal font sizing */
    font-size: 15px;
    font-weight: 600;
    color: inherit;
    /* MODERN: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    /* BEAUTIFUL: Subtle shadow for depth */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    /* SMOOTH: Beautiful transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.signup-count:hover {
    /* INTERACTIVE: Subtle hover effect */
    transform: translateY(-1px);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}


/* ===== FORM SYSTEM ===== */

.drop-signup-section {
    max-width: 400px;
    margin: 0 auto;
}

.signup-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    margin: 0;
}


/* 🎯 RESEARCH-BASED UNIFIED FORM INPUT SYSTEM */

.form-input {
    /* LAYOUT: Full width with proper box-sizing */
    width: 100%;
    box-sizing: border-box;
    /* SPACING: Research-based padding for optimal touch targets */
    padding: 16px 20px;
    height: 56px;
    /* TYPOGRAPHY: 16px prevents iOS zoom, optimal readability */
    font-size: 16px;
    font-weight: 400;
    line-height: 1.4;
    color: #374151;
    /* STYLING: Modern, accessible design */
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: var(--drop-form-field-color, var(--preview-drop-form-field-color, #ffffff));
    /* INTERACTION: Smooth transitions */
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    /* ACCESSIBILITY: Remove browser styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* VISUAL: Subtle depth */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    /* MOBILE: Prevent zoom and improve UX */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}


/* RESEARCH-BASED FOCUS STATES */

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-input:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


/* UNIFIED INPUT TYPE CONSISTENCY */

input[type="email"].form-input,
input[type="tel"].form-input,
input[type="text"].form-input {
    /* INHERIT: All properties from .form-input */
    /* This ensures 100% consistency across all input types */
}


/* PREVIEW INPUT CONSISTENCY */

.preview-input {
    /* INHERIT: All properties from .form-input */
    /* Additional override for background color */
    background: var(--drop-form-field-color, var(--preview-drop-form-field-color, #ffffff)) !important;
}


/* ===== BUTTON SYSTEM ===== */

.signup-button {
    width: 100%;
    /* MOBILE-FIRST: Start with mobile button sizing */
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    /* BEAUTIFUL: Modern smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* STUNNING: Modern gradient button with CSS variables */
    background: linear-gradient(135deg, var(--preview-drop-button-color, var(--drop-button-color, #3b82f6)) 0%, color-mix(in srgb, var(--preview-drop-button-color, var(--drop-button-color, #3b82f6)) 85%, #1d4ed8) 100%) !important;
    color: var(--preview-drop-button-text-color, var(--drop-button-text-color, #ffffff)) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
    /* MODERN: Beautiful shadows */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
    /* Modern: Subtle border */
    border: 1px solid rgba(255, 255, 255, 0.1);
    /* Modern: Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    position: relative;
    overflow: hidden;
}

.signup-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.signup-button:hover::before {
    left: 100%;
}

.signup-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
}

.signup-button:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

.signup-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.signup-button:disabled::before {
    display: none;
}


/* ===== FOOTER SYSTEM ===== */

.drop-footer {
    padding: 24px;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: transparent;
}

.drop-footer p {
    margin: 0;
    font-size: 14px;
    color: inherit;
    opacity: 0.6;
}

.footer-link {
    color: inherit;
    font-weight: 500;
    text-decoration: none;
}

.footer-link:hover {
    opacity: 0.8;
}


/* ===== MODERN RESPONSIVE TYPOGRAPHY ===== */


/* TABLET: Enhanced typography (768px and up) */

@media (min-width: 768px) {
    .drop-title {
        /* RESEARCH-BASED: Larger, more impactful tablet typography */
        font-size: 40px;
        margin-bottom: 32px;
        letter-spacing: -0.025em;
    }
    .drop-description p {
        /* OPTIMAL: Better tablet readability */
        font-size: 20px;
        margin-bottom: 40px;
        line-height: 1.75;
    }
    .signup-button {
        padding: 18px 32px;
        font-size: 17px;
        border-radius: 14px;
    }
    .drop-cover-image {
        /* LARGER: More impactful tablet sizing */
        width: 320px;
        height: 320px;
    }
    .drop-main {
        /* MODERN: Enhanced tablet spacing */
        padding: 48px 32px;
    }
}


/* DESKTOP: Premium typography (1024px and up) */

@media (min-width: 1024px) {
    .drop-title {
        /* STUNNING: Large, impactful desktop typography */
        font-size: 48px;
        margin-bottom: 40px;
        letter-spacing: -0.03em;
        line-height: 1.05;
    }
    .drop-description p {
        /* OPTIMAL: Premium desktop readability */
        font-size: 22px;
        margin-bottom: 48px;
        line-height: 1.8;
        max-width: 650px;
        margin-left: auto;
        margin-right: auto;
    }
    .signup-button {
        padding: 20px 40px;
        font-size: 18px;
        border-radius: 16px;
        font-weight: 700;
    }
    .drop-cover-image {
        /* LARGE: Impressive desktop sizing */
        width: 360px;
        height: 360px;
    }
    .drop-main {
        /* PREMIUM: Luxurious desktop spacing */
        padding: 64px 40px;
    }
}


/* LARGE DESKTOP: Luxurious typography (1440px and up) */

@media (min-width: 1440px) {
    .drop-title {
        /* LUXURIOUS: Maximum impact typography */
        font-size: 56px;
        margin-bottom: 48px;
    }
    .drop-description p {
        /* PREMIUM: Ultimate readability */
        font-size: 24px;
        margin-bottom: 56px;
        max-width: 700px;
    }
    .drop-cover-image {
        /* MASSIVE: Show-stopping image size */
        width: 400px;
        height: 400px;
    }
    .drop-main {
        /* LUXURIOUS: Maximum spacing */
        padding: 80px 48px;
    }
}


/* ===== RESEARCH-BASED MOBILE FIXES ===== */


/* MOBILE: Prevent zoom on input focus (iOS Safari) */

@media screen and (max-width: 767px) {
    .form-input {
        font-size: 16px !important;
        /* CRITICAL: Prevents iOS zoom */
        -webkit-text-size-adjust: 100%;
        -webkit-appearance: none;
    }
    /* MOBILE: Tighter spacing for mobile */
    .drop-header {
        padding: 12px 16px;
    }
    .drop-main {
        padding: 20px 16px;
    }
    .drop-footer {
        padding: 16px;
    }
    /* MOBILE: Smaller brand text */
    .brand-text {
        font-size: 16px;
    }
    /* MOBILE: Prevent horizontal scroll */
    body,
    html {
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
    }
    .drop-page-wrapper {
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
        /* MOBILE FIX: Reduce padding to minimize white space at bottom */
        padding: 10px !important;
    }
    .drop-container {
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
        /* MOBILE FIX: Ensure container fills available space properly */
        min-height: calc(100vh - 20px);
    }
}


/* TABLET AND UP: Enhanced spacing */

@media screen and (min-width: 768px) {
    .drop-header {
        padding: 16px 24px;
    }
    .drop-main {
        padding: 40px 24px;
    }
    .drop-footer {
        padding: 24px;
    }
    .brand-text {
        font-size: 18px;
    }
}


/* ===== UTILITY CLASSES ===== */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}