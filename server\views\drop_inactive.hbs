<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{pageTitle}} - BOUNCE2BOUNCE</title>
    <meta name="description" content="This drop is currently inactive">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            line-height: 1.6;
        }
        
        .inactive-container {
            text-align: center;
            max-width: 500px;
            padding: 3rem 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .inactive-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .inactive-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            letter-spacing: -0.025em;
        }
        
        .inactive-message {
            font-size: 1.125rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .drop-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 2rem;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .inactive-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .home-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .home-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .contact-info {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .brand-footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .brand-logo {
            width: 20px;
            height: 20px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .inactive-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            .inactive-title {
                font-size: 1.5rem;
            }
            
            .inactive-message {
                font-size: 1rem;
            }
            
            .drop-title {
                font-size: 1.125rem;
                padding: 0.875rem 1.25rem;
            }
            
            .home-button {
                padding: 0.875rem 1.5rem;
                width: 100%;
                justify-content: center;
            }
        }
        
        @media (max-width: 480px) {
            .inactive-container {
                padding: 1.5rem 1rem;
            }
            
            .inactive-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }
            
            .inactive-title {
                font-size: 1.25rem;
            }
            
            .inactive-message {
                font-size: 0.9rem;
            }
        }
        
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .inactive-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* Pulse animation for icon */
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }
        
        .inactive-icon {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="inactive-container">
        <div class="inactive-icon">
            ⏸️
        </div>
        
        <h1 class="inactive-title">Drop Inactive</h1>
        
        <p class="inactive-message">
            This drop is currently inactive and not accepting new signups.
        </p>
        
        {{#if dropTitle}}
        <div class="drop-title">
            "{{dropTitle}}"
        </div>
        {{/if}}
        
        <div class="inactive-actions">
            <a href="/" class="home-button">
                <span>🏠</span>
                Go to Homepage
            </a>
        </div>
        
        <div class="contact-info">
            <p>If you believe this is an error, please contact the drop owner.</p>
        </div>
        
        <div class="brand-footer">
            <img src="/images/favicon-32x32.png" alt="BOUNCE2BOUNCE" class="brand-logo">
            <span>Powered by BOUNCE2BOUNCE</span>
        </div>
    </div>
</body>
</html>
