<section id="change-password-wrapper">
  <h2>
    Change password
  </h2>
  <p>Enter your current password and a new password to change it to.</p>
  <form 
    id="change-password"
    hx-post="/api/auth/change-password"
    hx-select="form"
    hx-swap="outerHTML"
    hx-sync="this:abort"
  >
    <div class="inputs">
      <label class="{{#if errors.currentpassword}}error{{/if}}">
        Current password:
        <input 
          id="currentpassword" 
          name="currentpassword" 
          type="password" 
          placeholder="Current password..."
          hx-preserve="true"
        />
        {{#if errors.currentpassword}}<p class="error">{{errors.currentpassword}}</p>{{/if}}
      </label>
      <label class="{{#if errors.newpassword}}error{{/if}}">
        New password:
        <input 
          id="newpassword" 
          name="newpassword" 
          type="password" 
          placeholder="New password..."
          hx-preserve="true"
        />
        {{#if errors.newpassword}}<p class="error">{{errors.newpassword}}</p>{{/if}}
      </label>
    </div>
    <button type="submit" class="primary">
      <span>{{> icons/reload}}</span>
      <span>{{> icons/spinner}}</span>
      Update
    </button>
    {{#if error}}
      {{#unless errors}}
        <p class="error">{{error}}</p>
      {{/unless}}
    {{else if success}}
      <p class="success">{{success}}</p>
    {{/if}}
  </form>
</section>