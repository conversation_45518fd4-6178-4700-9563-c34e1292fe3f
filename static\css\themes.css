/**
 * 🎨 MODERN THEME SYSTEM
 * 
 * Research-based implementation following:
 * - Material Design 3 color system
 * - Apple Human Interface Guidelines
 * - Modern CSS custom properties best practices
 * - Accessibility standards (WCAG 2.1)
 */

/* ===== ROOT THEME VARIABLES ===== */
:root {
    /* Light Mode Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-elevated: #ffffff;
    --bg-overlay: rgba(255, 255, 255, 0.95);
    
    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-focus: #3b82f6;
    
    /* Brand Colors */
    --brand-primary: #3b82f6;
    --brand-secondary: #1e40af;
    --brand-accent: #06b6d4;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #06b6d4;
    
    /* Navigation Colors */
    --nav-bg: #ffffff;
    --nav-border: #e2e8f0;
    --nav-item-hover: #f1f5f9;
    --nav-item-active: #eff6ff;
    --nav-text: #374151;
    --nav-text-active: #1d4ed8;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Blur Effects */
    --blur-sm: blur(4px);
    --blur-md: blur(8px);
    --blur-lg: blur(16px);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Navigation Dimensions */
    --nav-width: 280px;
    --nav-mobile-height: 80px;
    --nav-mobile-expanded: 320px;
}

/* ===== DARK MODE THEME ===== */
[data-theme="dark"] {
    /* Dark Mode Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-elevated: #1e293b;
    --bg-overlay: rgba(15, 23, 42, 0.95);
    
    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #0f172a;
    
    /* Border Colors */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-focus: #60a5fa;
    
    /* Brand Colors (adjusted for dark mode) */
    --brand-primary: #60a5fa;
    --brand-secondary: #3b82f6;
    --brand-accent: #22d3ee;
    
    /* Status Colors (adjusted for dark mode) */
    --success: #34d399;
    --warning: #fbbf24;
    --error: #f87171;
    --info: #22d3ee;
    
    /* Navigation Colors */
    --nav-bg: #1e293b;
    --nav-border: #334155;
    --nav-item-hover: #334155;
    --nav-item-active: #1e40af;
    --nav-text: #cbd5e1;
    --nav-text-active: #60a5fa;
    
    /* Shadows (adjusted for dark mode) */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* ===== SYSTEM PREFERENCE DETECTION ===== */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        /* Auto dark mode colors */
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --bg-elevated: #1e293b;
        --bg-overlay: rgba(15, 23, 42, 0.95);
        
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-tertiary: #94a3b8;
        --text-inverse: #0f172a;
        
        --border-primary: #334155;
        --border-secondary: #475569;
        --border-focus: #60a5fa;
        
        --brand-primary: #60a5fa;
        --brand-secondary: #3b82f6;
        --brand-accent: #22d3ee;
        
        --nav-bg: #1e293b;
        --nav-border: #334155;
        --nav-item-hover: #334155;
        --nav-item-active: #1e40af;
        --nav-text: #cbd5e1;
        --nav-text-active: #60a5fa;
        
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    }
}

/* ===== THEME TRANSITION ===== */
* {
    transition: 
        background-color var(--transition-normal),
        border-color var(--transition-normal),
        color var(--transition-normal),
        box-shadow var(--transition-normal);
}

/* ===== GLOBAL THEME APPLICATION ===== */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== UTILITY CLASSES ===== */
.theme-bg-primary { background-color: var(--bg-primary); }
.theme-bg-secondary { background-color: var(--bg-secondary); }
.theme-bg-tertiary { background-color: var(--bg-tertiary); }
.theme-bg-elevated { background-color: var(--bg-elevated); }

.theme-text-primary { color: var(--text-primary); }
.theme-text-secondary { color: var(--text-secondary); }
.theme-text-tertiary { color: var(--text-tertiary); }

.theme-border-primary { border-color: var(--border-primary); }
.theme-border-secondary { border-color: var(--border-secondary); }

.theme-shadow-sm { box-shadow: var(--shadow-sm); }
.theme-shadow-md { box-shadow: var(--shadow-md); }
.theme-shadow-lg { box-shadow: var(--shadow-lg); }
.theme-shadow-xl { box-shadow: var(--shadow-xl); }
