{{!-- Laylo-Style Dashboard --}}

<div class="laylo-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <h1 class="greeting">Good evening, BOUNCE2BOUNCE</h1>

        <!-- Shortcuts -->
        <div class="shortcuts">
            <span class="shortcuts-label">Shortcuts</span>
            <a href="/messages" class="shortcut-btn">
                <span class="shortcut-icon">💬</span>
                Send a message
            </a>
            <a href="/campaigns" class="shortcut-btn">
                <span class="shortcut-icon">📸</span>
                Run an Instagram Campaign
            </a>
            <a href="/drops/create" class="shortcut-btn">
                <span class="shortcut-icon">🎵</span>
                Create a multidrop
            </a>
            <a href="/merch" class="shortcut-btn">
                <span class="shortcut-icon">👕</span>
                Set up a merch Drop
            </a>
        </div>
    </div>

    <!-- Email Domain Setup Card -->
    <div class="setup-card">
        <div class="setup-icon">
            <div class="icon-gradient"></div>
        </div>
        <div class="setup-content">
            <h3>Finish setting up your email domain</h3>
            <p>Add DMARC records to ensure better deliverability and build more trust with your fans.</p>
            <a href="/settings/email" class="setup-link">DMARC setup guide</a>
        </div>
    </div>

    <!-- Dropping Soon Section -->
    <div class="dropping-soon">
        <h2>Dropping soon</h2>
        <p class="section-subtitle">Announce something new, or see what you have scheduled to drop</p>

        {{#if upcomingDrops.length}}
            <div class="drops-grid">
                {{#each upcomingDrops}}
                    <div class="drop-card">
                        {{#if image}}
                            <div class="drop-image" style="background-image: url('{{image}}')"></div>
                        {{else}}
                            <div class="drop-image placeholder"></div>
                        {{/if}}
                        <div class="drop-info">
                            <h4>{{title}}</h4>
                            <p class="drop-date">{{formatDate scheduledDate "MMM d, yyyy"}}</p>
                        </div>
                    </div>
                {{/each}}
            </div>
        {{else}}
            <div class="empty-drops">
                <a href="/drops/create" class="create-drop-btn">
                    <span class="plus-icon">+</span>
                    Create a Drop
                </a>
            </div>
        {{/if}}
    </div>

    <!-- Your Drops -->
    <div class="drops-section">
        <h2>Your Drops</h2>
        {{> drops/section}}
    </div>
</div>
