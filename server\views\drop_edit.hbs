{{> header}}

<link rel="stylesheet" href="/css/drop-edit.css">
<link rel="stylesheet" href="/css/analytics.css">

<div class="drop-edit-container">
    <div class="drop-edit-header">
        <div class="header-content">
            <button type="button" class="back-button" onclick="window.history.back()">
                {{> icons/arrow_left}}
                <span>Back</span>
            </button>
            <h1>Edit Drop</h1>
        </div>
    </div>

    <div class="drop-edit-content">
        <div class="drop-edit-form-container">
            <!-- MODERN TAB NAVIGATION -->
            <div class="tab-navigation">
                <div class="tab-nav-container">
                    <button type="button" class="tab-btn active" data-tab="page">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <span>Page</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="messaging">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H6L4,18V4H20V16Z"/>
                        </svg>
                        <span>Messaging</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="advanced">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                        </svg>
                        <span>Advanced</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="analytics">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M22,21H2V3H4V19H6V17H10V19H12V16H16V19H18V17H22V21M16,8H18V15H16V8M12,2H14V15H12V2M8,13H10V15H8V13M4,8H6V15H4V8Z"/>
                        </svg>
                        <span>Analytics</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="sharing">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z"/>
                        </svg>
                        <span>Sharing</span>
                    </button>
                </div>
            </div>

            <form id="drop-edit-form" class="drop-form">
                <!-- PAGE TAB CONTENT -->
                <div class="tab-content active" data-tab-content="page">
                    <!-- Details Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Details</h2>
                            <p class="section-description">Configure the basic information for your drop page</p>
                        </div>

                        <div class="form-group">
                            <label for="edit-title">Title *</label>
                            <input type="text" id="edit-title" name="title" required maxlength="255"
                                   value="{{drop.title}}" placeholder="Enter drop title">
                        </div>

                        <div class="form-group">
                            <label for="edit-description">Description</label>
                            <textarea id="edit-description" name="description" rows="4"
                                      placeholder="Describe what this drop is about">{{drop.description}}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit-slug">URL Slug</label>
                            <div class="slug-input-wrapper">
                                <span class="slug-prefix">{{domain}}/drop/</span>
                                <input type="text" id="edit-slug" name="slug" maxlength="100"
                                       value="{{drop.slug}}" placeholder="my-drop">
                            </div>
                            <small class="form-help">This will be your drop's public URL</small>
                        </div>

                        <div class="form-group">
                            <label for="edit-button-text">Button Text</label>
                            <input type="text" id="edit-button-text" name="button_text" maxlength="50"
                                   value="{{drop.button_text}}" placeholder="Get Notified">
                        </div>

                        <div class="form-group">
                            <label for="edit-cover-image">Cover Image URL</label>
                            <input type="url" id="edit-cover-image" name="cover_image"
                                   value="{{drop.cover_image}}" placeholder="https://example.com/image.jpg">
                            <small class="form-help">Optional: Add a cover image for your drop</small>
                        </div>
                    </div>

                    <!-- Styling Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Styling</h2>
                            <p class="section-description">Customize the visual appearance of your drop page</p>
                        </div>

                        <!-- Background Settings -->
                        <div class="styling-section">
                            <h3 class="subsection-title">Background</h3>
                            <div class="form-group">
                                <label for="edit-background-type" class="form-label">Background Type</label>
                                <select id="edit-background-type" name="background_type" class="form-select">
                                    <option value="gradient" {{#if (eq drop.background_type "gradient")}}selected{{/if}}>Gradient</option>
                                    <option value="solid" {{#if (eq drop.background_type "solid")}}selected{{/if}}>Solid Color</option>
                                </select>
                            </div>
                            <div class="color-picker-group">
                                <label for="edit-background-color" class="color-picker-label">
                                    Background Color
                                </label>
                                <input type="color" id="edit-background-color" name="background_color"
                                       value="{{drop.background_color}}" class="modern-color-input">
                            </div>
                        </div>

                        <!-- Card Settings -->
                        <div class="styling-section">
                            <h3 class="subsection-title">Card Background</h3>
                            <div class="form-group">
                                <label for="edit-card-background-type" class="form-label">Card Style</label>
                                <select id="edit-card-background-type" name="card_background_type" class="form-select">
                                    <option value="solid_white" {{#if (eq drop.card_background_type "solid_white")}}selected{{/if}}>Solid White</option>
                                    <option value="solid_dark" {{#if (eq drop.card_background_type "solid_dark")}}selected{{/if}}>Solid Dark</option>
                                    <option value="translucent_light" {{#if (eq drop.card_background_type "translucent_light")}}selected{{/if}}>Translucent Light</option>
                                    <option value="translucent_dark" {{#if (eq drop.card_background_type "translucent_dark")}}selected{{/if}}>Translucent Dark</option>
                                </select>
                            </div>
                        </div>

                        <!-- Text Colors -->
                        <div class="styling-section">
                            <h3 class="subsection-title">Text Colors</h3>
                            <div class="color-picker-grid">
                                <div class="color-picker-group">
                                    <label for="edit-title-color" class="color-picker-label">
                                        Title Color
                                    </label>
                                    <input type="color" id="edit-title-color" name="title_color"
                                           value="{{drop.title_color}}" class="modern-color-input">
                                </div>

                                <div class="color-picker-group">
                                    <label for="edit-description-color" class="color-picker-label">
                                        Subtitle Color
                                    </label>
                                    <input type="color" id="edit-description-color" name="description_color"
                                           value="{{drop.description_color}}" class="modern-color-input">
                                </div>
                            </div>
                        </div>

                        <!-- Button Settings -->
                        <div class="styling-section">
                            <h3 class="subsection-title">Button Styling</h3>
                            <div class="color-picker-grid">
                                <div class="color-picker-group">
                                    <label for="edit-button-color" class="color-picker-label">
                                        Button Background
                                    </label>
                                    <input type="color" id="edit-button-color" name="button_color"
                                           value="{{drop.button_color}}" class="modern-color-input">
                                </div>

                                <div class="color-picker-group">
                                    <label for="edit-button-text-color" class="color-picker-label">
                                        Button Text Color
                                    </label>
                                    <input type="color" id="edit-button-text-color" name="button_text_color"
                                           value="{{drop.button_text_color}}" class="modern-color-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Platform Links Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Platform Links</h2>
                            <p class="section-description">Add links to your other platforms and social media</p>
                        </div>

                        <div class="platform-links-container">
                            <div class="form-group">
                                <label for="edit-website-link">Website</label>
                                <input type="url" id="edit-website-link" name="website_link"
                                       placeholder="https://yourwebsite.com">
                            </div>

                            <div class="form-group">
                                <label for="edit-instagram-link">Instagram</label>
                                <input type="url" id="edit-instagram-link" name="instagram_link"
                                       placeholder="https://instagram.com/yourusername">
                            </div>

                            <div class="form-group">
                                <label for="edit-twitter-link">Twitter/X</label>
                                <input type="url" id="edit-twitter-link" name="twitter_link"
                                       placeholder="https://twitter.com/yourusername">
                            </div>

                            <div class="form-group">
                                <label for="edit-youtube-link">YouTube</label>
                                <input type="url" id="edit-youtube-link" name="youtube_link"
                                       placeholder="https://youtube.com/@yourusername">
                            </div>

                            <div class="form-group">
                                <label for="edit-spotify-link">Spotify</label>
                                <input type="url" id="edit-spotify-link" name="spotify_link"
                                       placeholder="https://open.spotify.com/artist/...">
                            </div>

                            <div class="form-group">
                                <label for="edit-tiktok-link">TikTok</label>
                                <input type="url" id="edit-tiktok-link" name="tiktok_link"
                                       placeholder="https://tiktok.com/@yourusername">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- MESSAGING TAB CONTENT -->
                <div class="tab-content" data-tab-content="messaging">
                    <div class="tab-placeholder">
                        <div class="placeholder-content">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" class="placeholder-icon">
                                <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M20,16H6L4,18V4H20V16Z"/>
                            </svg>
                            <h3>Messaging</h3>
                            <p>Configure email templates, SMS messages, and notification settings.</p>
                            <span class="coming-soon-badge">Coming Soon</span>
                        </div>
                    </div>
                </div>

                <!-- ADVANCED TAB CONTENT -->
                <div class="tab-content" data-tab-content="advanced">
                    <!-- Visibility & Access Control -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Visibility & Access Control</h2>
                            <p class="section-description">Control who can access your drop and when it's live</p>
                        </div>

                        <div class="form-group">
                            <div class="modern-setting-group">
                                <div class="setting-header">
                                    <div class="setting-info">
                                        <h4 class="setting-title">Active Drop</h4>
                                        <p class="setting-description">When active, your drop is publicly accessible and accepts signups. When inactive, the drop URL will show a "Drop Inactive" page and block all signups.</p>
                                    </div>
                                    <div class="setting-control-column">
                                        <div class="world-class-status-indicator {{#if drop.is_active}}live{{else}}inactive{{/if}}" id="drop-status-indicator">
                                            <div class="status-pulse-dot"></div>
                                            <span class="status-text">{{#if drop.is_active}}Live{{else}}Inactive{{/if}}</span>
                                        </div>
                                        <label class="modern-toggle">
                                            <input type="checkbox" name="is_active" {{#if drop.is_active}}checked{{/if}} id="active-drop-toggle">
                                            <span class="toggle-slider">
                                                <span class="toggle-thumb"></span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Collection -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Data Collection</h2>
                            <p class="section-description">Configure what information to collect from users</p>
                        </div>

                        <div class="form-group">
                            <div class="modern-setting-group">
                                <div class="setting-header">
                                    <div class="setting-info">
                                        <h4 class="setting-title">Collect Email Addresses</h4>
                                        <p class="setting-description">Required for sending notifications when the drop goes live</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="modern-toggle">
                                            <input type="checkbox" name="collect_email" {{#if drop.collect_email}}checked{{/if}}>
                                            <span class="toggle-slider">
                                                <span class="toggle-thumb"></span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="modern-setting-group">
                                <div class="setting-header">
                                    <div class="setting-info">
                                        <h4 class="setting-title">Collect Phone Numbers</h4>
                                        <p class="setting-description">Optional: Collect phone numbers for SMS notifications</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="modern-toggle">
                                            <input type="checkbox" name="collect_phone" {{#if drop.collect_phone}}checked{{/if}}>
                                            <span class="toggle-slider">
                                                <span class="toggle-thumb"></span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Messages -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Messages</h2>
                            <p class="section-description">Customize messages shown to users</p>
                        </div>

                        <div class="form-group">
                            <label for="edit-thank-you-message">Thank You Message</label>
                            <textarea id="edit-thank-you-message" name="thank_you_message" rows="3"
                                      placeholder="Thank you for signing up! You'll be notified when this drop goes live.">{{drop.thank_you_message}}</textarea>
                            <small class="form-help">Message shown after successful signup</small>
                        </div>
                    </div>
                </div>

                <!-- ANALYTICS TAB CONTENT -->
                <div class="tab-content" data-tab-content="analytics">
                    <!-- 🚀 LAYLO-STYLE ANALYTICS DASHBOARD -->
                    <div class="analytics-dashboard">
                        <!-- Summary Stats Cards -->
                        <div class="analytics-summary-grid">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Total Fans</h3>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3.72 5.6-2.28-2.28A1 1 0 0 0 7.29 12L4 15.29V20h2v-3.59l2.71-2.7L11 16l4-6h2.54l2.46 7.4V22h4z"/>
                                    </svg>
                                </div>
                                <div class="card-value" id="total-fans">-</div>
                                <div class="card-change positive" id="fans-change">-</div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Total RSVPs</h3>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                    </svg>
                                </div>
                                <div class="card-value" id="total-rsvps">-</div>
                                <div class="card-change positive" id="rsvps-change">-</div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Repeat Fans</h3>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <div class="card-value" id="repeat-fans">-</div>
                                <div class="card-change positive" id="repeat-change">-</div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Avg RSVPs/Fan</h3>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                                    </svg>
                                </div>
                                <div class="card-value" id="avg-rsvps">-</div>
                                <div class="card-change neutral" id="avg-change">-</div>
                            </div>
                        </div>

                        <!-- Fans Table Section -->
                        <div class="fans-section">
                            <div class="fans-header">
                                <div class="fans-title">
                                    <h2>Fans</h2>
                                    <p>All fans who have RSVP'd to your drops</p>
                                </div>
                                <div class="fans-controls">
                                    <div class="search-container">
                                        <input type="text" id="fans-search" placeholder="Search fans..." class="search-input">
                                        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                        </svg>
                                    </div>
                                    <select id="fans-sort" class="sort-select">
                                        <option value="latest">Latest First</option>
                                        <option value="oldest">Oldest First</option>
                                        <option value="most_active">Most Active</option>
                                        <option value="name">Name A-Z</option>
                                        <option value="email">Email A-Z</option>
                                    </select>
                                    <button type="button" class="export-btn" onclick="exportFans()">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                        </svg>
                                        Export
                                    </button>
                                </div>
                            </div>

                            <!-- Fans Table -->
                            <div class="fans-table-container">
                                <div class="table-loading" id="fans-loading">
                                    <div class="loading-spinner"></div>
                                    <p>Loading fans...</p>
                                </div>

                                <!-- Desktop Table View -->
                                <div class="table-desktop">
                                    <table class="fans-table" id="fans-table" style="display: none;">
                                        <thead>
                                            <tr>
                                                <th>Contact</th>
                                                <th>Location</th>
                                                <th>Joined On</th>
                                                <th>Acq. Channel</th>
                                                <th>RSVPs</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fans-table-body">
                                            <!-- Dynamic content loaded here -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Mobile Card View -->
                                <div class="table-mobile">
                                    <div class="fans-cards" id="fans-cards" style="display: none;">
                                        <!-- Dynamic cards loaded here -->
                                    </div>
                                </div>

                                <div class="table-pagination" id="fans-pagination" style="display: none;">
                                    <div class="pagination-info">
                                        <span id="pagination-text">Showing 1-50 of 0 fans</span>
                                    </div>
                                    <div class="pagination-controls">
                                        <button type="button" id="prev-page" class="pagination-btn" disabled>
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                                            </svg>
                                            Previous
                                        </button>
                                        <span id="page-numbers" class="page-numbers"></span>
                                        <button type="button" id="next-page" class="pagination-btn">
                                            Next
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <div class="empty-state" id="fans-empty" style="display: none;">
                                    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor" class="empty-icon">
                                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3.72 5.6-2.28-2.28A1 1 0 0 0 7.29 12L4 15.29V20h2v-3.59l2.71-2.7L11 16l4-6h2.54l2.46 7.4V22h4z"/>
                                    </svg>
                                    <h3>No fans yet</h3>
                                    <p>Share your drop page to start collecting fans!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SHARING TAB CONTENT -->
                <div class="tab-content" data-tab-content="sharing">
                    <div class="tab-placeholder">
                        <div class="placeholder-content">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" class="placeholder-icon">
                                <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z"/>
                            </svg>
                            <h3>Sharing</h3>
                            <p>Configure social sharing, embed codes, and promotional tools.</p>
                            <span class="coming-soon-badge">Coming Soon</span>
                        </div>
                    </div>
                </div>

                <!-- Smart Actions - Always visible with smart lighting -->
                <div class="form-actions" id="form-actions">
                    <button type="button" class="button secondary" id="cancel-btn">
                        Cancel Changes
                    </button>
                    <button type="submit" class="button primary" id="save-btn">
                        <span class="button-text">Save Changes</span>
                        <span class="button-spinner" style="display: none;">
                            {{> icons/spinner}}
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Enhanced Preview Panel -->
        <div class="drop-preview-panel">
            <!-- Mobile Toggle Button (hidden on desktop) -->
            <button type="button" class="preview-toggle" onclick="togglePreview()">
                <span>Live Preview</span>
                <div class="preview-toggle-actions">
                    <a href="/drop/{{drop.slug}}" target="_blank" class="preview-quick-link" title="Open in new tab">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
                        </svg>
                    </a>
                    <svg class="preview-toggle-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5z"/>
                    </svg>
                </div>
            </button>

            <div class="preview-collapsible">
                <div class="preview-header">
                    <div class="preview-title-section">
                        <h3>Mobile Preview</h3>
                        <span class="preview-device-indicator" id="device-indicator">Mobile View</span>
                    </div>
                </div>

            <div class="preview-container">
                <div class="preview-viewport mobile" id="preview-viewport">
                    <div class="drop-page-wrapper">
                        <div class="drop-container" id="drop-preview">
                        <!-- Header -->
                        <header class="drop-header">
                            <div class="drop-brand">
                                <a href="/" class="brand-link">
                                    <img src="/images/logo.png" alt="BOUNCE2BOUNCE" class="brand-logo">
                                    <span class="brand-text" id="preview-brand-text">BOUNCE2BOUNCE</span>
                                </a>
                            </div>
                        </header>

                        <!-- Main Content -->
                        <main class="drop-main">
                            <div class="drop-content">
                                <div class="drop-cover-image" id="preview-cover" style="display: none;">
                                    <img id="preview-cover-image" src="" alt="Cover" loading="lazy">
                                </div>

                                <div class="drop-info">
                                    <h1 class="drop-title" id="preview-title">{{drop.title}}</h1>

                                    <div class="drop-description">
                                        <p id="preview-description">{{drop.description}}</p>
                                    </div>

                                    <div class="drop-stats">
                                        <span class="signup-count" id="preview-signup-count">42 people signed up</span>
                                    </div>
                                </div>

                                <!-- Signup Form -->
                                <div class="drop-signup-section">
                                    <form class="signup-form">
                                        <div class="form-group" id="preview-email-field" {{#unless drop.collect_email}}style="display: none;"{{/unless}}>
                                            <input type="email" placeholder="Enter your email" readonly class="form-input preview-input">
                                        </div>
                                        <div class="form-group" id="preview-phone-field" {{#unless drop.collect_phone}}style="display: none;"{{/unless}}>
                                            <input type="tel" placeholder="Enter your phone number" readonly class="form-input preview-input">
                                        </div>
                                        <div class="form-group" id="preview-name-field">
                                            <input type="text" placeholder="Your name (optional)" readonly class="form-input preview-input">
                                        </div>
                                        <button type="button" class="signup-button" id="preview-button">
                                            <span class="button-text" id="preview-button-text">{{drop.button_text}}</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </main>

                        <!-- Footer -->
                        <footer class="drop-footer">
                            <p>Powered by <a href="/" class="footer-link">BOUNCE2BOUNCE</a></p>
                        </footer>
                        </div>
                    </div>
                </div>
            </div>

                <div class="preview-info-panel">
                    <div class="preview-url">
                        <label>Preview URL:</label>
                        <a href="/drop/{{drop.slug}}" target="_blank" class="url-display clickable-url" title="Click to open in new tab">
                            <span class="url-prefix">{{domain}}/drop/</span>
                            <span class="url-slug" id="preview-url-slug">{{drop.slug}}</span>
                            <svg class="external-link-icon" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>

<div id="edit-error" class="error-message" style="display: none;"></div>
<div id="edit-success" class="success-message" style="display: none;"></div>

<!-- Fan Details Modal -->
<div class="fan-modal" id="fan-modal">
    <div class="fan-modal-content">
        <div class="fan-modal-header">
            <div class="fan-modal-title">
                <h3>Fan Details</h3>
                <button type="button" class="fan-modal-close" onclick="closeFanModal()">&times;</button>
            </div>
            <div class="fan-modal-contact">
                <div class="fan-modal-avatar" id="fan-modal-avatar">
                    <!-- Avatar initials will be inserted here -->
                </div>
                <div class="fan-modal-contact-info">
                    <h4 id="fan-modal-name">Loading...</h4>
                    <p id="fan-modal-email">Loading...</p>
                    <p id="fan-modal-phone" style="display: none;">Loading...</p>
                </div>
            </div>
        </div>

        <div class="fan-modal-body">
            <!-- Contact Information Section -->
            <div class="fan-modal-section">
                <div class="fan-modal-section-title">
                    <svg class="fan-modal-section-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6L21 9ZM3 9L9 6V4L3 7V9ZM15 18V20L21 17V15L15 18ZM3 15V17L9 20V18L3 15ZM12 7C14.21 7 16 8.79 16 11C16 13.21 14.21 15 12 15C9.79 15 8 13.21 8 11C8 8.79 9.79 7 12 7Z"/>
                    </svg>
                    Contact Information
                </div>
                <div class="fan-modal-grid">
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">Email Address</div>
                        <div class="fan-modal-field-value" id="fan-modal-detail-email">Loading...</div>
                    </div>
                    <div class="fan-modal-field" id="fan-modal-phone-field" style="display: none;">
                        <div class="fan-modal-field-label">Phone Number</div>
                        <div class="fan-modal-field-value" id="fan-modal-detail-phone">Loading...</div>
                    </div>
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">Location</div>
                        <div class="fan-modal-field-value" id="fan-modal-location">Loading...</div>
                    </div>
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">Acquisition Channel</div>
                        <div class="fan-modal-field-value" id="fan-modal-channel">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Activity Information Section -->
            <div class="fan-modal-section">
                <div class="fan-modal-section-title">
                    <svg class="fan-modal-section-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9,17H7V10H9V17M13,17H11V7H13V17M17,17H15V13H17V17M19.5,19.1H4.5V5H19.5V19.1Z"/>
                    </svg>
                    Activity & Engagement
                </div>
                <div class="fan-modal-grid">
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">First Signup</div>
                        <div class="fan-modal-field-value" id="fan-modal-first-signup">Loading...</div>
                    </div>
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">Total RSVPs</div>
                        <div class="fan-modal-field-value" id="fan-modal-total-rsvps">Loading...</div>
                    </div>
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">User Agent</div>
                        <div class="fan-modal-field-value" id="fan-modal-user-agent">Loading...</div>
                    </div>
                    <div class="fan-modal-field">
                        <div class="fan-modal-field-label">IP Address</div>
                        <div class="fan-modal-field-value" id="fan-modal-ip">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Drop History Section -->
            <div class="fan-modal-section">
                <div class="fan-modal-section-title">
                    <svg class="fan-modal-section-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
                    </svg>
                    Drop History
                </div>
                <div class="fan-modal-drops-list" id="fan-modal-drops">
                    <!-- Drop history will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

{{> footer}}

<!-- Modular Systems -->
<link rel="stylesheet" href="/css/drop-shared.css">
<script src="/js/drop-color-system.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('drop-edit-form');
    const dropId = '{{drop.id}}';

    // Enhanced real-time preview updates
    const titleInput = document.getElementById('edit-title');
    const descriptionInput = document.getElementById('edit-description');
    const slugInput = document.getElementById('edit-slug');
    const buttonTextInput = document.getElementById('edit-button-text');
    const buttonColorInput = document.getElementById('edit-button-color');
    const buttonTextColorInput = document.getElementById('edit-button-text-color');
    const backgroundColorInput = document.getElementById('edit-background-color');
    const backgroundTypeSelect = document.getElementById('edit-background-type');
    const cardBackgroundTypeSelect = document.getElementById('edit-card-background-type');
    const titleColorInput = document.getElementById('edit-title-color');
    const descriptionColorInput = document.getElementById('edit-description-color');
    const coverImageInput = document.getElementById('edit-cover-image');

    const previewDrop = document.getElementById('drop-preview');
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const previewButton = document.getElementById('preview-button');
    const previewButtonText = document.getElementById('preview-button-text');
    const previewUrlSlug = document.getElementById('preview-url-slug');
    const previewCover = document.getElementById('preview-cover');
    const previewCoverImage = document.getElementById('preview-cover-image');
    const previewViewport = document.getElementById('preview-viewport');

    // BULLETPROOF INITIALIZATION with error handling
    function initializePreview() {
        try {
            console.log('🚀 Initializing preview system...');

            // Initialize colors first
            updatePreviewColors();

            // Then initialize content
            updatePreviewContent();

            // Verify all elements are properly initialized
            const requiredElements = [previewTitle, previewDescription, previewButton, previewButtonText];
            const missingElements = requiredElements.filter(el => !el);

            if (missingElements.length > 0) {
                console.warn('⚠️ Some preview elements are missing:', missingElements);
            } else {
                console.log('✅ All preview elements initialized successfully');
            }

            // Force initial repaint
            const previewContainer = previewDrop.closest('.preview-viewport') || previewDrop;
            previewContainer.style.display = 'none';
            previewContainer.offsetHeight; // Trigger reflow
            previewContainer.style.display = '';

            console.log('🎨 Preview initialization complete');

        } catch (error) {
            console.error('❌ Error initializing preview:', error);
            // Fallback: try again after a short delay
            setTimeout(() => {
                console.log('🔄 Retrying preview initialization...');
                updatePreviewColors();
                updatePreviewContent();
            }, 500);
        }
    }

    // BULLETPROOF CSS CUSTOM PROPERTIES SYSTEM
    // Based on MDN best practices and research findings
    function updatePreviewColors() {
        const backgroundColor = backgroundColorInput.value || '#DC2626';
        const backgroundType = backgroundTypeSelect ? backgroundTypeSelect.value : 'gradient';
        const cardBackgroundType = cardBackgroundTypeSelect ? cardBackgroundTypeSelect.value : 'solid_dark';
        const titleColor = titleColorInput.value || '#ffffff';
        const descriptionColor = descriptionColorInput.value || '#ffffff';
        const buttonColor = buttonColorInput.value || '#DC2626';
        const buttonTextColor = buttonTextColorInput ? buttonTextColorInput.value : '#ffffff';

        console.log('🎨 Updating preview colors:', {
            backgroundColor, backgroundType, cardBackgroundType,
            titleColor, descriptionColor, buttonColor, buttonTextColor
        });

        // ENHANCED APPROACH: Apply CSS custom properties to :root for maximum inheritance
        const documentRoot = document.documentElement;

        // Set CSS variables with preview-specific prefixes to avoid conflicts
        documentRoot.style.setProperty('--preview-drop-background-color', backgroundColor);
        documentRoot.style.setProperty('--preview-drop-background-type', backgroundType);
        documentRoot.style.setProperty('--preview-drop-card-background-type', cardBackgroundType);
        documentRoot.style.setProperty('--preview-drop-title-color', titleColor);
        documentRoot.style.setProperty('--preview-drop-description-color', descriptionColor);
        documentRoot.style.setProperty('--preview-drop-button-color', buttonColor);
        documentRoot.style.setProperty('--preview-drop-button-text-color', buttonTextColor);

        // FIXED: Also set the standard variables for better compatibility
        documentRoot.style.setProperty('--drop-background-color', backgroundColor);
        documentRoot.style.setProperty('--drop-card-color', cardColor);
        documentRoot.style.setProperty('--drop-title-color', titleColor);
        documentRoot.style.setProperty('--drop-description-color', descriptionColor);
        documentRoot.style.setProperty('--drop-button-color', buttonColor);
        documentRoot.style.setProperty('--drop-form-field-color', formFieldColor);

        // Calculate and set button text color with intelligent contrast
        const buttonTextColor = getContrastColor(buttonColor);
        documentRoot.style.setProperty('--preview-drop-button-text-color', buttonTextColor);

        // ALSO apply to preview container as fallback for better scope control
        const previewContainer = previewDrop.closest('.preview-viewport') || previewDrop;
        previewContainer.style.setProperty('--drop-background-color', backgroundColor);
        previewContainer.style.setProperty('--drop-card-color', cardColor);
        previewContainer.style.setProperty('--drop-title-color', titleColor);
        previewContainer.style.setProperty('--drop-description-color', descriptionColor);
        previewContainer.style.setProperty('--drop-button-color', buttonColor);
        previewContainer.style.setProperty('--drop-button-text-color', buttonTextColor);
        previewContainer.style.setProperty('--drop-form-field-color', formFieldColor);

        console.log('✅ Applied CSS variables to both :root and preview container for maximum compatibility');

        // Force a repaint to ensure changes are applied immediately
        previewContainer.style.display = 'none';
        previewContainer.offsetHeight; // Trigger reflow
        previewContainer.style.display = '';

        console.log('🔄 Forced repaint for immediate visual update');
    }

    // Update preview content
    function updatePreviewContent() {
        previewTitle.textContent = titleInput.value || 'Drop Title';
        previewDescription.textContent = descriptionInput.value || 'Drop description';
        previewButtonText.textContent = buttonTextInput.value || 'Get Notified';
        previewUrlSlug.textContent = slugInput.value || 'drop-slug';

        // Handle cover image
        const coverImageUrl = coverImageInput.value;
        if (coverImageUrl && isValidUrl(coverImageUrl)) {
            previewCoverImage.src = coverImageUrl;
            previewCover.style.display = 'block';
        } else {
            previewCover.style.display = 'none';
        }
    }

    // Calculate contrast color for text on colored backgrounds
    function getContrastColor(hexColor) {
        // Remove # if present
        const color = hexColor.replace('#', '');

        // Convert to RGB
        const r = parseInt(color.substr(0, 2), 16);
        const g = parseInt(color.substr(2, 2), 16);
        const b = parseInt(color.substr(4, 2), 16);

        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return white for dark colors, black for light colors
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }

    // Validate URL
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    // BULLETPROOF EVENT LISTENERS with debouncing for performance
    // Debounce function to prevent excessive updates
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Debounced update functions for better performance
    const debouncedUpdateColors = debounce(updatePreviewColors, 100);
    const debouncedUpdateContent = debounce(updatePreviewContent, 100);

    // Content update listeners
    titleInput.addEventListener('input', debouncedUpdateContent);
    descriptionInput.addEventListener('input', debouncedUpdateContent);
    slugInput.addEventListener('input', debouncedUpdateContent);
    buttonTextInput.addEventListener('input', debouncedUpdateContent);
    coverImageInput.addEventListener('input', debouncedUpdateContent);

    // Color update listeners with immediate feedback
    buttonColorInput.addEventListener('input', updatePreviewColors);
    if (buttonTextColorInput) buttonTextColorInput.addEventListener('input', updatePreviewColors);
    backgroundColorInput.addEventListener('input', updatePreviewColors);
    if (backgroundTypeSelect) backgroundTypeSelect.addEventListener('change', updatePreviewColors);
    if (cardBackgroundTypeSelect) cardBackgroundTypeSelect.addEventListener('change', updatePreviewColors);
    titleColorInput.addEventListener('input', updatePreviewColors);
    descriptionColorInput.addEventListener('input', updatePreviewColors);

    // Also add 'change' event listeners for better compatibility
    buttonColorInput.addEventListener('change', updatePreviewColors);
    if (buttonTextColorInput) buttonTextColorInput.addEventListener('change', updatePreviewColors);
    backgroundColorInput.addEventListener('change', updatePreviewColors);
    titleColorInput.addEventListener('change', updatePreviewColors);
    descriptionColorInput.addEventListener('change', updatePreviewColors);

    // MOBILE-ONLY PREVIEW: No device switching needed
    function initializeMobilePreview() {
        // Force mobile preview always
        previewViewport.className = 'preview-viewport mobile';

        // Update device indicator
        const deviceIndicator = document.getElementById('device-indicator');
        if (deviceIndicator) {
            deviceIndicator.textContent = 'Mobile View';
        }

        console.log('Preview locked to mobile view for optimal display');
    }

    // MODERN TAB SYSTEM - Inspired by Laylo's design patterns
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // Tab switching functionality
    function switchTab(targetTab) {
        // Remove active class from all tabs and contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        const activeButton = document.querySelector(`[data-tab="${targetTab}"]`);
        const activeContent = document.querySelector(`[data-tab-content="${targetTab}"]`);

        if (activeButton && activeContent) {
            activeButton.classList.add('active');
            activeContent.classList.add('active');

            console.log(`Switched to tab: ${targetTab}`);
        }
    }

    // Add click listeners to tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.dataset.tab;
            switchTab(targetTab);
        });
    });

    // Initialize with first tab active (Page)
    switchTab('page');

    // 🚀 ANALYTICS SYSTEM INITIALIZATION
    initializeAnalytics();

    // Initialize preview
    initializePreview();

    // Initialize mobile-only preview
    initializeMobilePreview();

    // PHONE NUMBER TOGGLE SYSTEM - Fix preview and form field issues
    function initializeDataCollectionToggles() {
        const collectEmailToggle = document.querySelector('input[name="collect_email"]');
        const collectPhoneToggle = document.querySelector('input[name="collect_phone"]');
        const previewEmailField = document.getElementById('preview-email-field');
        const previewPhoneField = document.getElementById('preview-phone-field');

        // Update preview form fields based on toggle states
        function updatePreviewFormFields() {
            if (previewEmailField) {
                previewEmailField.style.display = collectEmailToggle.checked ? 'block' : 'none';
            }
            if (previewPhoneField) {
                previewPhoneField.style.display = collectPhoneToggle.checked ? 'block' : 'none';
            }
            console.log('📱 Preview form fields updated:', {
                email: collectEmailToggle.checked,
                phone: collectPhoneToggle.checked
            });
        }

        // Add event listeners for toggle changes
        if (collectEmailToggle) {
            collectEmailToggle.addEventListener('change', updatePreviewFormFields);
        }
        if (collectPhoneToggle) {
            collectPhoneToggle.addEventListener('change', updatePreviewFormFields);
        }

        // Initialize with current states
        updatePreviewFormFields();

        console.log('✅ Data collection toggles initialized');
    }

    // Initialize data collection toggles
    initializeDataCollectionToggles();

    // WORLD-CLASS STATUS INDICATOR SYSTEM - Dynamic real-time updates
    function initializeStatusIndicator() {
        const activeDropToggle = document.getElementById('active-drop-toggle');
        const statusIndicator = document.getElementById('drop-status-indicator');

        if (!activeDropToggle || !statusIndicator) {
            console.warn('⚠️ Status indicator elements not found');
            return;
        }

        function updateStatusIndicator(isActive) {
            const statusText = statusIndicator.querySelector('.status-text');
            const statusClasses = ['live', 'inactive'];

            // Remove all status classes
            statusIndicator.classList.remove(...statusClasses);

            if (isActive) {
                // LIVE STATE: Green with pulse animation
                statusIndicator.classList.add('live');
                statusText.textContent = 'Live';
                console.log('🟢 Status updated to LIVE');
            } else {
                // INACTIVE STATE: Red with pulse animation
                statusIndicator.classList.add('inactive');
                statusText.textContent = 'Inactive';
                console.log('🔴 Status updated to INACTIVE');
            }

            // PREMIUM: Add subtle bounce animation on change
            statusIndicator.style.transform = 'scale(1.1)';
            setTimeout(() => {
                statusIndicator.style.transform = '';
            }, 200);
        }

        // Listen for toggle changes
        activeDropToggle.addEventListener('change', function() {
            const isActive = this.checked;
            updateStatusIndicator(isActive);

            console.log('🎯 Active Drop toggle changed:', {
                isActive: isActive,
                timestamp: new Date().toISOString()
            });
        });

        // Initialize with current state
        updateStatusIndicator(activeDropToggle.checked);

        console.log('✅ World-class status indicator initialized');
    }

    // Initialize status indicator
    initializeStatusIndicator();

    // 🎯 SMART CHANGE DETECTION SYSTEM
    let originalFormData = {};
    let hasChanges = false;

    // Capture original form state
    function captureOriginalState() {
        const formData = new FormData(form);
        originalFormData = {};

        // Capture all form fields
        for (let [key, value] of formData.entries()) {
            originalFormData[key] = value;
        }

        // Also capture checkbox states
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            originalFormData[checkbox.name] = checkbox.checked;
        });

        console.log('📸 Original form state captured:', originalFormData);
    }

    // Check if form has changes
    function checkForChanges() {
        const currentFormData = new FormData(form);
        let currentData = {};

        // Capture current form fields
        for (let [key, value] of currentFormData.entries()) {
            currentData[key] = value;
        }

        // Also capture current checkbox states
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            currentData[checkbox.name] = checkbox.checked;
        });

        // Compare with original
        const changed = Object.keys(originalFormData).some(key => {
            return originalFormData[key] !== currentData[key];
        }) || Object.keys(currentData).some(key => {
            return originalFormData[key] !== currentData[key];
        });

        if (changed !== hasChanges) {
            hasChanges = changed;
            updateActionButtons();
            console.log('🔄 Form changes detected:', hasChanges);
        }
    }

    // Update action buttons lighting state
    function updateActionButtons() {
        const saveBtn = document.getElementById('save-btn');
        const cancelBtn = document.getElementById('cancel-btn');

        if (hasChanges) {
            // 🟢 Light up save button GREEN
            saveBtn.classList.add('changes-detected');
            // 🔴 Light up cancel button RED
            cancelBtn.classList.add('changes-detected');

            console.log('🔥 Buttons lit up - Save: GREEN, Cancel: RED');
        } else {
            // Turn off button lighting
            saveBtn.classList.remove('changes-detected');
            cancelBtn.classList.remove('changes-detected');

            console.log('💡 Button lighting turned off');
        }
    }

    // Cancel changes functionality
    function cancelChanges() {
        // Reset all form fields to original values
        Object.keys(originalFormData).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = originalFormData[key];
                } else {
                    field.value = originalFormData[key];
                }

                // Trigger change event to update preview
                field.dispatchEvent(new Event('input', { bubbles: true }));
                field.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });

        // Update preview
        updatePreviewColors();
        updatePreviewContent();

        // Reset change detection
        hasChanges = false;
        updateActionButtons();

        console.log('↩️ Changes cancelled, form reset to original state');
    }

    // Add event listeners for change detection
    function initializeChangeDetection() {
        // Capture original state
        captureOriginalState();

        // Add listeners to all form inputs
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', checkForChanges);
            input.addEventListener('change', checkForChanges);
        });

        // Cancel button functionality
        const cancelBtn = document.getElementById('cancel-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', cancelChanges);
        }

        console.log('✅ Smart change detection initialized');
    }

    // Initialize change detection
    initializeChangeDetection();

    // FIXED: Keep preview mobile-focused (no auto-switching)
    window.addEventListener('resize', () => {
        // Keep preview mobile-focused for better fit in preview window
        // Users can manually switch if they want desktop view
        console.log('Window resized - keeping mobile preview for better fit');
    });

    // Mobile preview toggle functionality
    window.togglePreview = function() {
        const toggle = document.querySelector('.preview-toggle');
        const collapsible = document.querySelector('.preview-collapsible');
        const icon = document.querySelector('.preview-toggle-icon');

        if (collapsible.classList.contains('collapsed')) {
            collapsible.classList.remove('collapsed');
            toggle.classList.remove('collapsed');
        } else {
            collapsible.classList.add('collapsed');
            toggle.classList.add('collapsed');
        }
    };

    // Auto-collapse preview on mobile by default
    if (window.innerWidth <= 768) {
        const toggle = document.querySelector('.preview-toggle');
        const collapsible = document.querySelector('.preview-collapsible');

        if (toggle && collapsible) {
            collapsible.classList.add('collapsed');
            toggle.classList.add('collapsed');
        }
    }

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());

        // Convert checkboxes to booleans
        data.is_active = formData.has('is_active');
        data.collect_email = formData.has('collect_email');
        data.collect_phone = formData.has('collect_phone');

        const submitBtn = this.querySelector('button[type="submit"]');
        const buttonText = submitBtn.querySelector('.button-text');
        const buttonSpinner = submitBtn.querySelector('.button-spinner');

        // Show loading state
        submitBtn.disabled = true;
        buttonText.style.display = 'none';
        buttonSpinner.style.display = 'inline-flex';

        try {
            const response = await fetch(`/api/drops/${dropId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                showSuccessBanner('Drop updated successfully!');
                // Update the drop data in memory if needed
                if (result.data) {
                    // Update any displayed data with the latest from server
                    console.log('Drop updated:', result.data);
                }
            } else {
                showErrorBanner(result.message || 'Failed to update drop');
            }
        } catch (error) {
            showErrorBanner('Network error. Please try again.');
        } finally {
            submitBtn.disabled = false;
            buttonText.style.display = 'inline';
            buttonSpinner.style.display = 'none';
        }
    });

    function showError(message) {
        const errorDiv = document.getElementById('edit-error');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => errorDiv.style.display = 'none', 5000);
    }

    function showSuccess(message) {
        const successDiv = document.getElementById('edit-success');
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        setTimeout(() => successDiv.style.display = 'none', 5000);
    }

    // Enhanced banner functions for better UX
    function showSuccessBanner(message) {
        createBanner(message, 'success');
    }

    function showErrorBanner(message) {
        createBanner(message, 'error');
    }

    function createBanner(message, type) {
        // Remove any existing banners
        const existingBanners = document.querySelectorAll('.notification-banner');
        existingBanners.forEach(banner => banner.remove());

        // Create new banner
        const banner = document.createElement('div');
        banner.className = `notification-banner ${type}`;
        banner.innerHTML = `
            <div class="banner-content">
                <div class="banner-icon">
                    ${type === 'success' ? '✅' : '❌'}
                </div>
                <div class="banner-message">${message}</div>
                <button class="banner-close" onclick="this.parentElement.parentElement.remove()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
        `;

        // Add to page
        document.body.appendChild(banner);

        // Animate in
        setTimeout(() => banner.classList.add('show'), 100);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            banner.classList.remove('show');
            setTimeout(() => banner.remove(), 300);
        }, 5000);
    }
    // 🚀 LAYLO-STYLE ANALYTICS SYSTEM

    let currentPage = 1;
    let currentSearch = '';
    let currentSort = 'latest';
    const pageSize = 50;

    function initializeAnalytics() {
        // Set up event listeners for analytics controls
        const searchInput = document.getElementById('fans-search');
        const sortSelect = document.getElementById('fans-sort');
        const prevButton = document.getElementById('prev-page');
        const nextButton = document.getElementById('next-page');

        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearch = e.target.value;
                    currentPage = 1;
                    loadFansData();
                }, 300);
            });
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                currentSort = e.target.value;
                currentPage = 1;
                loadFansData();
            });
        }

        if (prevButton) {
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    loadFansData();
                }
            });
        }

        if (nextButton) {
            nextButton.addEventListener('click', () => {
                currentPage++;
                loadFansData();
            });
        }

        // Load initial data when analytics tab is clicked
        const analyticsTab = document.querySelector('[data-tab="analytics"]');
        if (analyticsTab) {
            analyticsTab.addEventListener('click', () => {
                setTimeout(() => {
                    loadAnalyticsData();
                }, 100);
            });
        }
    }

    async function loadAnalyticsData() {
        try {
            // Load summary stats
            await loadSummaryStats();

            // Load fans data
            await loadFansData();

        } catch (error) {
            console.error('Failed to load analytics data:', error);
            showAnalyticsError('Failed to load analytics data');
        }
    }

    async function loadSummaryStats() {
        try {
            const response = await fetch(`/api/drops/analytics/summary?dropId={{drop.id}}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                const stats = result.data;

                // Update summary cards with safe defaults
                document.getElementById('total-fans').textContent = (stats.totalUniqueFans || 0).toLocaleString();
                document.getElementById('total-rsvps').textContent = (stats.totalRSVPs || 0).toLocaleString();
                document.getElementById('repeat-fans').textContent = (stats.repeatFans || 0).toLocaleString();
                document.getElementById('avg-rsvps').textContent = stats.averageRSVPsPerFan || '0';

                // Update change indicators with safe calculations
                const recentSignups = stats.recentSignups || 0;
                const totalFans = stats.totalUniqueFans || 0;
                const repeatFans = stats.repeatFans || 0;

                document.getElementById('fans-change').textContent = `+${recentSignups} this week`;
                document.getElementById('rsvps-change').textContent = `+${recentSignups} this week`;
                document.getElementById('repeat-change').textContent = totalFans > 0 ? `${((repeatFans / totalFans) * 100).toFixed(1)}% of fans` : '0% of fans';
                document.getElementById('avg-change').textContent = 'All time average';
            } else {
                throw new Error(result.message || 'Failed to load summary stats');
            }
        } catch (error) {
            console.error('Failed to load summary stats:', error);
            // Set default values on error
            document.getElementById('total-fans').textContent = '0';
            document.getElementById('total-rsvps').textContent = '0';
            document.getElementById('repeat-fans').textContent = '0';
            document.getElementById('avg-rsvps').textContent = '0';
            document.getElementById('fans-change').textContent = 'No data';
            document.getElementById('rsvps-change').textContent = 'No data';
            document.getElementById('repeat-change').textContent = 'No data';
            document.getElementById('avg-change').textContent = 'No data';
        }
    }

    async function loadFansData() {
        const loadingElement = document.getElementById('fans-loading');
        const tableElement = document.getElementById('fans-table');
        const emptyElement = document.getElementById('fans-empty');
        const paginationElement = document.getElementById('fans-pagination');

        // Show loading state
        loadingElement.style.display = 'flex';
        tableElement.style.display = 'none';
        emptyElement.style.display = 'none';
        paginationElement.style.display = 'none';

        try {
            const offset = (currentPage - 1) * pageSize;
            const params = new URLSearchParams({
                limit: pageSize,
                offset: offset,
                search: currentSearch,
                sortBy: currentSort,
                dropId: '{{drop.id}}'
            });

            const response = await fetch(`/api/drops/analytics/fans?${params}`);
            const result = await response.json();

            if (result.success) {
                const { fans, total, pagination } = result.data;

                // Hide loading
                loadingElement.style.display = 'none';

                if (fans.length === 0) {
                    // Show empty state
                    emptyElement.style.display = 'flex';
                } else {
                    // Show table with data
                    renderFansTable(fans);
                    renderPagination(pagination);
                    tableElement.style.display = 'table';

                    // Show mobile cards
                    const cardsElement = document.getElementById('fans-cards');
                    cardsElement.style.display = 'flex';

                    paginationElement.style.display = 'flex';
                }
            } else {
                throw new Error(result.message || 'Failed to load fans data');
            }
        } catch (error) {
            console.error('Failed to load fans data:', error);
            loadingElement.style.display = 'none';
            showAnalyticsError('Failed to load fans data');
        }
    }

    function renderFansTable(fans) {
        const tbody = document.getElementById('fans-table-body');
        const cardsContainer = document.getElementById('fans-cards');

        // Clear both table and cards
        tbody.innerHTML = '';
        cardsContainer.innerHTML = '';

        fans.forEach(fan => {
            // Render desktop table row
            renderDesktopTableRow(fan, tbody);

            // Render mobile card
            renderMobileCard(fan, cardsContainer);
        });
    }

    function renderDesktopTableRow(fan, tbody) {
        const row = document.createElement('tr');
        row.className = fan.is_repeat_fan ? 'repeat-fan' : '';
        row.style.cursor = 'pointer';

        // Add click handler for modal
        row.addEventListener('click', () => openFanModal(fan));

        // Format join date
        const joinDate = new Date(fan.join_date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        // Create contact cell with name and email
        const contactName = fan.name || 'Anonymous';
        const contactEmail = fan.email;

        row.innerHTML = `
            <td class="contact-cell">
                <div class="contact-info">
                    <div class="contact-name">${escapeHtml(contactName)}</div>
                    <div class="contact-email">${escapeHtml(contactEmail)}</div>
                    ${fan.phone ? `<div class="contact-phone">${escapeHtml(fan.phone)}</div>` : ''}
                </div>
                ${fan.is_repeat_fan ? '<span class="repeat-badge">Repeat Fan</span>' : ''}
            </td>
            <td class="location-cell">${escapeHtml(fan.location || 'Unknown')}</td>
            <td class="date-cell">${joinDate}</td>
            <td class="channel-cell">
                <span class="channel-badge channel-${(fan.acquisition_channel || 'direct').toLowerCase().replace(/[^a-z0-9]/g, '')}">${escapeHtml(fan.acquisition_channel || 'Direct')}</span>
            </td>
            <td class="rsvp-cell">
                <span class="rsvp-count">${fan.total_rsvps || 1}</span>
                ${fan.total_rsvps > 1 ? `<span class="rsvp-drops">${fan.fan_drops ? fan.fan_drops.length : 1} drops</span>` : ''}
            </td>
        `;

        tbody.appendChild(row);
    }

    function renderMobileCard(fan, container) {
        const card = document.createElement('div');
        card.className = `fan-card ${fan.is_repeat_fan ? 'repeat-fan' : ''}`;

        // Add click handler for modal
        card.addEventListener('click', () => openFanModal(fan));

        // Format join date
        const joinDate = new Date(fan.join_date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        const contactName = fan.name || 'Anonymous';
        const contactEmail = fan.email;

        card.innerHTML = `
            <div class="fan-card-header">
                <div class="fan-card-contact">
                    <div class="fan-card-name">${escapeHtml(contactName)}</div>
                    <div class="fan-card-email">${escapeHtml(contactEmail)}</div>
                    ${fan.phone ? `<div class="fan-card-phone">${escapeHtml(fan.phone)}</div>` : ''}
                </div>
                <div class="fan-card-badges">
                    ${fan.is_repeat_fan ? '<span class="repeat-badge">Repeat Fan</span>' : ''}
                    <span class="rsvp-count">${fan.total_rsvps || 1} RSVPs</span>
                </div>
            </div>
            <div class="fan-card-details">
                <div class="fan-card-detail">
                    <div class="fan-card-detail-label">Location</div>
                    <div class="fan-card-detail-value">${escapeHtml(fan.location || 'Unknown')}</div>
                </div>
                <div class="fan-card-detail">
                    <div class="fan-card-detail-label">Joined On</div>
                    <div class="fan-card-detail-value">${joinDate}</div>
                </div>
                <div class="fan-card-detail">
                    <div class="fan-card-detail-label">Acq. Channel</div>
                    <div class="fan-card-detail-value">
                        <span class="channel-badge channel-${(fan.acquisition_channel || 'direct').toLowerCase().replace(/[^a-z0-9]/g, '')}">${escapeHtml(fan.acquisition_channel || 'Direct')}</span>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(card);
    }

    function renderPagination(pagination) {
        const paginationText = document.getElementById('pagination-text');
        const prevButton = document.getElementById('prev-page');
        const nextButton = document.getElementById('next-page');
        const pageNumbers = document.getElementById('page-numbers');

        // Update pagination text
        const start = pagination.offset + 1;
        const end = Math.min(pagination.offset + pagination.limit, pagination.total);
        paginationText.textContent = `Showing ${start}-${end} of ${pagination.total} fans`;

        // Update button states
        prevButton.disabled = pagination.currentPage <= 1;
        nextButton.disabled = pagination.currentPage >= pagination.pages;

        // Update page numbers
        pageNumbers.innerHTML = '';
        const maxVisiblePages = 5;
        const startPage = Math.max(1, pagination.currentPage - Math.floor(maxVisiblePages / 2));
        const endPage = Math.min(pagination.pages, startPage + maxVisiblePages - 1);

        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = `page-number ${i === pagination.currentPage ? 'active' : ''}`;
            pageButton.textContent = i;
            pageButton.addEventListener('click', () => {
                currentPage = i;
                loadFansData();
            });
            pageNumbers.appendChild(pageButton);
        }
    }

    function showAnalyticsError(message) {
        const emptyElement = document.getElementById('fans-empty');
        emptyElement.innerHTML = `
            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor" class="empty-icon error">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <h3>Error loading analytics</h3>
            <p>${escapeHtml(message)}</p>
            <button type="button" onclick="loadAnalyticsData()" class="retry-btn">Try Again</button>
        `;
        emptyElement.style.display = 'flex';
    }

    function exportFans() {
        // Create CSV export of fans data
        const params = new URLSearchParams({
            search: currentSearch,
            sortBy: currentSort,
            dropId: '{{drop.id}}',
            format: 'csv'
        });

        window.open(`/api/drops/analytics/fans?${params}`, '_blank');
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 🎯 RESEARCH-BASED FAN MODAL SYSTEM
    // Based on HubSpot, Salesforce, and modern CRM best practices

    async function openFanModal(fan) {
        const modal = document.getElementById('fan-modal');
        currentFanModal = fan;

        // Show modal with loading state
        modal.classList.add('active');

        // Populate basic info immediately
        populateModalBasicInfo(fan);

        // Load detailed CRM data
        await loadFanCRMData(fan);
    }

    function populateModalBasicInfo(fan) {
        // Generate avatar initials
        const name = fan.name || fan.email || 'Anonymous';
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);

        // Populate header
        document.getElementById('fan-modal-avatar').textContent = initials;
        document.getElementById('fan-modal-name').textContent = fan.name || 'Anonymous Fan';
        document.getElementById('fan-modal-email').textContent = fan.email;

        // Show/hide phone
        const phoneElement = document.getElementById('fan-modal-phone');
        const phoneFieldElement = document.getElementById('fan-modal-phone-field');
        if (fan.phone) {
            phoneElement.textContent = fan.phone;
            phoneElement.style.display = 'block';
            phoneFieldElement.style.display = 'block';
        } else {
            phoneElement.style.display = 'none';
            phoneFieldElement.style.display = 'none';
        }

        // Populate basic fields
        document.getElementById('fan-modal-detail-email').textContent = fan.email;
        document.getElementById('fan-modal-detail-phone').textContent = fan.phone || 'Not provided';
        document.getElementById('fan-modal-location').textContent = fan.location || 'Unknown';
        document.getElementById('fan-modal-channel').textContent = fan.acquisition_channel || 'Direct';

        // Format and populate activity info
        const joinDate = new Date(fan.join_date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        document.getElementById('fan-modal-first-signup').textContent = joinDate;
        document.getElementById('fan-modal-total-rsvps').textContent = fan.total_rsvps || 1;
        document.getElementById('fan-modal-user-agent').textContent = fan.user_agent || 'Unknown';
        document.getElementById('fan-modal-ip').textContent = fan.ip_address || 'Unknown';
    }

    async function loadFanCRMData(fan) {
        try {
            // Load comprehensive fan data including drop history
            const response = await fetch(`/api/fans/${encodeURIComponent(fan.email)}/details`);

            if (response.ok) {
                const fanDetails = await response.json();
                populateModalCRMData(fanDetails);
            } else {
                // Fallback to basic data if CRM not available
                populateModalDropHistory(fan);
            }
        } catch (error) {
            console.warn('Could not load CRM data:', error);
            populateModalDropHistory(fan);
        }
    }

    function populateModalCRMData(fanDetails) {
        // Update with comprehensive CRM data
        if (fanDetails.drops && fanDetails.drops.length > 0) {
            populateModalDropHistory({ fan_drops: fanDetails.drops });
        }

        // Add any additional CRM fields here
        if (fanDetails.totalValue) {
            // Could add revenue/value information
        }
    }

    function populateModalDropHistory(fan) {
        const dropsContainer = document.getElementById('fan-modal-drops');

        if (fan.fan_drops && fan.fan_drops.length > 0) {
            dropsContainer.innerHTML = fan.fan_drops.map(drop => {
                const dropDate = new Date(drop.created_at).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });

                return `
                    <div class="fan-modal-drop-item">
                        <div class="fan-modal-drop-name">${escapeHtml(drop.title)}</div>
                        <div class="fan-modal-drop-date">${dropDate}</div>
                    </div>
                `;
            }).join('');
        } else {
            dropsContainer.innerHTML = `
                <div class="fan-modal-drop-item">
                    <div class="fan-modal-drop-name">Current Drop</div>
                    <div class="fan-modal-drop-date">Active signup</div>
                </div>
            `;
        }
    }

    // Global modal functions
    window.closeFanModal = function() {
        const modal = document.getElementById('fan-modal');
        modal.classList.remove('active');
        currentFanModal = null;
    }

    // Close modal when clicking outside
    document.addEventListener('click', (e) => {
        const modal = document.getElementById('fan-modal');
        if (e.target === modal) {
            closeFanModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && currentFanModal) {
            closeFanModal();
        }
    });

});
</script>
