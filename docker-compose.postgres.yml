services:
  server:
    build:
      context: .
    volumes:
      - custom:/kutt/custom
    environment:
        DB_CLIENT: pg
        DB_HOST: postgres
        DB_PORT: 5432
        REDIS_ENABLED: true
        REDIS_HOST: redis
        REDIS_PORT: 6379
    ports:
      - 3000:3000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
  postgres:
    image: postgres
    restart: always
    user: ${DB_USER}
    volumes:
      - db_data_pg:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD} 
    expose:
      - 5432
    healthcheck:
      test: [ "CMD", "pg_isready" ]
      interval: 10s
      timeout: 5s
      retries: 5
  redis:
    image: redis:alpine
    restart: always
    expose:
      - 6379
volumes:
  db_data_pg:
  custom: