# Optional - App port to run on
PORT=3000

# Optional - The name of the site where <PERSON><PERSON> is hosted
SITE_NAME=Kutt

# Optional - The domain that this website is on
DEFAULT_DOMAIN=localhost:3000

# Required - A passphrase to encrypt JWT. Use a random long string
JWT_SECRET=

# Optional - Database client. Available clients for the supported databases:
# pg | better-sqlite3 | mysql2
# other supported drivers that you can use but you have to manually install them with npm:
# pg-native | sqlite3 | mysql
DB_CLIENT=better-sqlite3

# Optional - SQLite database file path
# Only if you're using SQLite
DB_FILENAME=db/data

# Optional - SQL database credential details
# Only if you're using Postgres or MySQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kutt
DB_USER=postgres
DB_PASSWORD=
DB_SSL=false
DB_POOL_MIN=0
DB_POOL_MAX=10

# Optional - Generated link length
LINK_LENGTH=6

# Optional - <PERSON>bet used to generate custom addresses
# Default value omits o, O, 0, i, I, l, 1, and j to avoid confusion when reading the URL
LINK_CUSTOM_ALPHABET=abcdefghkmnpqrstuvwxyzABCDEFGHKLMNPQRSTUVWXYZ23456789

# Optional - Tells the app that it's running behind a proxy server
# and that it should get the IP address from that proxy server
# if you're not using a proxy server then set this to false, otherwise users can override their IP address
TRUST_PROXY=true

# Optional - Redis host and port
REDIS_ENABLED=false
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
# The number for Redis database, between 0 and 15. Defaults to 0.
# If you don't know what this is, then you probably don't need to change it.
REDIS_DB=0

# Optional - Disable registration. Default is true.
DISALLOW_REGISTRATION=true

# Optional - Disable anonymous link creation. Default is true.
DISALLOW_ANONYMOUS_LINKS=true

# Optional - This would be shown to the user on the settings page
# It's only for display purposes and has no other use
SERVER_IP_ADDRESS=
SERVER_CNAME_ADDRESS=

# Optional - Use HTTPS for links with custom domain
# It's on you to generate SSL certificates for those domains manually, at least on this version for now
CUSTOM_DOMAIN_USE_HTTPS=false

# Optional - Email is used to verify or change email address, reset password, and send reports.
# If it's disabled, all the above functionality would be disabled as well.
# <AUTHOR> <EMAIL>". Leave it empty to use MAIL_USER.
# More info on the configuration on http://nodemailer.com/.
MAIL_ENABLED=false
MAIL_HOST=
MAIL_PORT=587
MAIL_SECURE=true
MAIL_USER=
MAIL_FROM=
MAIL_PASSWORD=

# Optional - Enable rate limitting for some API routes
ENABLE_RATE_LIMIT=false

# Optional - The email address that will receive submitted reports
REPORT_EMAIL=

# Optional - Support email to show on the app
CONTACT_EMAIL=
