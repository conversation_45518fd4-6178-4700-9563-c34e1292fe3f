<form id="login-signup" hx-post="/api/auth/login" hx-swap="outerHTML">
  <label class="{{#if errors.email}}error{{/if}}">
    Email address:
    <input
      name="email"
      id="email"
      type="email"
      autofocus="true"
      placeholder="Email address..."
      hx-preserve="true"
    />
    {{#if errors.email}}<p class="error">{{errors.email}}</p>{{/if}}
  </label>
  <label class="{{#if errors.password}}error{{/if}}">
    Password:
    <input
      name="password"
      id="password"
      type="password"
      placeholder="Password..."
      hx-preserve="true"
    />
    {{#if errors.password}}<p class="error">{{errors.password}}</p>{{/if}}
  </label>
  <div class="buttons-wrapper">
    <button 
      type="submit" 
      class="primary login {{#if disallow_registration}}full{{else}}{{#unless mail_enabled}}full{{/unless}}{{/if}}"
    >
      <span>{{> icons/login}}</span>
      <span>{{> icons/spinner}}</span>
      Log in
    </button>
    {{#unless disallow_registration}}
      {{#if mail_enabled}}
        <button 
          type="button"
          class="secondary signup" 
          hx-post="/api/auth/signup" 
          hx-target="#login-signup" 
          hx-trigger="click" 
          hx-indicator="#login-signup" 
          hx-swap="outerHTML"
          hx-sync="closest form"
          hx-on:htmx:before-request="htmx.addClass('#login-signup', 'signup')"
          hx-on:htmx:after-request="htmx.removeClass('#login-signup', 'signup')"
        >
            <span>{{> icons/new_user}}</span>
            <span>{{> icons/spinner}}</span>
            Sign up
        </button>
      {{/if}}
    {{/unless}}
  </div>
  {{#if mail_enabled}}
    <a class="forgot-password" href="/reset-password" title="Reset password">Forgot your password?</a>
  {{/if}}
  {{#unless errors}}
    {{#if error}}
      <p class="error">{{error}}</p>
    {{/if}}
  {{/unless}}
</form>