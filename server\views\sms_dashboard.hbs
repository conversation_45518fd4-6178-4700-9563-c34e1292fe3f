<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Dashboard - BOUNCE2BOUNCE</title>
    <link rel="stylesheet" href="/css/main.css">
    <style>
        .sms-dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .sms-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .sms-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .test-sms-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .compliance-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .compliance-info h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="sms-dashboard">
        <div class="sms-header">
            <h1>📱 SMS Dashboard</h1>
            <p>Manage Twilio SMS integration for BOUNCE2BOUNCE</p>
        </div>

        <div class="compliance-info">
            <h4>🛡️ SMS Compliance Notice</h4>
            <p>This SMS system follows TCPA compliance guidelines. All messages include opt-out instructions, and users can reply STOP to unsubscribe at any time.</p>
        </div>

        <div class="sms-status-grid">
            <div class="status-card">
                <h3>Service Status</h3>
                <div class="status-value" id="service-status">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>Phone Number</h3>
                <div class="status-value" id="phone-number">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>From Name</h3>
                <div class="status-value" id="from-name">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>Webhook Security</h3>
                <div class="status-value" id="webhook-status">Loading...</div>
            </div>
        </div>

        <div class="test-sms-section">
            <h2>🧪 Test SMS Functionality</h2>
            <p>Send a test SMS to verify your Twilio integration is working correctly.</p>
            
            <div id="test-result"></div>
            
            <form id="test-sms-form">
                <div class="form-group">
                    <label for="test-phone">Phone Number (with country code)</label>
                    <input type="tel" id="test-phone" placeholder="+1234567890" required>
                </div>
                
                <button type="submit" class="btn-primary">Send Test SMS</button>
            </form>
        </div>
    </div>

    <script>
        // Load SMS service status
        async function loadSMSStatus() {
            try {
                const response = await fetch('/api/sms/status');
                const status = await response.json();
                
                document.getElementById('service-status').textContent = status.enabled ? '✅ Enabled' : '❌ Disabled';
                document.getElementById('phone-number').textContent = status.phoneNumber || 'Not configured';
                document.getElementById('from-name').textContent = status.fromName || 'Not set';
                document.getElementById('webhook-status').textContent = status.hasWebhookSecret ? '🔒 Secured' : '⚠️ Not secured';
                
                // Update status card colors
                const serviceCard = document.getElementById('service-status').parentElement;
                serviceCard.style.borderLeftColor = status.enabled ? '#28a745' : '#dc3545';
                
            } catch (error) {
                console.error('Failed to load SMS status:', error);
                document.getElementById('service-status').textContent = '❌ Error loading status';
            }
        }

        // Handle test SMS form submission
        document.getElementById('test-sms-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const phoneNumber = document.getElementById('test-phone').value;
            const resultDiv = document.getElementById('test-result');
            
            // Show loading state
            resultDiv.innerHTML = '<div class="alert alert-info">📱 Sending test SMS...</div>';
            
            try {
                const response = await fetch('/api/sms/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phoneNumber })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            ✅ Test SMS sent successfully!<br>
                            <strong>Message SID:</strong> ${result.messageSid}<br>
                            <strong>Status:</strong> ${result.status}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-error">
                            ❌ Failed to send test SMS<br>
                            <strong>Error:</strong> ${result.error}
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        });

        // Load status on page load
        loadSMSStatus();
    </script>
</body>
</html>
