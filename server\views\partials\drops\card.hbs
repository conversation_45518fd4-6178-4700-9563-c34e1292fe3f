<!-- 🎯 STRATEGIC DROP CARD - CLEAN & PURPOSEFUL DESIGN -->
<div class="strategic-drop-card" data-drop-id="{{id}}">
  <!-- 🎨 Clean Header with Purposeful Animation -->
  <div class="card-header-clean">
    {{#if cover_image}}
      <div class="card-cover-image" style="background-image: url('{{cover_image}}')">
        <div class="cover-overlay"></div>
      </div>
    {{else}}
      <div class="card-cover-gradient" style="background: linear-gradient(135deg, {{background_color}} 0%, {{background_color}}e6 100%)">
        <div class="subtle-pattern"></div>
        <div class="cover-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
          </svg>
        </div>
      </div>
    {{/if}}

    <!-- 🎯 Strategic Status Badge -->
    <div class="status-badge-container">
      {{#if is_active}}
        <span class="status-badge status-live">
          <span class="status-indicator live-pulse"></span>
          <span class="status-text">Live</span>
        </span>
      {{else}}
        <span class="status-badge status-draft">
          <span class="status-indicator draft-dot"></span>
          <span class="status-text">Draft</span>
        </span>
      {{/if}}
    </div>

    <!-- ⚡ Quick Actions (Only on Hover) -->
    <div class="quick-actions-overlay">
      <button type="button" class="quick-action edit-action" title="Edit Drop" onclick="editDrop('{{id}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="m16 3 5 5L8 21H3v-5z"/>
        </svg>
      </button>
      <button type="button" class="quick-action stats-action" title="View Stats" onclick="viewDropStats('{{id}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M21.2 15.9A10 10 0 1 1 8 2.9M22 12A10 10 0 0 0 12 2v10z"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- 📝 Clean Content Section -->
  <div class="card-content-clean">
    <!-- 🎯 Title & Description -->
    <div class="content-header">
      <h3 class="card-title">{{title}}</h3>
      {{#if description}}
        <p class="card-description">{{description}}</p>
      {{/if}}
    </div>

    <!-- 📊 Clean Stats Grid -->
    <div class="stats-grid-clean">
      <div class="stat-item">
        <div class="stat-value">{{signup_count}}</div>
        <div class="stat-label">Signups</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{formatDate created_at "MMM d"}}</div>
        <div class="stat-label">Created</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{#if is_active}}Active{{else}}Draft{{/if}}</div>
        <div class="stat-label">Status</div>
      </div>
    </div>

    <!-- 🔗 Clean URL Display -->
    <div class="url-display-clean">
      <div class="url-container">
        <span class="url-domain">{{../domain}}/drop/</span>
        <span class="url-slug">{{slug}}</span>
      </div>
      <button type="button" class="copy-url-btn" title="Copy URL" onclick="copyDropUrl('{{../domain}}/drop/{{slug}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <rect width="13" height="13" x="9" y="9" rx="2" ry="2"/>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- 🎯 Clean Action Section -->
  <div class="card-actions-clean">
    <div class="primary-actions">
      <button type="button" class="action-btn primary edit-btn" title="Edit Drop" onclick="editDrop('{{id}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="m16 3 5 5L8 21H3v-5z"/>
        </svg>
        <span>Edit</span>
      </button>
      <button type="button" class="action-btn secondary stats-btn" title="View Stats" onclick="viewDropStats('{{id}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M21.2 15.9A10 10 0 1 1 8 2.9M22 12A10 10 0 0 0 12 2v10z"/>
        </svg>
        <span>Stats</span>
      </button>
    </div>
    <div class="secondary-actions">
      <button type="button" class="icon-btn qr-btn" title="QR Code" onclick="showDropQR('{{id}}', '{{../domain}}/drop/{{slug}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <rect width="5" height="5" x="3" y="3" rx="1"/>
          <rect width="5" height="5" x="16" y="3" rx="1"/>
          <rect width="5" height="5" x="3" y="16" rx="1"/>
          <path d="m21 16-3.5-3.5-1 1"/>
          <path d="m14 12 1 1 3.5-3.5"/>
          <path d="m10 16 1.5 1.5"/>
        </svg>
      </button>
      <button type="button" class="icon-btn delete-btn" title="Delete Drop" onclick="showDeleteDropModal('{{id}}', '{{title}}')">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m-6 5v6m4-6v6"/>
        </svg>
      </button>
    </div>
  </div>
</div>
