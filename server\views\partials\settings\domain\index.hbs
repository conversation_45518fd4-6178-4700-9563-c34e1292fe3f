<h2>
  Custom domain
</h2>
<p>
  You can set a custom domain for your short URLs, so instead of
  <b>{{default_domain}}/shorturl</b> you can have
  <b>yoursite.com/shorturl.</b>
</p>

{{#if server_cname_address}}
  <p>
    Point your domain's A record to 
    {{#if server_ip_address}}
      <b>{{server_ip_address}}</b>
    {{else}}
      our <b>IP address</b>
    {{/if}} or your subdomain's CNAME record to
    <b>{{server_cname_address}}</b>. If you're using <b>Cloudflare</b>,
    make sure to use <b>DNS only</b> mode for your subdomain.
  </p>
  <p>Then, add the domain via the form below:</p>
{{else}}
  <p>
    Point your domain's A record to 
    {{#if server_ip_address}}
      <b>{{server_ip_address}}</b>
    {{else}}
      our <b>IP address</b>
    {{/if}} 
    then add the domain via the form below:
  </p>
{{/if}}

{{> settings/domain/table}}
<div class="add-domain-wrapper">
  <button
    type="button"
    class="secondary show-domain-form"
    hx-indicator=".add-domain-wrapper"
    hx-get="/add-domain-form"
    hx-target="#domain-form-wrapper"
    hx-swap="innerHTML"
    hx-on::after-request="event.srcElement.classList.add('hidden')"
  >
    <span>{{> icons/plus}}</span>
    Add domain
  </button>
  {{> icons/spinner}}
  <div id="domain-form-wrapper">
  </div>
</div>
{{> settings/domain/dialog}}