
<html lang="en">
  <head>
    <title>Visit www.pixelrocket.store to learn how to become a frontend web developer</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="css/tailwind/tailwind.min.css"/>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon.png"/>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer="defer"></script>
  </head>
  <body class="antialiased bg-body text-body font-body">
    <div>
      <p class="py-4 bg-green-500 text-green-900 text-center">Want to learn how to build websites like this one? <a href="https://www.pixelrocket.store">Visit Pixel Rocket</a></p>
      <section x-data="{ mobileNavOpen: false }">
        <div class="container px-4 mx-auto">
          <div class="flex items-center justify-between pt-5 pb-2.5 -m-2">
            <div class="w-auto p-2">
              <div class="flex flex-wrap items-center">
                <div class="w-auto"><a class="relative z-10 inline-block" href="index.html"><img src="images/logo.svg" alt=""/></a></div>
              </div>
            </div>
            <div class="w-auto p-2">
              <div class="flex flex-wrap items-center">
                <div class="w-auto hidden lg:block">
                  <ul class="flex items-center mr-12">
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="about.html">About</a></li>
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="pricing.html">Pricing</a></li>
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="blog.html">Blog</a></li>
                    <li class="text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="contact.html">Contact</a></li>
                  </ul>
                </div>
                <div class="w-auto hidden lg:block">
                  <div class="inline-block"><a class="inline-block px-8 py-4 text-white hover:text-black tracking-tighter hover:bg-green-400 border-2 border-white focus:border-green-400 focus:border-opacity-40 hover:border-green-400 focus:ring-4 focus:ring-green-400 focus:ring-opacity-40 rounded-full transition duration-300" href="login.html">Login</a></div>
                </div>
                <div class="w-auto lg:hidden">
                  <button class="relative z-10 inline-block" x-on:click="mobileNavOpen = !mobileNavOpen">
                    <svg class="text-green-500" width="51" height="51" viewbox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="56" height="56" rx="28" fill="currentColor"></rect>
                      <path d="M37 32H19M37 24H19" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="hidden fixed top-0 left-0 bottom-0 w-4/6 sm:max-w-xs z-50" :class="{'block': mobileNavOpen, 'hidden': !mobileNavOpen}">
          <div class="fixed inset-0 bg-black opacity-60" x-on:click="mobileNavOpen = !mobileNavOpen"></div>
          <nav class="relative z-10 px-9 pt-8 h-full bg-black overflow-y-auto">
            <div class="flex flex-wrap justify-between h-full">
              <div class="w-full">
                <div class="flex items-center justify-between -m-2">
                  <div class="w-auto p-2"><a class="inline-block" href="#"><img src="images/logo.svg" alt=""/></a></div>
                  <div class="w-auto p-2">
                    <button class="inline-block text-white" x-on:click="mobileNavOpen = !mobileNavOpen">
                      <svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 18L18 6M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex flex-col justify-center py-16 w-full">
                <ul>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="about.html">About</a></li>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="pricing.html">Pricing</a></li>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="blog.html">Blog</a></li>
                  <li class="text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="contact.html">Contact</a></li>
                </ul>
              </div>
              <div class="flex flex-col justify-end w-full pb-8"><a class="inline-block px-8 py-4 text-center text-white hover:text-black tracking-tighter hover:bg-green-400 border-2 border-white focus:border-green-400 focus:border-opacity-40 hover:border-green-400 focus:ring-4 focus:ring-green-400 focus:ring-opacity-40 rounded-full transition duration-300" href="login.html">Login</a></div>
            </div>
          </nav>
        </div>
      </section>
      <section class="py-20 overflow-hidden">
        <div class="container px-4 mx-auto">
          <div class="md:max-w-xl text-center mx-auto mb-20"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Check our stats</span>
            <h2 class="font-heading text-7xl text-white tracking-tighter-xl">About us</h2>
          </div>
          <div class="max-w-5xl mx-auto">
            <div class="flex flex-wrap lg:items-center -m-8 lg:-m-12">
              <div class="w-full md:w-1/2 p-8 lg:p-12">
                <div class="max-w-max mx-auto"><img class="rounded-3xl" src="template-assets/images/abouts/about.png" alt=""/></div>
              </div>
              <div class="w-full md:w-1/2 p-8 lg:p-12"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Check our stats</span>
                <h2 class="mb-6 text-6xl md:text-7xl text-white tracking-tighter">Making credit history with banking</h2>
                <p class="mb-10 text-white text-opacity-60 md:max-w-xs">Global Bank is a strategic branding agency focused on brand creation, rebrands, and brand</p><a class="inline-block px-8 py-4 text-white hover:text-black font-medium tracking-tighter hover:bg-green-400 border-2 border-white focus:border-green-400 focus:border-opacity-40 hover:border-green-400 focus:ring-4 focus:ring-green-400 focus:ring-opacity-40 rounded-full transition duration-300" href="#">Read more</a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="py-16 overflow-hidden">
        <div class="container px-4 mx-auto">
          <div class="text-center"><span class="inline-block mb-8 text-sm text-green-400 font-medium tracking-tighter">Check ourstats</span>
            <div class="flex flex-wrap -m-11">
              <div class="w-full md:w-1/2 lg:w-1/4 p-11">
                <div class="text-center">
                  <h3 class="mb-1.5 text-7xl text-white tracking-4xl">$1.5B+</h3>
                  <p class="text-gray-300">Assets Under Managment</p>
                </div>
              </div>
              <div class="w-full md:w-1/2 lg:w-1/4 p-11">
                <div class="text-center">
                  <h3 class="mb-1.5 text-7xl text-white tracking-4xl">100+</h3>
                  <p class="text-gray-300">Assets Under Managment</p>
                </div>
              </div>
              <div class="w-full md:w-1/2 lg:w-1/4 p-11">
                <div class="text-center">
                  <h3 class="mb-1.5 text-7xl text-white tracking-4xl">150+</h3>
                  <p class="text-gray-300">Assets Under Managment</p>
                </div>
              </div>
              <div class="w-full md:w-1/2 lg:w-1/4 p-11">
                <div class="text-center">
                  <h3 class="mb-1.5 text-7xl text-white tracking-4xl">40+</h3>
                  <p class="text-gray-300">Assets Under Managment</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="py-20 overflow-hidden">
        <div class="container px-4 mx-auto">
          <div class="flex flex-wrap items-center -m-8">
            <div class="w-full md:w-1/2 p-8">
              <div class="md:max-w-md"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Check our stats</span>
                <h2 class="font-heading mb-8 text-6xl md:text-7xl text-white tracking-tighter-xl">Making credit history with nightcard</h2>
                <p class="mb-8 text-lg text-gray-300">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text.</p><a class="inline-block text-white hover:text-opacity-80 font-medium underline transition duration-500" href="#">Read more</a>
              </div>
            </div>
            <div class="w-full md:w-1/2 p-8">
              <div class="mx-auto max-w-lg md:mr-0">
                <div class="flex flex-wrap -m-4">
                  <div class="w-1/2 p-4">
                    <div class="flex flex-wrap">
                      <div class="mb-8 w-full"><img class="w-full" src="template-assets/images/abouts/img2.png" alt=""/></div>
                      <div class="w-full"><img class="w-full" src="template-assets/images/abouts/img4.png" alt=""/></div>
                    </div>
                  </div>
                  <div class="w-1/2 p-4">
                    <div class="flex flex-wrap mt-24">
                      <div class="mb-8 w-full"><img class="w-full" src="template-assets/images/abouts/img3.png" alt=""/></div>
                      <div class="w-full"><img class="w-full" src="template-assets/images/abouts/img.png" alt=""/></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="relative py-24 overflow-hidden">
        <div class="container px-4 mx-auto">
          <div class="mb-20 md:max-w-xl text-center mx-auto"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">our team</span>
            <h2 class="font-heading text-7xl lg:text-8xl text-white tracking-7xl lg:tracking-8xl">Our Team</h2>
          </div>
          <div class="flex flex-wrap -m-8">
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar1.png" alt=""/><span class="inline-block mb-2 text-gray-300">CEO at Global Bank</span>
                <h3 class="text-3xl text-white">Guy Hawkins</h3>
              </div>
            </div>
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar2.png" alt=""/><span class="inline-block mb-2 text-gray-300">CFO at Global Bank</span>
                <h3 class="text-3xl text-white">Brooklyn Simmons</h3>
              </div>
            </div>
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar3.png" alt=""/><span class="inline-block mb-2 text-gray-300">Head Marketer Global Bank</span>
                <h3 class="text-3xl text-white">Floyd Miles</h3>
              </div>
            </div>
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar4.png" alt=""/><span class="inline-block mb-2 text-gray-300">Support Lead Global Bank</span>
                <h3 class="text-3xl text-white">Jenny Wilson</h3>
              </div>
            </div>
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar5.png" alt=""/><span class="inline-block mb-2 text-gray-300">Design at Global Bank</span>
                <h3 class="text-3xl text-white">Esther Howard</h3>
              </div>
            </div>
            <div class="w-full md:w-1/2 lg:w-1/3 p-8">
              <div class="text-center"><img class="mb-9 mx-auto" src="template-assets/images/team/avatar6.png" alt=""/><span class="inline-block mb-2 text-gray-300">Engineer at Global Bank</span>
                <h3 class="text-3xl text-white">Leslie Alexander</h3>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="py-20 overflow-hidden" x-data="{ showContent: false, active: 'all' }">
        <div class="container px-4 mx-auto">
          <div class="md:max-w-4xl text-center mx-auto"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Join us</span>
            <h2 class="font-heading mb-12 text-7xl lg:text-8xl text-white tracking-7xl lg:tracking-8xl">Open positions</h2>
            <div class="flex flex-wrap justify-center mb-12">
              <div class="w-auto"><a class="inline-block py-5 px-8 text-green-400 font-medium border-2 border-green-400 rounded-full" x-on:click.prevent="active = 'all'" :class="{'text-green-400 border-green-400': active === 'all', 'text-gray-300 border-transparent': active !== 'all'}" href="#">All</a></div>
              <div class="w-auto"><a class="inline-block py-5 px-8 text-gray-300 border-2 border-transparent font-medium rounded-full" x-on:click.prevent="active = 'design'" :class="{'text-green-400 border-green-400': active === 'design', 'text-gray-300 border-transparent': active !== 'design'}" href="#">Design</a></div>
              <div class="w-auto"><a class="inline-block py-5 px-8 text-gray-300 border-2 border-transparent font-medium rounded-full" x-on:click.prevent="active = 'software-engineering'" :class="{'text-green-400 border-green-400': active === 'software-engineering', 'text-gray-300 border-transparent': active !== 'software-engineering'}" href="#">Software Engineering</a></div>
              <div class="w-auto"><a class="inline-block py-5 px-8 text-gray-300 border-2 border-transparent font-medium rounded-full" x-on:click.prevent="active = 'customer-success'" :class="{'text-green-400 border-green-400': active === 'customer-success', 'text-gray-300 border-transparent': active !== 'customer-success'}" href="#">Customer Success</a></div>
              <div class="w-auto"><a class="inline-block py-5 px-8 text-gray-300 border-2 border-transparent font-medium rounded-full" x-on:click.prevent="active = 'sales'" :class="{'text-green-400 border-green-400': active === 'sales', 'text-gray-300 border-transparent': active !== 'sales'}" href="#">Sales</a></div>
              <div class="w-auto"><a class="inline-block py-5 px-8 text-gray-300 border-2 border-transparent font-medium rounded-full" x-on:click.prevent="active = 'marketing'" :class="{'text-green-400 border-green-400': active === 'marketing', 'text-gray-300 border-transparent': active !== 'marketing'}" href="#">Marketing</a></div>
            </div>
          </div>
          <div class="w-full overflow-x-auto overflow-y-hidden mb-20">
            <div class="flex">
              <div class="flex-1">
                <div class="flex flex-wrap">
                  <div class="flex items-center w-full h-16 px-8 py-5 border-b border-gray-900">
                    <p class="min-w-max text-sm text-gray-300 font-medium">Position</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Product Manager, Instagram Camera</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'customer-success' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Research Director</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Customer Success Manager</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'software-engineering' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Engineering Manager</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Product Designer</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Product Manager, Instagram Camera</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Product Manager, Instagram Camera</a></div>
                  <div class="visibility-item flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="showContent &amp;&amp; active === 'customer-success' || showContent &amp;&amp; active === 'all'"><a class="mr-3.5 text-2xl min-w-max text-white tracking-2xl hover:underline" href="#">Research Director</a></div>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex flex-wrap">
                  <div class="flex items-center w-full h-16 px-8 py-6 border-b border-gray-900">
                    <p class="min-w-max text-sm text-gray-300 font-medium">Departement</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Marketing</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'customer-success' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Customer Success</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Marketing</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'software-engineering' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Software Engineering</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Design</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Design</a></div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Design</a></div>
                  <div class="visibility-item flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="showContent &amp;&amp; active === 'customer-success' || showContent &amp;&amp; active === 'all'"><a class="inline-block py-3 px-4 text-xs text-green-400 hover:text-black font-medium hover:bg-green-400 border-2 border-green-400 rounded-full transition duration-300" href="#">Customer success</a></div>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex flex-wrap">
                  <div class="flex items-center w-full h-16 px-8 py-6 border-b border-gray-900">
                    <p class="min-w-max text-sm text-gray-300 font-medium">Commitment</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'">
                    <p class="min-w-max text-white">Full Time</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'customer-success' || active === 'all'">
                    <p class="min-w-max text-white">Part Time</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'">
                    <p class="min-w-max text-white">Part Time</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'software-engineering' || active === 'all'">
                    <p class="min-w-max text-white">Freelance</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Full Time</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Full Time</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Full Time</p>
                  </div>
                  <div class="visibility-item flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="showContent &amp;&amp; active === 'customer-success' || showContent &amp;&amp; active === 'all'">
                    <p class="min-w-max text-white">Full Time</p>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex flex-wrap">
                  <div class="flex items-center w-full h-16 px-8 py-6 border-b border-gray-900">
                    <p class="min-w-max text-sm text-gray-300 font-medium">Location</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'customer-success' || active === 'all'">
                    <p class="min-w-max text-white">Miami</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'marketing' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'software-engineering' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="active === 'design' || active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                  <div class="visibility-item flex items-center w-full h-20 px-8 py-5 border-b border-gray-900" x-show="showContent &amp;&amp; active === 'customer-success' || showContent &amp;&amp; active === 'all'">
                    <p class="min-w-max text-white">Los Angeles</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-wrap justify-center max-w-xs mx-auto mb-6" x-show="!showContent">
            <div class="w-full"><a class="block px-14 py-4 text-center font-medium tracking-2xl border-2 border-green-400 bg-green-400 hover:bg-green-500 text-black focus:ring-4 focus:ring-green-500 focus:ring-opacity-40 rounded-full transition duration-300" x-on:click.prevent="showContent = true" href="#">See all positions</a></div>
          </div>
        </div>
      </section>
      <section class="py-12">
        <div class="container px-4 mx-auto">
          <div class="relative pt-20 px-4 bg-gray-900 bg-opacity-20 overflow-hidden rounded-6xl">
            <div class="text-center md:max-w-xl mx-auto removed pb-20"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Learn to code</span>
              <h2 class="font-heading mb-6 text-7xl text-white tracking-8xl">Want to build templates like this one?</h2><a class="mb-8 text-gray-300 relative z-10" href="https://www.pixelrocket.store">Visit www.pixelrocket.store and learn to become a frontend web developer today</a><img class="absolute -bottom-24 right-0 z-0" src="template-assets/images/application-section/lines2.png" alt=""/>
            </div>
          </div>
        </div>
      </section>
      <section class="bg-gray-50 overflow-hidden">
        <div class="py-14 bg-black rounded-b-7xl"></div>
        <div class="py-24">
          <div class="container px-4 mx-auto">
            <div class="flex flex-wrap justify-center -m-8 mb-28">
              <div class="w-full md:w-1/2 lg:w-4/12 p-8">
                <div class="md:max-w-xs"><img class="mb-7" src="images/logo-dark.svg" alt=""/>
                  <p class="text-gray-400 font-medium">Global Bank is a strategic branding agency focused on brand creation, rebrands, and brand</p>
                </div>
              </div>
              <div class="w-full md:w-1/2 lg:w-2/12 p-8">
                <h3 class="mb-6 text-lg text-black font-medium">About</h3>
                <ul>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Contact</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Blog</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Our Story</a></li>
                  <li><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Careers</a></li>
                </ul>
              </div>
              <div class="w-full md:w-1/2 lg:w-2/12 p-8">
                <h3 class="mb-6 text-lg text-black font-medium">Company</h3>
                <ul>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Contact</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Blog</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Our Story</a></li>
                  <li><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Careers</a></li>
                </ul>
              </div>
              <div class="w-full md:w-1/2 lg:flex-1 p-8">
                <div class="flex flex-wrap -m-2">
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/twitter.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on Twitter for updates</p>
                        </div>
                      </div></a></div>
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/instagram.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on Instagram for updates</p>
                        </div>
                      </div></a></div>
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/tiktok.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on TikTok for updates</p>
                        </div>
                      </div></a></div>
                </div>
              </div>
            </div>
            <div class="flex flex-wrap justify-between -m-2">
              <div class="w-auto p-2">
                <p class="inline-block text-sm font-medium text-black text-opacity-60">© 2023 Global Bank</p>
              </div>
              <div class="w-auto p-2">
                <div class="flex flex-wrap items-center -m-2 sm:-m-7">
                  <div class="w-auto p-2 sm:p-7"><a class="inline-block text-sm text-black text-opacity-60 hover:text-opacity-100 font-medium transition duration-300" href="#">Terms of Use</a></div>
                  <div class="w-auto p-2 sm:p-7"><a class="inline-block text-sm text-black text-opacity-60 hover:text-opacity-100 font-medium transition duration-300" href="#">Privacy Policy</a></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </body>
</html>