<tr id="tr-{{id}}" {{#if swap_oob}}hx-swap-oob="true"{{/if}}>
  <td class="domains-id">
    {{id}}
  </td>
  <td class="domains-address right-fade">
    {{address}}
    <p class="description">
      by&nbsp;
      {{~#if user_id~}}
        <a 
          aria-label="View user" 
          data-tooltip="View user" 
          hx-get="/api/users/admin"
          hx-target="closest table"
          hx-swap="outerHTML" 
          hx-sync="this:replace"
          hx-indicator="closest table"
          hx-vals='{"search":"{{email}}"}'
          onclick="setTab(event, 'tab-links')"
        >
          {{email}}
        </a>
        {{#ifEquals @root.query.user email}}
        {{else}}
          &nbsp;(
          <a 
            aria-label="View domains" 
            data-tooltip="View domains" 
            hx-get="/api/domains/admin"
            hx-target="closest table"
            hx-swap="outerHTML" 
            hx-sync="this:replace"
            hx-indicator="closest table"
            hx-vals='{"user":"{{email}}"}'
          >
            view domains
          </a>)
        {{/ifEquals}}
      {{~else~}}
        <a 
          aria-label="View system domains" 
          data-tooltip="View system domains" 
          hx-get="/api/domains/admin"
          hx-target="closest table"
          hx-swap="outerHTML" 
          hx-sync="this:replace"
          hx-indicator="closest table"
          hx-vals='{"owner":"false"}'
        >
          System
        </a>
      {{~/if~}}
      &nbsp;{{~#if description~}}· {{description}}{{~/if}}
    </p>
  </td>
  <td class="domains-homepage right-fade">
    {{#if homepage}}
      <a href="{{homepage}}" target="_blank" rel="noopener noreferrer">
        {{homepage}}
      </a>
    {{else}}
      No homepage
    {{/if}}
  </td>
  <td class="domains-created-at">
    {{relative_created_at}}
  </td>
  <td class="domains-links-count">
    {{#ifEquals links_count '0'}}
      {{links_count}}
    {{else}}
      <a
        data-tooltip="View links"
        aria-label="View links"
        hx-get="/api/links/admin"
        hx-target="closest table"
        hx-swap="outerHTML" 
        hx-sync="this:replace"
        hx-vals='{"domain":"{{address}}"}'
        hx-indicator="closest table"
        onclick="setTab(event, 'tab-links')"
      >
        {{links_count}}
      </a>
    {{/ifEquals}}
  </td>
  {{> admin/domains/actions}}
</tr>
<tr class="edit">
  <td class="loading">
    {{> icons/spinner}}
  </td>
</tr>