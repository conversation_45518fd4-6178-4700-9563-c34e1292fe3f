!function(){"use strict";var t,e,r,n,o,i,a,c,u,s={},l={};function f(t){var e=l[t];if(void 0!==e)return e.exports;var r=l[t]={exports:{}};return s[t](r,r.exports,f),r.exports}f.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),f.rv=function(){return"1.0.14"},f.ruid="bundler=rspack@1.0.14";(t=a||(a={})).WINDOWS_PHONE="Windows Phone",t.ANDROID="android",t.IOS="ios",t.PC="pc",(e=c||(c={})).MUSICAL_LY="musical_ly",e.MUSICALLY_GO="musically_go",e.TRILL="trill",e.ULTRALITE="ultralite",e.LEMON8="lemon8",(i={})[c.LEMON8]={},i[c.MUSICAL_LY]=((n={})[a.IOS]="33.4.0",n[a.ANDROID]="23.1.0",n),i[c.TRILL]=((o={})[a.IOS]="33.4.0",o[a.ANDROID]="23.1.0",o);var p=function(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==f.g?f.g:Function("return this")()},h=function(){return p().TiktokAnalyticsObject||"ttq"},y=function(){var t=p();return 0,t[h()]},_=function(t){try{var e=y()._plugins||{};if(null!=e[t])return!!e[t];return!0}catch(t){return!0}};function d(){d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(e,r,n,i){var a=Object.create((r&&r.prototype instanceof v?r:v).prototype);return o(a,"_invoke",{value:function(e,r,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=function e(r,n){var o=n.method,i=r.iterator[o];if(i===t)return n.delegate=null,"throw"===o&&r.iterator.return&&(n.method="return",n.arg=t,e(r,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=f(i,r.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,_;var c=a.arg;return c?c.done?(n[r.resultName]=c.value,n.next=r.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,_):c:(n.method="throw",n.arg=TypeError("iterator result is not an object"),n.delegate=null,_)}(c,n);if(u){if(u===_)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:"suspendedYield",s.arg===_)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}(e,n,new S(i||[]))}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="executing",y="completed",_={};function v(){}function g(){}function b(){}var m={};s(m,a,function(){return this});var w=Object.getPrototypeOf,O=w&&w(w(j([])));O&&O!==r&&n.call(O,a)&&(m=O);var E=b.prototype=v.prototype=Object.create(m);function I(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function T(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(r,a){!function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then(function(t){r("next",t,a,c)},function(t){r("throw",t,a,c)}):e.resolve(l).then(function(t){s.value=t,a(s)},function(t){return r("throw",t,a,c)})}c(u.arg)}(o,i,r,a)})}return r=r?r.then(a,a):a()}})}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},I(T.prototype),s(T.prototype,c,function(){return this}),e.AsyncIterator=T,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new T(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},I(E),s(E,u,"Generator"),s(E,a,function(){return this}),s(E,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(L),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),_}},e}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function g(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(g=function(){return!!t})()}var b={error:[]};(r=u||(u={})).LOAD_START="load_start",r.LOAD_END="load_end",r.BEFORE_INIT="before_init",r.INIT_START="init_start",r.INIT_END="init_end",r.JSB_INIT_START="jsb_init_start",r.JSB_INIT_END="jsb_init_end",r.BEFORE_AD_INFO_INIT_START="before_ad_info_init_start",r.AD_INFO_INIT_START="ad_info_init_start",r.AD_INFO_INIT_END="ad_info_init_end",r.IDENTIFY_INIT_START="identify_init_start",r.IDENTIFY_INIT_END="identify_init_end",r.PLUGIN_INIT_START="_init_start",r.PLUGIN_INIT_END="_init_end",r.PIXEL_SEND="pixel_send",r.PIXEL_SEND_PCM="pixel_send_PCM",r.JSB_SEND="jsb_send",r.HTTP_SEND="http_send",r.HANDLE_CACHE="handle_cache",r.INIT_ERROR="init_error",r.PIXEL_EMPTY="pixel_empty",r.JSB_ERROR="jsb_error",r.API_ERROR="api_error",r.PLUGIN_ERROR="plugin_error",r.CUSTOM_INFO="custom_info",r.CUSTOM_ERROR="custom_error",r.CUSTOM_TIMER="custom_timer";try{(function(){if(/function bind\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Function.prototype.bind.toString()))return!0;function t(){}return new(t.bind.apply(t,[void 0,1])) instanceof t})()&&!Function.prototype._ttq_bind?Object.defineProperty(Function.prototype,"_ttq_bind",{value:Function.prototype.bind,enumerable:!1,writable:!1,configurable:!1}):Function.prototype._ttq_bind||Object.defineProperty(Function.prototype,"_ttq_bind",{value:function(t){if("function"!=typeof this)throw TypeError("What is being called by bind is not a function.");var e=t||window,r=Array.prototype.slice.call(arguments).slice(1),n=Symbol("key");return e[n]=this,function t(){return this instanceof t?function(t,e,r){if(g())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,e);var o=new(t.bind.apply(t,n));return r&&v(o,r.prototype),o}(e[n],r.concat(Array.prototype.slice.call(arguments))):e[n].apply(e,r.concat(Array.prototype.slice.call(arguments)))}},enumerable:!1,writable:!1,configurable:!1}),!function(){if(!Object._ttq_keys)Object._ttq_keys=function(t){try{if(Array.isArray(t))return Object.keys(t).filter(function(t){return -1===["each","eachSlice","all","any","collect","detect","findAll","grep","include","inGroupsOf","inject","invoke","max","min","partition","pluck","reject","sortBy","toArray","zip","size","inspect","select","member","_reverse","_each","clear","first","last","compact","flatten","without","uniq","intersect","clone","toJSON","remove","swap","putAll"].indexOf(t)});return Object.keys(t)}catch(e){return Object.keys(t)}}}(),!function(){var t=h();function e(t){if(null===t)return"NULL";if(void 0===t)return"UNDEFINED";if("[object Object]"===Object.prototype.toString.call(t)||"[object Array]"===Object.prototype.toString.call(t))return JSON.stringify(t);return t.toString()}/function Map\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Map.toString())?window[t]._ttq_map=Map:!window[t]._ttq_map&&(window[t]._ttq_map=function(){this.items={},this.size=0},window[t]._ttq_map.prototype.set=function(t,r){return!this.has(t)&&(this.items[e(t)]=r,this.size++),this},window[t]._ttq_map.prototype.get=function(t){return this.items[e(t)]},window[t]._ttq_map.prototype.has=function(t){return void 0!==this.items[e(t)]},window[t]._ttq_map.prototype.delete=function(t){return this.has(t)&&(delete this.items[e(t)],this.size--),this},window[t]._ttq_map.prototype.clear=function(){this.items={},this.size=0},window[t]._ttq_map.prototype.keys=function(){var t=d().mark(n),e=[];for(var r in this.items)this.has(r)&&e.push(r);function n(){return d().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(e,"t0",1);case 1:case"end":return t.stop()}},t)}return n()},window[t]._ttq_map.prototype.values=function(){var t=d().mark(n),e=[];for(var r in this.items)this.has(r)&&e.push(this.items[r]);function n(){return d().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(e,"t0",1);case 1:case"end":return t.stop()}},t)}return n()})}(),!function(){if(/function create\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Map.toString())){Object._ttq_create=Object.create;return}Object._ttq_create=function(){function t(){}var e=Object.prototype.hasOwnProperty;return function(r,n){if("object"!=typeof r&&"function"!=typeof r)throw TypeError("Object prototype may only be an Object or null");t.prototype=r;var o=new t;return t.prototype=null,null!=n&&Object.keys(n).forEach(function(t){var r=n[t];if("object"==typeof r&&null!==r)e.call(r,"value")?o[t]=r.value:("function"==typeof r.get||"function"==typeof r.set)&&Object.defineProperty(o,t,r);else throw TypeError("Property description must be an object: "+r)}),o}}()}()}catch(t){!function(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n=!1);try{var o=y(),i=o.getPlugin&&o.getPlugin("Monitor")||null;i&&i.error&&"function"==typeof i.error?i.error.call(i,t,e,r,n):_("Monitor")&&b.error.push({event:t,err:e,detail:r,withoutJSB:n})}catch(t){}}(u.INIT_ERROR,t)}}();!function(){var t={515:function(t){function e(t){if(t)return function(t){for(var n in e.prototype)t[n]=e.prototype[n];return t}(t)}t.exports=e;e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+t];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var o=0;o<r.length;o++)if((n=r[o])===e||n.fn===e){r.splice(o,1);break}return 0===r.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),n=this._callbacks["$"+t],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,o=n.length;r<o;++r)n[r].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length}},42:function(){!function(){if("undefined"!=typeof window)try{var t=new window.CustomEvent("test",{cancelable:!0});if(t.preventDefault(),!0!==t.defaultPrevented)throw Error("Could not prevent default")}catch(t){var e=function(t,e){var n,r;return(e=e||{}).bubbles=!!e.bubbles,e.cancelable=!!e.cancelable,(n=document.createEvent("CustomEvent")).initCustomEvent(t,e.bubbles,e.cancelable,e.detail),r=n.preventDefault,n.preventDefault=function(){r.call(this);try{Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}catch(t){this.defaultPrevented=!0}},n};e.prototype=window.Event.prototype,window.CustomEvent=e}}()},505:function(){!function(){"use strict";if("object"==typeof window){if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype){!("isIntersecting"in window.IntersectionObserverEntry.prototype)&&Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});return}var t=window.document,e=[];r.prototype.THROTTLE_TIMEOUT=100,r.prototype.POLL_INTERVAL=null,r.prototype.USE_MUTATION_OBSERVER=!0,r.prototype.observe=function(t){if(!this._observationTargets.some(function(e){return e.element==t})){if(!(t&&1==t.nodeType))throw Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(),this._checkForIntersections()}},r.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter(function(e){return e.element!=t}),!this._observationTargets.length&&(this._unmonitorIntersections(),this._unregisterInstance())},r.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorIntersections(),this._unregisterInstance()},r.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},r.prototype._initThresholds=function(t){var e=t||[0];return!Array.isArray(e)&&(e=[e]),e.sort().filter(function(t,e,n){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw Error("threshold must be a number between 0 and 1 inclusively");return t!==n[e-1]})},r.prototype._parseRootMargin=function(t){var e=(t||"0px").split(/\s+/).map(function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2]}});return e[1]=e[1]||e[0],e[2]=e[2]||e[0],e[3]=e[3]||e[1],e},r.prototype._monitorIntersections=function(){!this._monitoringIntersections&&(this._monitoringIntersections=!0,this.POLL_INTERVAL?this._monitoringInterval=setInterval(this._checkForIntersections,this.POLL_INTERVAL):(o(window,"resize",this._checkForIntersections,!0),o(t,"scroll",this._checkForIntersections,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in window&&(this._domObserver=new MutationObserver(this._checkForIntersections),this._domObserver.observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0}))))},r.prototype._unmonitorIntersections=function(){this._monitoringIntersections&&(this._monitoringIntersections=!1,clearInterval(this._monitoringInterval),this._monitoringInterval=null,i(window,"resize",this._checkForIntersections,!0),i(t,"scroll",this._checkForIntersections,!0),this._domObserver&&(this._domObserver.disconnect(),this._domObserver=null))},r.prototype._checkForIntersections=function(){var t=this._rootIsInDom(),e=t?this._getRootRect():c();this._observationTargets.forEach(function(r){var o=r.element,i=a(o),c=this._rootContainsTarget(o),s=r.entry,u=t&&c&&this._computeTargetAndRootIntersection(o,e),f=r.entry=new n({time:function(){return window.performance&&performance.now&&performance.now()}(),target:o,boundingClientRect:i,rootBounds:e,intersectionRect:u});s?t&&c?this._hasCrossedThreshold(s,f)&&this._queuedEntries.push(f):s&&s.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)},this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)},r.prototype._computeTargetAndRootIntersection=function(e,n){if("none"!=window.getComputedStyle(e).display){for(var r=a(e),o=u(e),i=!1;!i;){var c=null,s=1==o.nodeType?window.getComputedStyle(o):{};if("none"==s.display)return;if(o==this.root||o==t?(i=!0,c=n):o!=t.body&&o!=t.documentElement&&"visible"!=s.overflow&&(c=a(o)),c&&!(r=function(t,e){var n=Math.max(t.top,e.top),r=Math.min(t.bottom,e.bottom),o=Math.max(t.left,e.left),i=Math.min(t.right,e.right),a=i-o,c=r-n;return a>=0&&c>=0&&{top:n,bottom:r,left:o,right:i,width:a,height:c}}(c,r)))break;o=u(o)}return r}},r.prototype._getRootRect=function(){var e;if(this.root)e=a(this.root);else{var n=t.documentElement,r=t.body;e={top:0,left:0,right:n.clientWidth||r.clientWidth,width:n.clientWidth||r.clientWidth,bottom:n.clientHeight||r.clientHeight,height:n.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(e)},r.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map(function(e,n){return"px"==e.unit?e.value:e.value*(n%2?t.width:t.height)/100}),n={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},r.prototype._hasCrossedThreshold=function(t,e){var n=t&&t.isIntersecting?t.intersectionRatio||0:-1,r=e.isIntersecting?e.intersectionRatio||0:-1;if(n!==r)for(var o=0;o<this.thresholds.length;o++){var i=this.thresholds[o];if(i==n||i==r||i<n!=i<r)return!0}},r.prototype._rootIsInDom=function(){return!this.root||s(t,this.root)},r.prototype._rootContainsTarget=function(e){return s(this.root||t,e)},r.prototype._registerInstance=function(){0>e.indexOf(this)&&e.push(this)},r.prototype._unregisterInstance=function(){var t=e.indexOf(this);-1!=t&&e.splice(t,1)};window.IntersectionObserver=r,window.IntersectionObserverEntry=n}function n(t){this.time=t.time,this.target=t.target,this.rootBounds=t.rootBounds,this.boundingClientRect=t.boundingClientRect,this.intersectionRect=t.intersectionRect||c(),this.isIntersecting=!!t.intersectionRect;var e=this.boundingClientRect,n=e.width*e.height,r=this.intersectionRect,o=r.width*r.height;n?this.intersectionRatio=Number((o/n).toFixed(4)):this.intersectionRatio=this.isIntersecting?1:0}function r(t,e){var n=e||{};if("function"!=typeof t)throw Error("callback must be a function");if(n.root&&1!=n.root.nodeType)throw Error("root must be an Element");this._checkForIntersections=function(t,e){var n=null;return function(){!n&&(n=setTimeout(function(){t(),n=null},e))}}(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map(function(t){return t.value+t.unit}).join(" ")}function o(t,e,n,r){"function"==typeof t.addEventListener?t.addEventListener(e,n,r||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,n)}function i(t,e,n,r){"function"==typeof t.removeEventListener?t.removeEventListener(e,n,r||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,n)}function a(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?(!(e.width&&e.height)&&(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):c()}function c(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function s(t,e){for(var n=e;n;){if(n==t)return!0;n=u(n)}return!1}function u(t){var e=t.parentNode;return e&&11==e.nodeType&&e.host?e.host:e&&e.assignedSlot?e.assignedSlot.parentNode:e}}()},278:function(t,e,n){t=n.nmd(t);var r,o="__lodash_hash_undefined__",i="[object Arguments]",a="[object Boolean]",c="[object Date]",s="[object Function]",u="[object GeneratorFunction]",f="[object Map]",l="[object Number]",p="[object Object]",h="[object Promise]",d="[object RegExp]",_="[object Set]",v="[object String]",g="[object Symbol]",m="[object WeakMap]",y="[object ArrayBuffer]",E="[object DataView]",w="[object Float32Array]",b="[object Float64Array]",O="[object Int8Array]",I="[object Int16Array]",T="[object Int32Array]",S="[object Uint8Array]",N="[object Uint8ClampedArray]",R="[object Uint16Array]",P="[object Uint32Array]",A=/\w*$/,L=/^\[object .+?Constructor\]$/,C=/^(?:0|[1-9]\d*)$/,x={};x[i]=x["[object Array]"]=x[y]=x[E]=x[a]=x[c]=x[w]=x[b]=x[O]=x[I]=x[T]=x[f]=x[l]=x[p]=x[d]=x[_]=x[v]=x[g]=x[S]=x[N]=x[R]=x[P]=!0,x["[object Error]"]=x[s]=x[m]=!1;var k="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,D="object"==typeof self&&self&&self.Object===Object&&self,M=k||D||Function("return this")(),j=e&&!e.nodeType&&e,U=j&&t&&!t.nodeType&&t,q=U&&U.exports===j;function F(t,e){return t.set(e[0],e[1]),t}function V(t,e){return t.add(e),t}function G(t,e,n,r){var o=-1,i=t?t.length:0;for(r&&i&&(n=t[++o]);++o<i;)n=e(n,t[o],o,t);return n}function H(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function B(t){var e=-1,n=Array(t.size);return t.forEach(function(t,r){n[++e]=[r,t]}),n}function K(t,e){return function(n){return t(e(n))}}function W(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}var Y=Array.prototype,J=Function.prototype,X=Object.prototype,Z=M["__core-js_shared__"];var Q=(r=/[^.]+$/.exec(Z&&Z.keys&&Z.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"",z=J.toString,$=X.hasOwnProperty,tt=X.toString,te=RegExp("^"+z.call($).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),tn=q?M.Buffer:void 0,tr=M.Symbol,to=M.Uint8Array,ti=K(Object.getPrototypeOf,Object),ta=Object.create,tc=X.propertyIsEnumerable,ts=Y.splice,tu=Object.getOwnPropertySymbols,tf=tn?tn.isBuffer:void 0,tl=K(Object.keys,Object),tp=tk(M,"DataView"),th=tk(M,"Map"),td=tk(M,"Promise"),t_=tk(M,"Set"),tv=tk(M,"WeakMap"),tg=tk(Object,"create"),tm=tU(tp),ty=tU(th),tE=tU(td),tw=tU(t_),tb=tU(tv),tO=tr?tr.prototype:void 0,tI=tO?tO.valueOf:void 0;function tT(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function tS(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}tT.prototype.clear=function(){this.__data__=tg?tg(null):{}},tT.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},tT.prototype.get=function(t){var e=this.__data__;if(tg){var n=e[t];return n===o?void 0:n}return $.call(e,t)?e[t]:void 0},tT.prototype.has=function(t){var e=this.__data__;return tg?void 0!==e[t]:$.call(e,t)},tT.prototype.set=function(t,e){return this.__data__[t]=tg&&void 0===e?o:e,this};function tN(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}tS.prototype.clear=function(){this.__data__=[]},tS.prototype.delete=function(t){var e=this.__data__,n=tA(e,t);return!(n<0)&&(n==e.length-1?e.pop():ts.call(e,n,1),!0)},tS.prototype.get=function(t){var e=this.__data__,n=tA(e,t);return n<0?void 0:e[n][1]},tS.prototype.has=function(t){return tA(this.__data__,t)>-1},tS.prototype.set=function(t,e){var n=this.__data__,r=tA(n,t);return r<0?n.push([t,e]):n[r][1]=e,this};function tR(t){this.__data__=new tS(t)}tN.prototype.clear=function(){this.__data__={hash:new tT,map:new(th||tS),string:new tT}},tN.prototype.delete=function(t){return tx(this,t).delete(t)},tN.prototype.get=function(t){return tx(this,t).get(t)},tN.prototype.has=function(t){return tx(this,t).has(t)},tN.prototype.set=function(t,e){return tx(this,t).set(t,e),this};tR.prototype.clear=function(){this.__data__=new tS},tR.prototype.delete=function(t){return this.__data__.delete(t)},tR.prototype.get=function(t){return this.__data__.get(t)},tR.prototype.has=function(t){return this.__data__.has(t)},tR.prototype.set=function(t,e){var n=this.__data__;if(n instanceof tS){var r=n.__data__;if(!th||r.length<199)return r.push([t,e]),this;n=this.__data__=new tN(r)}return n.set(t,e),this};function tP(t,e,n){var r=t[e];(!($.call(t,e)&&tq(r,n))||void 0===n&&!(e in t))&&(t[e]=n)}function tA(t,e){for(var n=t.length;n--;)if(tq(t[n][0],e))return n;return -1}function tL(t){var e=new t.constructor(t.byteLength);return new to(e).set(new to(t)),e}function tC(t,e,n,r){n||(n={});for(var o=-1,i=e.length;++o<i;){var a=e[o],c=r?r(n[a],t[a],a,n,t):void 0;tP(n,a,void 0===c?t[a]:c)}return n}function tx(t,e){var n=t.__data__;return function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}(e)?n["string"==typeof e?"string":"hash"]:n.map}function tk(t,e){var n,r,o,i=(n=t,r=e,null==n?void 0:n[r]);return!(!tB(o=i)||function(t){return!!Q&&Q in t}(o))&&(tH(o)||H(o)?te:L).test(tU(o))?i:void 0}var tD=tu?K(tu,Object):function(){return[]},tM=function(t){return tt.call(t)};(tp&&tM(new tp(new ArrayBuffer(1)))!=E||th&&tM(new th)!=f||td&&tM(td.resolve())!=h||t_&&tM(new t_)!=_||tv&&tM(new tv)!=m)&&(tM=function(t){var e=tt.call(t),n=e==p?t.constructor:void 0,r=n?tU(n):void 0;if(r)switch(r){case tm:return E;case ty:return f;case tE:return h;case tw:return _;case tb:return m}return e});function tj(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||X)}function tU(t){if(null!=t){try{return z.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tq(t,e){return t===e||t!=t&&e!=e}var tF=Array.isArray;function tV(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}(t.length)&&!tH(t)}var tG=tf||function(){return!1};function tH(t){var e=tB(t)?tt.call(t):"";return e==s||e==u}function tB(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function tK(t){return tV(t)?function(t,e){var n=tF(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&tV(t)}(t)&&$.call(t,"callee")&&(!tc.call(t,"callee")||tt.call(t)==i)}(t)?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],r=n.length,o=!!r;for(var a in t)$.call(t,a)&&!(o&&("length"==a||function(t,e){return!!(e=null==e?0x1fffffffffffff:e)&&("number"==typeof t||C.test(t))&&t>-1&&t%1==0&&t<e}(a,r)))&&n.push(a);return n}(t):function(t){if(!tj(t))return tl(t);var e=[];for(var n in Object(t))$.call(t,n)&&"constructor"!=n&&e.push(n);return e}(t)}t.exports=function(t){return function t(e,n,r,o,h,m,L){if(o&&(C=m?o(e,h,m,L):o(e)),void 0!==C)return C;if(!tB(e))return e;var C,k=tF(e);if(k){if(C=function(t){var e=t.length,n=t.constructor(e);return e&&"string"==typeof t[0]&&$.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(e),!n)return function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}(e,C)}else{var D,M,j=tM(e),U=j==s||j==u;if(tG(e))return function(t,e){if(e)return t.slice();var n=new t.constructor(t.length);return t.copy(n),n}(e,n);if(j==p||j==i||U&&!m){if(H(e))return m?e:{};if(C=function(t){var e;return"function"!=typeof t.constructor||tj(t)?{}:tB(e=ti(t))?ta(e):{}}(U?{}:e),!n){;return function(t,e){return tC(t,tD(t),e)}(e,(D=C,M=e,D&&tC(M,tK(M),D)))}}else{if(!x[j])return m?e:{};C=function(t,e,n,r){var o,i,s,u,p,h,m,L,C,x,k,D,M,j=t.constructor;switch(e){case y:return tL(t);case a:case c:return new j(+t);case E:;return o=t,i=r?tL(o.buffer):o.buffer,new o.constructor(i,o.byteOffset,o.byteLength);case w:case b:case O:case I:case T:case S:case N:case R:case P:;return s=t,u=r?tL(s.buffer):s.buffer,new s.constructor(u,s.byteOffset,s.length);case f:;return p=t,h=r,m=n,G(h?m(B(p),!0):B(p),F,new p.constructor);case l:case v:return new j(t);case d:;return(C=new(L=t).constructor(L.source,A.exec(L))).lastIndex=L.lastIndex,C;case _:;return x=t,k=r,D=n,G(k?D(W(x),!0):W(x),V,new x.constructor);case g:;return M=t,tI?Object(tI.call(M)):{}}}(e,j,t,n)}}L||(L=new tR);var q=L.get(e);if(q)return q;if(L.set(e,C),!k)var K=r?function(t){var e,n,r,o;return e=t,n=tK,r=tD,o=n(e),tF(e)?o:function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}(o,r(e))}(e):tK(e);return!function(t,e){for(var n=-1,r=t?t.length:0;++n<r&&!1!==e(t[n],n,t););;}(K||e,function(i,a){K&&(i=e[a=i]),tP(C,a,t(i,n,r,o,a,e,L))}),C}(t,!0,!0)}},616:function(){},229:function(t,e,n){"use strict";n.d(e,{AU:function(){return O},Bk:function(){return s},Bp:function(){return b},Cd:function(){return d},Jn:function(){return p},Kc:function(){return _},M0:function(){return y},MQ:function(){return C},Mm:function(){return A},Ns:function(){return S},OW:function(){return N},QN:function(){return R},Qu:function(){return m},RC:function(){return k},WO:function(){return E},Yp:function(){return x},aj:function(){return v},bO:function(){return D},bk:function(){return l},ct:function(){return g},dm:function(){return h},iG:function(){return f},ku:function(){return T},lV:function(){return P},n2:function(){return L},qC:function(){return w},qf:function(){return I},vu:function(){return u}});var r,o,i,a,c,s,u,f="TiktokAnalyticsObject",l="ttq",p="2.2.1",h="tt_adInfo",d="tt_appInfo",_="insight_log",v="insight_log_monitor",g="_tt_enable_cookie",m="sessionId",y="pageId",E="messageId",w="_ttp",b="tt_sessionId",O="tt_pixel_session_index",I="default_eventId",T="ttcsid",S="https://analytics.tiktok.com/i18n/pixel/config.js",N="https://analytics.tiktok.com/i18n/pixel/enable_cookie",R="https://analytics.tiktok.com/i18n/pixel/disable_cookie",P="https://analytics.tiktok.com/i18n/pixel/static/identify_3255c1c6.js",A="analytics.tiktok.com",L="::";(r=s||(s={})).WINDOWS_PHONE="Windows Phone",r.ANDROID="android",r.IOS="ios",r.PC="pc",(o=u||(u={})).MUSICAL_LY="musical_ly",o.MUSICALLY_GO="musically_go",o.TRILL="trill",o.ULTRALITE="ultralite",o.LEMON8="lemon8";var C=((c={})[u.LEMON8]={},c[u.MUSICAL_LY]=((i={})[s.IOS]="33.4.0",i[s.ANDROID]="23.1.0",i),c[u.TRILL]=((a={})[s.IOS]="33.4.0",a[s.ANDROID]="23.1.0",a),c),x={expires:390},k="ttoclid",D={"www.agoda.com":"tag"}},408:function(t,e,n){"use strict";var r,o;n.d(e,{S:function(){return r}}),(o=r||(r={}))[o.OFFSITE=0]="OFFSITE",o[o.ONSITE=1]="ONSITE"},74:function(t,e,n){"use strict";n.d(e,{M:function(){return i},V:function(){return o}}),(r=o||(o={})).EXTERNAL="external",r.APP="app",r.TIKTOK="tiktok";var r,o,i={ID:Symbol.for("ID"),Type:Symbol.for("type"),Partner:Symbol.for("partner"),Options:Symbol.for("Options"),Plugins:Symbol.for("Plugins"),Rules:Symbol.for("Rules"),Info:Symbol.for("Info"),ExtraParams:Symbol.for("extraParams"),WebLibraryInfo:Symbol.for("WebLibraryInfo"),SignalType:Symbol.for("SignalType"),IsOnsitePage:Symbol.for("IsOnsitePage")}},825:function(t,e,n){"use strict";var r,o;n.d(e,{O:function(){return r}}),(o=r||(r={})).LOAD_START="load_start",o.LOAD_END="load_end",o.BEFORE_INIT="before_init",o.INIT_START="init_start",o.INIT_END="init_end",o.JSB_INIT_START="jsb_init_start",o.JSB_INIT_END="jsb_init_end",o.BEFORE_AD_INFO_INIT_START="before_ad_info_init_start",o.AD_INFO_INIT_START="ad_info_init_start",o.AD_INFO_INIT_END="ad_info_init_end",o.IDENTIFY_INIT_START="identify_init_start",o.IDENTIFY_INIT_END="identify_init_end",o.PLUGIN_INIT_START="_init_start",o.PLUGIN_INIT_END="_init_end",o.PIXEL_SEND="pixel_send",o.PIXEL_SEND_PCM="pixel_send_PCM",o.JSB_SEND="jsb_send",o.HTTP_SEND="http_send",o.HANDLE_CACHE="handle_cache",o.INIT_ERROR="init_error",o.PIXEL_EMPTY="pixel_empty",o.JSB_ERROR="jsb_error",o.API_ERROR="api_error",o.PLUGIN_ERROR="plugin_error",o.CUSTOM_INFO="custom_info",o.CUSTOM_ERROR="custom_error",o.CUSTOM_TIMER="custom_timer"},118:function(t,e,n){"use strict";n.d(e,{Ae:function(){return r},Sk:function(){return i},X2:function(){return o}});var r=["phone_number","email","external_id"],o={EMAIL_IS_HASHED:"email_is_hashed",PHONE_IS_HASHED:"phone_is_hashed",SHA256_EMAIL:"sha256_email",SHA256_PHONE:"sha256_phone"},i="auto_trigger_type"},729:function(t,e,n){"use strict";n.d(e,{N4:function(){return y},Ts:function(){return h},Vp:function(){return p},_R:function(){return m},hZ:function(){return l}}),(o=l||(l={})).EMPTY_VALUE="empty_value",o.WRONG_FORMAT="wrong_format",o.CORRECT_FORMAT="correct_format",o.HASHED="hashed",o.HASHED_ERR="hashed_err",o.HASHED_CORRECT="hashed_correct",o.PLAINTEXT_EMAIL="plaintext_email",o.PLAINTEXT_PHONE="plaintext_phone",(i=p||(p={})).EMPTY_VALUE="empty_value",i.PLAIN_EMAIL="plain_email",i.PLAIN_PHONE="plain_phone",i.HASHED="hashed",i.FILTER_EVENTS="filter_events",i.UNKNOWN_INVALID="unknown_invalid",i.BASE64_STRING_HASHED="base64_string_hashed",i.BASE64_HEX_HASHED="base64_hex_hashed",i.PLAIN_MDN_EMAIL="plain_mdn_email",i.ZIP_CODE_IS_NOT_HASHED="zip_code_is_not_hashed",i.ZIP_CODE_IS_NOT_US="zip_code_is_not_us",i.ZIP_CODE_IS_HASHED="zip_code_is_hashed",i.ZIP_CODE_IS_US="zip_code_is_us",(a=h||(h={})).Manual="manual",a.ManualV2="manual_v2",a.Auto="auto",(c=d||(d={})).empty="empty",c.whitespace="whitespace",c.hardcode="hardcode",c.encode="encode",(s=_||(_={})).letterCase="letter_case",s.isNotValidEmail="is_not_valid_email",s.isNotPossibleEmail="is_not_possible_email",s.domainTypo="domain_typo",s.addressFormat="address_format",(u=v||(v={})).invalidCountry="invalid_country",u.notANumber="not_a_number",u.tooShort="too_short",u.tooLong="too_long",u.invalidLength="invalid_length",u.emptyCountryCodeThroughIP="empty_country_code_through_ip",u.invalidCountryAfterInjectPlus="invalid_country_after_inject_plus",u.notANumberAfterInjectPlus="not_a_number_after_inject_plus",u.tooShortAfterInjectPlus="too_short_after_inject_plus",u.tooLongAfterInjectPlus="too_long_after_inject_plus",u.invalidLengthAfterInjectPlus="invalid_length_after_inject_plus",u.invalidCountryAfterInjectCountry="invalid_country_after_inject_country",u.notANumberAfterInjectCountry="not_a_number_after_inject_country",u.tooShortAfterInjectCountry="too_short_after_inject_country",u.tooLongAfterInjectCountry="too_long_after_inject_country",u.invalidLengthAfterInjectCountry="invalid_length_after_inject_country",(f=g||(g={})).missing="missing",f.valid="valid",f.invalid="invalid";var r,o,i,a,c,s,u,f,l,p,h,d,_,v,g,m,y={raw_email:{label:g.missing},raw_auto_email:{label:g.missing},raw_phone:{label:g.missing},raw_auto_phone:{label:g.missing},hashed_email:{label:g.missing},hashed_phone:{label:g.missing}};(r=m||(m={}))[r.UNKNOWN=0]="UNKNOWN",r[r.HOLD=1]="HOLD",r[r.REVOKE=2]="REVOKE",r[r.GRANT=3]="GRANT"},188:function(t,e,n){"use strict";n.d(e,{HD:function(){return r},Kn:function(){return i},P2:function(){return f},Qr:function(){return a},eZ:function(){return l},fV:function(){return c},gj:function(){return s},hj:function(){return o},pW:function(){return u}});var r=function(t){return"string"==typeof t},o=function(t){return"number"==typeof t},i=function(t){return"[object Object]"===Object.prototype.toString.call(t)},a=function(t){return"{}"===JSON.stringify(t)},c=function(t){return t+"-"+Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12)},s=function(t,e,n){return void 0===n&&(n="-"),""+t+n+e},u=function(){return new Date(Date.now()+864e5).toUTCString()};function f(t,e){void 0===e&&(e=500);var n=-1;return function(){var r=Array.prototype.slice.apply(arguments);Date.now()-n>=e&&(t.apply(void 0,r),n=Date.now())}}var l=function(t){return/^(0|([1-9]\d*))$/.test(""+t)}},790:function(t,e,n){"use strict";n.d(e,{xD:function(){return T},r8:function(){return E},gn:function(){return y},z6:function(){return M},XA:function(){return _},bp:function(){return O},z1:function(){return D},zP:function(){return C},Z0:function(){return d},zd:function(){return w},NC:function(){return b},Fn:function(){return I},Tm:function(){return R},Xf:function(){return v},b4:function(){return h},sH:function(){return g},PO:function(){return P},RA:function(){return p},Dt:function(){return m}});var r,o,i,a,c=n("332");(r=i||(i={}))[r.NOT_SURE=0]="NOT_SURE",r[r.INVOKE_METHOD_ENABLED=1]="INVOKE_METHOD_ENABLED",r[r.INVOKE_METHOD_NOT_ENABLED=2]="INVOKE_METHOD_NOT_ENABLED",(o=a||(a={})).NORMAL="1",o.NOT_CROSS_DOMAIN_IFRAME="2",o.CROSS_DOMAIN_IFRAME="3",o.WEB_WORKER="4",o.SANDBOX_IFRAME="5",o.GTM_IFRAME="6",o.URL_IN_QUERY_IFRAME="7",o.UNKNOWN_IFRAME="8";var s=n("319"),u=n("74"),f=n("408"),l=n("229"),p=function(){var t,e;return(null===(e=null===(t=(0,c.Ie)())||void 0===t?void 0:t._env)||void 0===e?void 0:e.env)||u.V.EXTERNAL},h=function(){var t,e;return null!==(e=null===(t=(0,c.Ie)())||void 0===t?void 0:t._is_onsite)&&void 0!==e?e:f.S.OFFSITE},d=function(t){return(t||p())!==u.V.EXTERNAL},_=function(t){return(t||p())===u.V.TIKTOK},v=function(){var t=(0,c.ij)();return/windows phone/i.test(t)?l.Bk.WINDOWS_PHONE:/android/i.test(t)?l.Bk.ANDROID:/iPad|iPhone|iPod/.test(t)?l.Bk.IOS:l.Bk.PC},g=function(){try{return navigator.userAgentData.getHighEntropyValues(["model","platformVersion"])}catch(t){return Promise.resolve({})}},m=function(){return"android"===v()},y=function(){return"ios"===v()},E=function(){return!1},w=function(){return window.top!==window},b=(0,s.IH)(function(){return/open_news/i.test((0,c.ij)())}),O=(0,s.IH)(function(){return/ultralite/i.test((0,c.ij)())});function I(){var t;try{return[i.INVOKE_METHOD_ENABLED,i.INVOKE_METHOD_NOT_ENABLED][[!!(null===(t=null==window?void 0:window.ToutiaoJSBridge)||void 0===t?void 0:t.invokeMethod),!0].findIndex(function(t){return t})]}catch(t){return i.NOT_SURE}}var T=function(){try{return window&&window.top&&window.top.location.href,!1}catch(t){return!0}},S=function(){try{var t=new URL(decodeURIComponent(window.location.href)),e=/https?:\/\/[^\s/$.?#].[^\s]*/i;return e.test(t.search)||e.test(t.pathname)}catch(t){return!1}},N=function(){try{if(!T())return a.NOT_CROSS_DOMAIN_IFRAME;if(S())return a.URL_IN_QUERY_IFRAME;if(window.google_tag_manager)return a.GTM_IFRAME;if(window.name&&"web-pixel-sandbox"===window.name)return a.SANDBOX_IFRAME;return a.CROSS_DOMAIN_IFRAME}catch(t){return a.UNKNOWN_IFRAME}},R=function(){return(0,c.n2)()?a.WEB_WORKER:w()?N():a.NORMAL},P=function(){var t=(0,c.ij)();if(!!t)for(var e=0,n=Object.values(l.vu);e<n.length;e++){var r=n[e];if(t.includes(r))return r}},A=function(){var t=(0,c.ij)();if(!!t){var e=t.match(/\bapp_version\/(\S*)/),n=e&&e[1]?e[1].match(/^\d+\.\d+\.\d+$/):void 0;return n?n[0]:void 0}},L=function(){var t=(0,c.ij)();if(!!t)for(var e=Object.keys(l.vu),n=0;n<e.length;n++){var r=e[n],o=RegExp("\\b"+l.vu[r]+"_(\\S*)"),i=t.match(o),a=i&&i[1]?i[1].match(/^\d+\.\d+\.\d+$/):void 0;if(a)return a[0]}},C=function(){var t=P(),e=v(),n=y()?L():m()?A():null;if(!n||!t||!l.MQ[t])return!1;var r=l.MQ[t];return!r[e]||x(r[e],n)},x=function(t,e){for(var n=t.split("."),r=e.split("."),o=0;o<Math.max(n.length,r.length);o++){var i=parseInt(n[o])||Number.MAX_VALUE,a=parseInt(r[o])||-1;if(i<a)break;if(i>a)return!1}return!0},k=function(t){var e=P();return void 0!==e&&t.has(e)},D=function(){return k(new Set([l.vu.MUSICAL_LY,l.vu.TRILL,l.vu.ULTRALITE]))},M=function(){var t=(0,c.E9)();try{return!!(t.Shopify&&t.Shopify.shop&&t.Shopify.shop.indexOf(".myshopify.com")>0||t.name&&t.name.startsWith("web-pixel-sandbox-CUSTOM-shopify"))}catch(t){}return!1}},332:function(t,e,n){"use strict";n.d(e,{E9:function(){return a},FH:function(){return h},Gp:function(){return c},Ie:function(){return s},My:function(){return p},Wf:function(){return v},c6:function(){return g},cH:function(){return f},gM:function(){return i},ij:function(){return l},kW:function(){return d},n2:function(){return u},ri:function(){return _}});var r=n(229),o=null,i=function(t){o=t},a=function(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==n.g?n.g:Function("return this")()},c=function(){return a()[r.iG]||r.bk},s=function(){var t=a();return o||t[c()]},u=function(){var t=a();return void 0!==t.DedicatedWorkerGlobalScope?t instanceof t.DedicatedWorkerGlobalScope:"DedicatedWorkerGlobalScope"===t.constructor.name},f=function(){return!!s()._is_onsite},l=function(){var t=a();return("object"==typeof navigator&&navigator.userAgent?navigator.userAgent:"")||t._userAgent},p=function(t){try{var e=s();return e&&e._self_host_config&&e._self_host_config[t]||""}catch(t){return""}},h=function(t,e){void 0===e&&(e=[]);try{return e.includes(t)}catch(t){return!1}},d=function(t){var e=s(),n=e._i||{},r=t&&n[t];return t&&r&&r._partner?r._partner:e._partner?e._partner:""},_=function(t){var e=s(),n=e._i||{};return Object.keys(n).filter(function(e){return n[e]._partner===t}).length>0||e._partner===t},v=function(t){try{var e=s()._plugins||{};if(null!=e[t])return!!e[t];return!0}catch(t){return!0}},g=function(){var t;try{var e=s()._ppf;return null===(t=e.printAndClear)||void 0===t?void 0:t.call(e)}catch(t){}}},783:function(t,e,n){"use strict";n.d(e,{ZK:function(){return c},qe:function(){return o},um:function(){return i},vU:function(){return a},wE:function(){return s}});var r=n(332),o={info:[],error:[]};function i(t,e,n){void 0===e&&(e={}),void 0===n&&(n=!1);try{var i=(0,r.Ie)(),a=i.getPlugin&&i.getPlugin("Monitor")||null;a&&a.info&&"function"==typeof a.info?a.info.call(a,t,e,n):(0,r.Wf)("Monitor")&&o.info.push({event:t,detail:e,withoutJSB:n})}catch(t){}}function a(t,e,n,i){void 0===n&&(n={}),void 0===i&&(i=!1);try{var a=(0,r.Ie)(),c=a.getPlugin&&a.getPlugin("Monitor")||null;c&&c.error&&"function"==typeof c.error?c.error.call(c,t,e,n,i):(0,r.Wf)("Monitor")&&o.error.push({event:t,err:e,detail:n,withoutJSB:i})}catch(t){}}function c(t,e){try{var n=(0,r.Ie)(),o=n.getPlugin&&n.getPlugin("DiagnosticsConsole")||null;o&&o.warn.apply(o,[t,e])}catch(t){}}function s(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"getPixelDetail",start:performance.now()}}catch(t){}var e={lib:"ttq",pixelCode:"MOCK_SHOP_ID"};try{var n=document&&document.currentScript,o=n&&n.getAttribute("data-id")||"",i=(0,r.Gp)()||"ttq";e={pixelCode:o,lib:i}}catch(t){e={lib:"ttq",pixelCode:""}}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}return e}},319:function(t,e,n){"use strict";n.d(e,{$j:function(){return I},LT:function(){return T},X1:function(){return A},vO:function(){return L},eD:function(){return N},hi:function(){return R},xF:function(){return K},iK:function(){return V},iE:function(){return y},eh:function(){return G},Pm:function(){return b},Zq:function(){return E},WZ:function(){return m},IH:function(){return O},ZV:function(){return g},_v:function(){return j},bJ:function(){return X},TX:function(){return P},Xu:function(){return w},oh:function(){return H},Ds:function(){return M},cm:function(){return D},QO:function(){return x},te:function(){return C},P6:function(){return F},zh:function(){return q},mQ:function(){return k},d$:function(){return S}});function r(t){var e=Error(t);return e.source="ulid",e}var o,i="0123456789ABCDEFGHJKMNPQRSTVWXYZ",a=i.length,c=0xffffffffffff;var s=(!o&&(o=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments[1];!e&&(e="undefined"!=typeof window?window:null);var o=e&&(e.crypto||e.msCrypto);if(o)return function(){var t=new Uint8Array(1);return o.getRandomValues(t),t[0]/255};try{var i=n(616);return function(){return i.randomBytes(1).readUInt8()/255}}catch(t){}if(t)return function(){return Math.random()};throw r("secure crypto unusable, insecure Math.random not allowed")}()),function(t){return isNaN(t)&&(t=Date.now()),function(t,e){if(isNaN(t))throw Error(t+" must be a number");if(t>c)throw r("cannot encode time greater than "+c);if(t<0)throw r("time must be positive");if(!1===Number.isInteger(t))throw r("time must be an integer");for(var n=void 0,o="";e>0;e--)n=t%a,o=i.charAt(n)+o,t=(t-n)/a;return o}(t,10)+function(t,e){for(var n="";t>0;t--)n=function(t){var e=Math.floor(t()*a);return e===a&&(e=a-1),i.charAt(e)}(e)+n;return n}(16,o)}),u=n("729"),f=n("229"),l=n("118"),p=n("188"),h=n("790");function d(){d=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof g?n:g).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===_){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),v;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,v)}(c,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=_,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?_:"suspendedYield",u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=_,r.method="throw",r.arg=u.arg)}}}(e,r,new R(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",_="completed",v={};function g(){}function m(){}function y(){}var E={};u(E,a,function(){return this});var w=Object.getPrototypeOf,b=w&&w(w(P([])));b&&b!==n&&r.call(b,a)&&(E=b);var O=y.prototype=g.prototype=Object._ttq_create(E);function I(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function T(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function R(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function P(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return m.prototype=y,o(O,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:m,configurable:!0}),m.displayName=u(y,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(O),t},e.awrap=function(t){return{__await:t}},I(T.prototype),u(T.prototype,c,function(){return this}),e.AsyncIterator=T,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new T(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},I(O),u(O,s,"Generator"),u(O,a,function(){return this}),u(O,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),N(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:P(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function _(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}var v=function(t){return t},g=function(t){},m=function(t){return!!t},y=function(t){return void 0!==t.metric_name},E=function(t){return"CompletePayment"===t||"Purchase"===t},w=function(t){return y(t)?f.aj:f.Kc},b=function(t){var e;return Object.keys((null===(e=null==t?void 0:t.context)||void 0===e?void 0:e.user)||{}).some(function(t){return -1!==l.Ae.indexOf(t)})};function O(t,e){var n,r=t;return function(){if(r){for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];n=t.apply(e,i),r=null}return n}}var I=function(t){var e;return void 0===(e=t)&&(e=21),crypto.getRandomValues(new Uint8Array(e)).reduce(function(t,e){return(e&=63)<36?t+=e.toString(36):e<62?t+=(e-26).toString(36).toUpperCase():e>62?t+="-":t+="_",t},"")},T=function(t){return t?""+t+f.n2+I(20):(0,p.fV)("sessionId")},S=function(){for(var t=s(Date.now());27!==t.length;)t.length>27?t=t.slice(0,27):t+="_";return t},N=function(t){return t?t.split(f.n2)[0]:""},R=function(t,e){return((0,h.r8)()?"/static/config.js":f.Ns)+"?sdkid="+t+"&hostname="+e},P=function(){return(0,h.r8)()?"/static/enable_cookie.js":f.OW},A=function(){return(0,h.r8)()?"/static/disable_cookie.js":f.QN},L=function(){return(0,h.r8)()?"/static/identify.js":f.lV},C=function(t,e){if(0===Object.keys(t).length)return{};var n={identity_params:{}},r={email:["email_is_hashed","sha256_email"],phone_number:["phone_is_hashed","sha256_phone"],zip_code:["zip_code"]};return Object.entries(e).forEach(function(e){var o=e[0];e[1]&&r[o]&&r[o].forEach(function(e){if(n.identity_params[e]=[u.hZ.EMPTY_VALUE],t[e]){var r=t[e]||[u.hZ.EMPTY_VALUE];n.identity_params&&(n.identity_params[e]=[].concat(r))}})}),n},x=function(t,e){var n={identity_params:{}};return 0===Object.keys(t).length?{}:(Object.entries(e).forEach(function(e){var r=e[0];if(e[1]){if(t[r]&&t[r].length){var o=t[r]||[u.hZ.EMPTY_VALUE];n.identity_params[r]=[].concat(o)}else n.identity_params[r]=[u.hZ.EMPTY_VALUE]}}),n)};function k(t,e){var n=Object.assign({},t);return e.forEach(function(t){null!==n[t]&&void 0!==n[t]&&delete n[t]}),n}var D=function(t,e){if(!t)return{};var n={};return Object.keys(t).forEach(function(r){e[r]&&(n[r]=t[r])}),n};function M(t,e,n){var r;return function(){for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(r),r=setTimeout(function(){t.apply(n,i)},e)}}function j(t){return U.apply(this,arguments)}function U(){var t;return t=d().mark(function t(e){return d().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===e&&(e=500),t.abrupt("return",new Promise(function(t){setTimeout(function(){t(!0)},e)}));case 2:case"end":return t.stop()}},t)}),(U=function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){_(i,r,o,a,c,"next",t)}function c(t){_(i,r,o,a,c,"throw",t)}a(void 0)})}).apply(this,arguments)}var q=["input[type='button']","input[type='image']","input[type='submit']","button","[class*=btn]","[class*=Btn]","[class*=button]","[class*=Button]","[role*=button]","[id*=btn]","[id*=Btn]","[id*=button]","[id*=Button]","a"],F=["[href^='tel:']","[href^='callto:']","[href^='sms:']","[href^='skype:']","[href^='whatsapp:']","[href^='mailto:']"],V=function(t){var e=q.some(function(e){return t.matches(e)}),n=F.some(function(e){return t.matches(e)});return e&&!n};function G(t,e){return Object.keys(function t(e,n){var r={};for(var o in e)if(e.hasOwnProperty(o)&&!n.hasOwnProperty(o))r[o]=e[o];else if(e.hasOwnProperty(o)&&n.hasOwnProperty(o)&&e[o]!==n[o]){if("object"==typeof e[o]&&"object"==typeof n[o]){var i=t(e[o],n[o]);Object.keys(i).length>0&&(r[o]=i)}else r[o]=e[o]}for(var a in n)n.hasOwnProperty(a)&&!e.hasOwnProperty(a)&&(r[a]=n[a]);return r}(t,e)).length>0}function H(t,e){var n={};return t&&((0,p.HD)(t)||(0,p.hj)(t)?n.external_id=t.toString():(0,p.Kn)(t)&&(n=t)),e&&(0,p.Kn)(e)&&Object.assign(n,e),n}var B=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e,n={taskName:window.ttq._pf_tn,functionName:"getPixelScriptByPixelCode",start:performance.now()}}catch(t){}for(var r=Array.prototype.slice.call(document.getElementsByTagName("script")),o=0;o<r.length;o++){var i=r[o];if(i.innerHTML&&i.innerHTML.indexOf(t)>-1){e=i;break}}try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}return e},K=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"getPixelInstalledPosition",start:performance.now()}}catch(t){}var n="unknown";try{var r=t&&B(t);r&&(W(r)&&(n="isInHead"),J(r)&&(n="isInBodyTop10"))}catch(t){}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}return n},W=function t(e){var n=e.parentElement;return!!n&&("HEAD"===n.tagName||t(n))},Y=function(t,e){for(var n,r=[document.body],o=0;o<=t&&r.length;){var i=r.pop();if(i===e)return!0;if((null==i?void 0:i.tagName.toLowerCase())==="script"&&(null===(n=i.src)||void 0===n?void 0:n.indexOf(f.Mm))>-1)continue;if(o++,"object"==typeof i&&!!i.children)for(var a=i.children.length-1;a>=0;a--)r.push(i.children[a])}return!1},J=function(t){return Y(10,t)},X=function(t){return function(){setTimeout(t)}}},666:function(t,e,n){"use strict";n.d(e,{Z:function(){return tg}}),n("42"),n("505");var r,o,i,a,c,s,u,f,l,p,h=n("319"),d=n("783"),_=n("825"),v=["","webkit","Moz","MS","ms","o"],g=window,m=void 0!==function(t,e){for(var n,r,o=e[0].toUpperCase()+e.slice(1),i=0;i<v.length;){if((r=(n=v[i])?n+o:e)in t)return t[r];i++}}(g,"PointerEvent"),y="ontouchstart"in g;(r=s||(s={}))[r.Default=0]="Default",r[r.Start=1]="Start",r[r.Move=2]="Move",r[r.End=4]="End",r[r.Cancle=8]="Cancle";var E={pointer:{events:["pointerdown","pointermove","pointerup","pointercancel"],handler:function(t){var e=t.type,n={status:s.Default,timestamp:Date.now(),position:[t.clientX,t.clientY]};return e===this.events[0]&&(0===t.button||"touch"===t.pointerType)?n.status=s.Start:e===this.events[1]?n.status=s.Move:e===this.events[2]?n.status=s.End:e===this.events[3]&&(n.status=s.Cancle),n}},touch:{events:["touchstart","touchmove","touchend","touchcancel"],handler:function(t){var e=t.type;if(1!==t.changedTouches.length)return null;var n={status:s.Default,timestamp:Date.now(),position:[t.changedTouches[0].clientX,t.changedTouches[0].clientY]};return e===this.events[0]?n.status=s.Start:e===this.events[1]?n.status=s.Move:e===this.events[2]?n.status=s.End:e===this.events[3]&&(n.status=s.Cancle),n.status===s.Default?null:n}},mouse:{events:["mousedown","mousemove","mouseup"],handler:function(t){var e=t.type,n={status:s.Default,timestamp:Date.now(),position:[t.clientX,t.clientY]};return e===this.events[0]&&0===t.button?n.status=s.Start:e===this.events[1]?n.status=s.Move:e===this.events[2]&&(n.status=s.End),n.status&s.Move&&1!==t.which&&(n.status=s.End),n.status===s.Default?null:n}}};"MSPointerEvent"in g&&!("PointerEvent"in g)&&(E.pointer.events=["MSPointerDown","MSPointerMove","MSPointerUp","MSPointerCancel"]);var w={time:250,threshold:9};function b(t,e,n){for(var r=0;r<t.length;r++)document.addEventListener(t[r],e,n)}var O=function(t,e){var n=function(e){var n,r,o,i;return function(i){var a=E[e].handler(i);if(null!==a){if(a.status&s.Start){n=s.Start,r=a.timestamp,o=a.position,i.target;return}if(a.status&s.End)n&s.Start&&a.timestamp-r<w.time&&Math.sqrt(Math.pow(a.position[0]-o[0],2)+Math.pow(a.position[1]-o[1],2))<w.threshold&&t(i);else if(a.status&s.Move&&n&s.Start)return}n=0,r=0,o=[0,0]}};m?b(E.pointer.events,n("pointer"),e):y?b(E.touch.events,n("touch"),e):b(E.mouse.events,n("mouse"),e)};(o=u||(u={})).V1="v1",o.V2="v2",(i=f||(f={})).ELEMENT_V2="ELEMENT_V2",i.IMG_SRC="IMG_SRC",i.TOKENIZE_TEXT="TOKENIZE_TEXT",i.PAGE_URL_V2="PAGE_URL_V2";var I=function(t,e,n){var r=document.querySelectorAll(e);for(var o in r)if(n){if(Object.is(o,t))return!0}else if(!Object.is(o,t))return!0;return!1},T=function(t){var e=document.createRange(),n=document.body?document.body:document.head;e.selectNode(n);var r=e.createContextualFragment(t);n.appendChild(r)},S=function(){var t={},e=new Promise(function(e,n){t.resolve=e,t.reject=n});return t.promise=e,t},N=function(t,e,n){var r=S();return new IntersectionObserver(function(t){t.forEach(function(t){if(t.isIntersecting){var o={result:t.isIntersecting,curValue:e,condition:n};r.resolve(o)}})},{root:null,rootMargin:"0px",threshold:.5}).observe(t),r.promise};function R(t,e){var n=history[t],r=t+"-"+e;return function(){n.apply(history,arguments);var t=new CustomEvent(r,{detail:arguments});window.dispatchEvent(t)}}var P=function(t){history.pushState=R("pushState",t),history.replaceState=R("replaceState",t)},A=function(t,e){var n=t.getComputedStyle(e);return!("none"===n.display||"visible"!==n.visibility||.1>Number(n.opacity))&&!0},L=function(t){var e=t;if("string"==typeof t)try{e=decodeURI(t)}catch(n){e=t}return e},C=function(t,e){try{var n=new URL(t);return n.searchParams.delete(e),n.toString()}catch(e){return t}},x='"pixelMethod":"standard"',k=function(t,e){try{var n=t.split(x),r="";if(e&&(r+=',"is_button":"true"'),r)return n[0]+x+r+n[1];return t}catch(e){return t}},D=function(t){try{var e=t.split(x);return e[0]+x+',"is_standard_mode":"1"'+e[1]}catch(e){return t}},M=/[\-!$><-==&_\/\?\.,0-9:; \]\[%~\"\{\}\)\(\+\@\^\`]/g,j=/((([a-z])(?=[A-Z]))|(([A-Z])(?=[A-Z][a-z])))/g,U=/\s+/g,q={TOKENIZE_TEXT:"rule_compute_tokenize_text_error",IMG_SRC:"rule_compute_img_src_error",ELEMENT_V2:"rule_compute_element_v2_xpath_error"},F=function(t){var e;if(null===t)return null;return(null===(e=t.innerText)||void 0===e?void 0:e.length)>0?t.innerText.replace(M," ").replace(j,function(t){return t+" "}).replace(U," ").toLowerCase().trim():null},V=function(t){var e,n;if("IMG"===t.tagName)return t.getAttribute("src")||"";if(window.getComputedStyle){var r=window.getComputedStyle(t).getPropertyValue("background-image");if(null!==r&&"none"!==r&&r.length>0)return r}return"INPUT"===t.tagName&&t.getAttribute("src")||(null===(n=null===(e=t.getElementsByTagName("img"))||void 0===e?void 0:e[0])||void 0===n?void 0:n.getAttribute("src"))||null},G=function(t,e){var n;return(null===(n=F(t))||void 0===n?void 0:n.toLowerCase())===e||!1},H=function(t,e){return V(t)===e||!1},B=function(t,e){var n;return(null===(n=null==t?void 0:t.matches)||void 0===n?!!void 0:!!n.call(t,e))||!1},K=function(t,e,n,r){var o=!1,i=null,a=null;switch(e){case"TOKENIZE_TEXT":a=G;break;case"IMG_SRC":a=H;break;case"ELEMENT_V2":a=B}for(var c=0;c<5&&!["HTML","BODY"].includes(null==t?void 0:t.tagName);c++){;if((null==t?void 0:t.matches("input[type='button'], input[type='image'], input[type='submit'], button, [class*=btn], [class*=Btn], [class*=button], [class*=Button], [role*=button], [href^='tel: '], [href^='callto: '], [href^='mailto: '], [href^='sms: '], [href^='skype: '], [href^='whatsapp: '], [id*=btn], [id*=Btn], [id*=button], [id*=Button], a"))&&(null==a?void 0:a(t,n))){o=!0,i=t;break}t=t.parentElement}return r?i:o};!String.prototype.startsWith&&Object.defineProperty(String.prototype,"startsWith",{value:function(t,e){return e=!e||e<0?0:+e,this.substring(e,e+t.length)===t}}),!String.prototype.endsWith&&(String.prototype.endsWith=function(t,e){return(void 0===e||e>this.length)&&(e=this.length),this.substring(e-t.length,e)===t});var W=function(t,e,n,r){switch(e){case"EQUALS":if([f.TOKENIZE_TEXT,f.IMG_SRC,f.ELEMENT_V2].includes(r))try{return K(t,r,n,!1)}catch(t){(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"eb_jelly_error",custom_enum:q[r]});break}else if("ELEMENT"==r)try{for(var o=document.querySelectorAll(n),i=Array.prototype.slice.call(o),a=0;a<i.length;a++)if(i[a].contains(t))return!0}catch(t){(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"eb_jelly_error",custom_enum:"rule_compute_element_xpath_error"});break}if(n.split(";").filter(function(e){return t==e}).length>0)return!0;break;case"LT":if(t<n)return!0;break;case"GT":if(t>n)return!0;break;case"LT_OR_EQUAL":if(t<=n)return!0;break;case"GT_OR_EQUAL":if(t>=n)return!0;break;case"CONTAINS":if(n.split(";").filter(function(e){return(null==e?void 0:e.length)>0&&t.indexOf(e)>-1}).length>0)return!0;break;case"DOES_NOT_EQUAL":if(0==n.split(";").filter(function(e){return t==e}).length)return!0;break;case"DOES_NOT_CONTAIN":if(-1==t.indexOf(n))return!0;break;case"STARTS_WITH":if(t.startsWith(n))return!0;break;case"ENDS_WITH":if(t.endsWith(n))return!0;break;case"MATCHES_REGEX":if(n.test(t))return!0;break;case"MATCHES_REGEX_IGNORE_CASE":if(!n.test(t))return!0;break;case"MATCHES_CSS_SELECTOR":if(I(t,n,!0))return!0;break;case"DOSE_NOT_MATCHES_CSS_SELECTOR":if(I(t,n,!1))return!0}return!1};function Y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var J={click:["ELEMENT","TOKENIZE_TEXT","IMG_SRC","ELEMENT_V2","ELEMENT_CLASSES","ELEMENT_ID","ELEMENT_TARGET","ElEMENT_URL","ELEMENT_TEXT"],pageview:["PAGE_URL","PAGE_URL_V2","PAGE_HOSTNAME","PAGE_PATH","REFERRER"],visibility:["ELEMENT","ELEMENT_CLASSES","ELEMENT_ID"],history_change:["NEW_HISTORY_FRAGMENT","OLD_HISTORY_FRAGMENT","NEW_HISTORY_STATE","OLD_HISTORY_STATE","HISTORY_SOURCE"]},X="ttclid",Z=function(){function t(){}var e=t.prototype;return e.dispatcher=function(t,e,n,r,o){void 0===o&&(o=document);var i=e.variable_type;a="visibility"==t?["pageview","history_change","visibility"]:["pageview","history_change","click"];for(var a,c,s=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Y(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(t,e):void 0}}(t))){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(a);!(c=s()).done;){var u=c.value;if(J[u].indexOf(i)>-1){var f=void 0;switch(u){case"click":f=this.click(i,n);break;case"pageview":f=this.pageview(i);break;case"history_change":f=this.history_change(i,n,r);break;case"visibility":f=this.visibility(i,e.value,o)}return f}}},e.click=function(t,e){var n;if(!e)return!1;switch(t){case"ELEMENT":case"ELEMENT_V2":case"TOKENIZE_TEXT":case"IMG_SRC":case"ELEMENT_TARGET":n=e.target;break;case"ELEMENT_CLASSES":n=e.target.className;break;case"ELEMENT_ID":n=e.target.id;break;case"ElEMENT_URL":n=e.target.href||e.target.src||"";break;case"ELEMENT_TEXT":n=e.target.text||"";break;default:n=null}return n},e.pageview=function(t){var e;switch(t){case"PAGE_URL":case"PAGE_URL_V2":e=C(location.href,X);break;case"PAGE_HOSTNAME":e=location.hostname;break;case"PAGE_PATH":e=location.pathname;break;case"REFERRER":e=C(document.referrer,X);break;default:e=null}return e},e.history_change=function(t,e,n){var r;switch(t){case"NEW_HISTORY_FRAGMENT":r=location.hash;break;case"OLD_HISTORY_FRAGMENT":r=n.old_hash;break;case"NEW_HISTORY_STATE":r=history.state;break;case"OLD_HISTORY_STATE":r=n.old_state;break;case"HISTORY_SOURCE":r=e.type;break;default:r=null}return r},e.visibility=function(t,e,n){var r;switch(void 0===n&&(n=document),t){case"ELEMENT_ID":r="#"+e;break;case"ELEMENT_CLASS":r="."+e;break;case"ELEMENT":r=e;break;default:r=null}return n.querySelector(r)},t}(),Q=n("515"),z=n.n(Q);function $(t,e){return($=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tt=function(t){var e,n;function r(e,n){var r;return(r=t.call(this)||this).BaseConf=e,r.SDK_ID=n,r.BaseConf.forEach(function(t){t.id=t.code_id,t.conditions=t.conditions||[],t.conditions.forEach(function(t){t.result=!1})}),r}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,$(e,n),r.prototype.sendDebugEvent=function(t,e,n){var r=this.BaseConf,o=[];r.forEach(function(t){t.code_id==e&&(t.conditions=n),o.push(t)});var i={sdk_id:this.SDK_ID,event_name:t,data:o};this.emit("jelly_message",i)},r}(z());(a=l||(l={}))[a.WRONG=-1]="WRONG",a[a.KEEP=0]="KEEP",a[a.ARRAY=-2]="ARRAY",a[a.TURNINTOINTEGER=1]="TURNINTOINTEGER",a[a.TURNINTODECIMAL=2]="TURNINTODECIMAL",(c=p||(p={}))[c.CLICK_EVENT=0]="CLICK_EVENT",c[c.DESTINATION_URL=1]="DESTINATION_URL";var te=function(t){var e,n={};try{if(t.currency&&(n.currency=t.currency),t.value){document.querySelectorAll(t.value).length;var r=document.querySelector(t.value);(null==r?void 0:r.innerHTML)&&(n.ori_value=r.innerHTML,n.value=tn(null===(e=r.innerHTML)||void 0===e?void 0:e.trim(),t.value_index,t.value_parsing_method))}if(t.contents&&void 0!==t.contents[0].content_type&&(1===t.contents[0].content_type&&(n.content_type="product"),2===t.contents[0].content_type&&(n.content_type="product_group")),t.contents&&t.contents[0].content_name){var o=document.querySelector(t.contents[0].content_name);n.content_name=null==o?void 0:o.innerHTML}if(t.contents&&t.contents[0].content_id){if(t.contents[0].content_from===p.CLICK_EVENT){var i=document.querySelector(t.contents[0].content_id);n.content_id=null==i?void 0:i.innerHTML}else if(t.contents[0].content_from===p.DESTINATION_URL){var a=new URL(location.href);if(t.contents[0].content_id.startsWith("path")){var c=a.pathname.split("/"),s=t.contents[0].content_id.split("|")[1];n.content_id=c[s]}if(t.contents[0].content_id.startsWith("search")){var u=new URLSearchParams(a.search),f=t.contents[0].content_id.split("|")[1];n.content_id=u.get(f)}}}return n}catch(t){return(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"eb_jelly_error",custom_enum:"dynamicParameter_v1_error"}),n}},tn=function(t,e,n){var r="";if(-1===e||void 0===e){var o=to(t)[0];r=void 0!==o?tr(o,n):""}else{var i=to(t)[e];r=void 0!==i?tr(i,n):""}return r},tr=function(t,e){var n="";if((e===l.KEEP||e===l.WRONG)&&(n=t),e===l.TURNINTOINTEGER&&(n=t.replace(/[,\.]/g,"")),e===l.TURNINTODECIMAL){var r=t.split(/[,\.]/g),o="";r.forEach(function(t,e){e<r.length-1?o+=t:o+="."+t}),n=o}return n},to=function(t){for(var e,n=/[\d|\.|,]+/gm,r=[];null!==(e=n.exec(t));)e.index===n.lastIndex&&n.lastIndex++,e.forEach(function(t){r.push(t)});return r},ti=function(t,e,n){try{var r=t.split(x),o="";if(Object.keys(e).forEach(function(t){(null!==e[t]||void 0!==e[t])&&(o+=',"'+t+'":"'+("value"!==t?encodeURIComponent(e[t]):e[t])+'"')}),n&&(o+=',"dynamic_parameter_config":'+JSON.stringify(n)),o)return r[0]+x+o+r[1];return t}catch(e){return(0,d.vU)(_.O.CUSTOM_ERROR,e,{custom_name:"eb_jelly_error",custom_enum:"dynamicParameter_v1_transform_code_error"}),t}},ta=function(t,e){if(!t||""===t)return null;var n,r,o,i=null===(n=t.match(/closest\$([^$]+)\$/))||void 0===n?void 0:n[1],a=null===(r=t.match(/children\$([^$]+)\$/))||void 0===r?void 0:r[1];if(e&&i&&a){!Element.prototype.closest&&(Element.prototype.closest=function(t){var e=this;if(!document.contains(e))return null;do{if(e.matches(t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null});var c=e.closest(i);o=null==c?void 0:c.querySelector(a)}else o=e&&a?e.querySelector(a):document.querySelector(t);return o},tc=function(t,e,n){t.map(function(t){var r=t.content_type,o=t.content_id,i=t.content_from,a={};if(r&&(1===r?a.content_type="product":2===r&&(a.content_type="product_group")),o){if(i===p.CLICK_EVENT){var c=ta(o,n);(null==c?void 0:c.innerText)&&(a.content_id=null==c?void 0:c.innerText)}else if(i===p.DESTINATION_URL){var s=new URL(location.href);if(o.startsWith("path")){var u=s.pathname.split("/"),f=o.split("|")[1];a.content_id=u[f]}if(o.startsWith("search")){var l=new URLSearchParams(s.search),h=o.split("|")[1];a.content_id=l.get(h)||void 0}}!a.content_id&&(0,d.um)(_.O.CUSTOM_INFO,{custom_name:"eb_jelly_info",custom_enum:"dynamic_parameter_v2_content_id_empty",extJSON:{selector:o}})}i&&(a.content_from=i),e.push(a)})},ts=function(t,e){var n,r={};try{if(t.currency&&(r.currency=t.currency),t.value){var o=ta(t.value,e);(null==o?void 0:o.innerHTML)&&(r.ori_value=o.innerHTML,r.value=tn(null===(n=o.innerHTML)||void 0===n?void 0:n.trim(),t.value_index,t.value_parsing_method)),!r.value&&(0,d.um)(_.O.CUSTOM_INFO,{custom_name:"eb_jelly_info",custom_enum:"dynamic_parameter_v2_value_empty",extJSON:{selector:t.value}})}return t.contents&&t.contents.length>0&&(r.contents=[],tc(t.contents,r.contents,e)),r}catch(t){return(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"eb_jelly_error",custom_enum:"dynamicParameter_v2_error"}),r}},tu=function(t,e,n){try{var r=t.split(x),o="";if(Object.keys(e).forEach(function(t){if(null!==e[t]||void 0!==e[t]){if("contents"===t){var n=e[t];o+=',"'+t+'":[',null==n||n.map(function(t,e){o+="{";var r=Object.keys(t);r.forEach(function(e,n){"content_id"===e&&t[e]&&(t[e]=encodeURIComponent(t[e])),o+='"'+e+'":"'+t[e]+'"'+(n===(null==r?void 0:r.length)-1?"":",")}),o+="}"+(e===(null==n?void 0:n.length)-1?"":",")}),o+="]"}else o+=',"'+t+'":"'+("value"!==t?encodeURIComponent(e[t]):e[t])+'"'}}),n&&(o+=',"dynamic_parameter_config":'+JSON.stringify(n)),o)return r[0]+x+o+r[1];return t}catch(e){return(0,d.vU)(_.O.CUSTOM_ERROR,e,{custom_name:"eb_jelly_error",custom_enum:"dynamicParameter_v2_transform_error"}),t}};function tf(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return tl(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?tl(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function tl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function tp(t,e){return(tp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var th=new Z,td=function(t){function e(e,n,r,o){var i;return(i=t.call(this)||this).on("jelly_message",o),i.SendEvent=new tt(n,r),i.SendEvent.on("jelly_message",function(t){i.emit("jelly_message",t)}),i.CLICK=e.CLICK||[],i.PAGEVIEW=e.PAGEVIEW||[],i.VISIBILITY=e.VISIBILITY||[],i.HISTORY_CHANGE=e.HISTORY_CHANGE||[],i.SDK_ID=r||"",i.click(),i.pageview(),i.visibility(),i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,tp(n,r);var n,r,o=e.prototype;return o.dispatcher=function(t,e,n,r){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var o={taskName:window.ttq._pf_tn||"event_builder_dispatcher",functionName:window.ttq._pf_tn&&"event_builder_dispatcher",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="event_builder_dispatcher")}}catch(t){}if(e){for(var i,a=tf(e);!(i=a()).done;){for(var c,s=i.value,l=[],p=tf(s.conditions);!(c=p()).done;){var v=c.value,g=th.dispatcher(t,v,n,r),m=W(g,v.operator,v.value,v.variable_type);if(("history_change"===t||"pageview"===t)&&(m=m||W(L(g),v.operator,v.value,v.variable_type)),m){var y,E=!1;var w=(y=v.variable_type,Object.values(f).includes(y)?u.V2:u.V1);try{E=["ELEMENT",f.TOKENIZE_TEXT,f.IMG_SRC,f.ELEMENT_V2].includes(v.variable_type)&&(0,h.iK)(g)}catch(t){E=!1,(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"button_check_jelly_error",custom_enum:"auto_click",extJSON:{element:g}})}var b=k(s.code,E);if(b=function(t,e){try{var n=t.split(x),r=Object.entries(e).map(function(t){var e=t[0],n=t[1];return',"'+e+'":"'+n+'"'}).join("");return n[0]+x+r+n[1]}catch(e){return t}}(b=D(b),Object.assign({eb_version:w},v.rule_id&&{eb_rule_id:v.rule_id})),v.dynamic_parameter)try{var O=void 0,I=void 0;switch(v.variable_type){case f.PAGE_URL_V2:O=ts(v.dynamic_parameter,null),I=tu(b,O,v.dynamic_parameter);break;case f.ELEMENT_V2:case f.TOKENIZE_TEXT:case f.IMG_SRC:var S=K(g,v.variable_type,v.value,!0);O=ts(v.dynamic_parameter,S),I=tu(b,O,v.dynamic_parameter);break;default:O=te(v.dynamic_parameter),I=ti(b,O,v.dynamic_parameter)}T(I)}catch(t){(0,d.vU)(_.O.CUSTOM_ERROR,t,{custom_name:"eb_jelly_error",custom_enum:"dynamic_parameter_code_concat"}),T(b)}else T(b);this.SendEvent.sendDebugEvent("jelly."+t,s.code_id,l)}l.push(Object.assign(v,{cur_value:g,result:m}))}}try{window.ttq&&window.ttq._ppf&&(o.end=performance.now(),window.ttq._ppf.push(o),"event_builder_dispatcher"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}}},o.click=function(){var t=this;O(function(e){t.dispatcher("click",t.CLICK,e)},!0)},o.pageview=function(){this.dispatcher("pageview",this.PAGEVIEW),this.history_change(this.PAGEVIEW)},o.history_change=function(t){void 0===t&&(t=this.HISTORY_CHANGE);var e=this,n=history.state,r=location.hash,o=location.href;P(this.SDK_ID),window.addEventListener("pushState-"+this.SDK_ID,function(r){if(location.href!=o){var i={old_state:n};e.dispatcher("history_change",t,r,i),n=history.state,o=location.href}}),window.addEventListener("replaceState-"+this.SDK_ID,function(){if(location.href!=o){var r={old_state:n};e.dispatcher("history_change",t,r),n=history.state,o=location.href}}),window.addEventListener("popstate",function(n){if(location.href!=o){var i={old_hash:r};e.dispatcher("history_change",t,n,i),r=location.hash,o=location.href}})},o.visibility=function(){if(!(this.VISIBILITY.length<1)){var t=this.VISIBILITY,e=this.SendEvent.sendDebugEvent.bind(this.SendEvent);new MutationObserver(t_(t,e,window)).observe(document,{childList:!0,characterData:!0,subtree:!0,attributes:!0});for(var n=document.getElementsByTagName("iframe"),r=0;r<n.length;r++)try{var o=n[r].contentWindow;null!=o&&new MutationObserver(t_(t,e,o)).observe(o.document,{childList:!0,characterData:!0,subtree:!0,attributes:!0})}catch(t){}}},e}(z()),t_=function(t,e,n){void 0===n&&(n=window);var r={};return function(){t.forEach(function(t){var o=!0,i=[],a=[];t.conditions.forEach(function(t){if(J.visibility.indexOf(t.variable_type)>-1){var e=th.dispatcher("visibility",t,null,null,n.document),c="_"+t.value;e&&A(n,e)&&!r[c]&&(i.push(N(e,"",t)),r[c]=!0)}else{var s=th.dispatcher("visibility",t),u=W(s,t.operator,t.value,t.variable_type);!u&&(o=!1),a.push(Object.assign(t,{cur_value:s,result:u}))}}),i.length>0&&Promise.all(i).then(function(n){for(var r=!0,i,c=tf(n);!(i=c()).done;){var s=i.value;a.push(Object.assign(s.condition,{cur_value:s.curValue,result:s.result})),(!s.result||!o)&&(r=!1)}r&&T(t.code),e("jelly.visibility",t.code_id,a)},function(){})})}};function tv(t,e){return(tv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tg=function(t){var e,n;function r(e,n){var r;if((r=t.call(this)||this).BaseConf=n,r.SDK_ID=e,window.jelly_tool_events&&window.jelly_tool_events.length&&window.jelly_tool_events.forEach(function(t){r.on(t.name,t.callback)}),r.emit("jelly_event",{SDK_ID:e,BaseConf:n||[]}),r.BaseConf instanceof Array){if(self._jelly_sdks=self._jelly_sdks||{},self._jelly_sdks[e])return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(r);self._jelly_sdks[e]=!0;var o,i=r.dispatch();o=0,r.trigger=new td(i,n,e,function(t){r.emit("jelly_message",t)})}return r}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,tv(e,n),r.prototype.dispatch=function(){var t={CLICK:[],PAGEVIEW:[],VISIBILITY:[],HISTORY_CHANGE:[]};return this.BaseConf.forEach(function(e){var n={code_id:e.code_id,code:e.code,conditions:e.conditions||[]};void 0!==e.trigger_type&&t[e.trigger_type]&&t[e.trigger_type].push(n)}),t},r}(z());window.TiktokJelly=tg}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={id:r,loaded:!1,exports:{}};return t[r](i,i.exports,n),i.loaded=!0,i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.nmd=function(t){return t.paths=[],!t.children&&(t.children=[]),t},n.rv=function(){return"1.0.14"},n.ruid="bundler=rspack@1.0.14",!function(){"use strict";var t,e,r,o,i,a=n("783"),c=n("825"),s=n("332"),u=n("790"),f=n("319"),l=n("188"),p=n("229"),h=function(t){void 0===t&&(t={});var e=Object.assign({},{path:"/"},t);"number"==typeof e.expires&&(e.expires=new Date(Date.now()+864e5*e.expires)),e.expires instanceof Date&&(e.expires=e.expires.toUTCString());var n="";for(var r in e){if(!e[r])continue;if(n+="; "+r,!0!==e[r])n+="="+e[r].split(";")[0]}return n},d=function(t,e,n,r){void 0===n&&(n="/"),void 0===r&&(r=(0,l.pW)()),E(t,e,{path:n,expires:r})},_=function(t){if(t&&t.cde){var e=t.cde;return Object.assign({},p.Yp,{expires:e})}return p.Yp},v=0,g=0,m=function(t){var e={};return document.cookie.split(";").forEach(function(t){var n=t.split("=");e[n[0].trim()]=n.slice(1).join("=")}),e[t]||""},y=function(t){if(0===document.cookie.length)return"";var e=m(t);return e?unescape(e):""},E=function(t,e,n){try{if(n){var r=window.location.hostname.split(".");if(g=r.length,(v=O())&&v<g){n.domain="."+r.slice(g-v-1).join("."),I(t,e,n);return}for(var o="",i=0;i<g&&(o="."+r[g-i-1]+o,n.domain=o,v=i,!I(t,e,n));i++);}else document.cookie=t+"="+e+h(n)}catch(t){(0,a.vU)(c.O.API_ERROR,t,{extJSON:{position:"setCookieToHighestDomain"}})}},w=function(t){var e=t.index,n=t.main;sessionStorage.setItem(p.AU,JSON.stringify({index:e,main:n}))},b=function(t){var e=t.split(".");return e.length>=3&&e.includes("tt")&&!isNaN(Number(e[2]))&&Number(e[2])>0},O=function(){if(v)return v;var t=y(p.qC);return t&&b(t)?Number(t.split(".")[2]):0},I=function(t,e,n){if(t===p.qC&&!b(e)){var r;e=(r=e)?r.split(".")[0]+".tt."+((0,u.z6)()?0:O()):""}return document.cookie=t+"="+e+h(n),e===y(t)},T=function(){function t(){}return t.interceptCookies=function(){var t=Object.getOwnPropertyDescriptor(Document.prototype,"cookie");if(!t||!!t.configurable){var e=this;Object.defineProperty(document,"cookie",Object.assign(Object.assign({},t),{set:function(n){if(t&&"function"==typeof t.set)try{var r=y(p.qC),o=t.set.call(this,n);if(e.ttpCookieRegex.test(n)){var i=y(p.qC);(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"cookie_set",extJSON:{message:r+"||"+i,stack:Error().stack||""}})}return o}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"cookie_set_error"})}}}))}},t.interceptSessionStorage=function(){var t=sessionStorage.setItem.bind(sessionStorage),e=sessionStorage.clear.bind(sessionStorage),n=sessionStorage.removeItem.bind(sessionStorage);sessionStorage.clear=function(){try{var t=e();return(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"sessionStorage_clear",extJSON:{stack:Error().stack||""}}),t}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"sessionStorage_clear_error"})}},sessionStorage.setItem=function(e,n){try{var r=sessionStorage.getItem(e),o=t(e,n);return e===p.Bp&&(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"sessionStorage_set",extJSON:{message:r+"||"+n,stack:Error().stack||""}}),o}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"sessionStorage_set_error"})}},sessionStorage.removeItem=function(t){try{var e=n(t);return t===p.Bp&&(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"sessionStorage_remove",extJSON:{stack:Error().stack||""}}),e}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"sessionStorage_remove_error"})}}},t.listenPageLeave=function(){window.addEventListener("beforeunload",function(){try{var t=y(p.qC);(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"track_beforeunload",extJSON:{message:""+t}})}catch(t){}})},t}();T.ttpCookieRegex=/_ttp=/,T.init=(0,f.IH)(function(){try{T.interceptCookies(),T.interceptSessionStorage(),T.listenPageLeave()}catch(t){(0,a.vU)(c.O.API_ERROR,t,{custom_name:"StorageObserver_init_error"})}});var S=function(){var t=(0,s.Ie)();return"object"==typeof t&&t._i?t._i:{}},N=function(t,e){var n=S()||{};Object.keys(n).forEach(function(r){var o=n[r];if(!o._init)o.push([t].concat(e))})},R=function(t,e,n){var r=(S()||{})[t];if(r){if(r._init)return;r.push([e].concat(n))}};(rJ=od||(od={})).PIXEL_CODE="pixelCode",rJ.EVENT_SOURCE_ID="eventSourceId",rJ.SHOP_ID="shopId";var P=n("408"),A={TTQ:Symbol.for("TTQ"),GLOBAL_TTQ:Symbol.for("GLOBAL_TTQ"),SHOPIFY_TTQ:Symbol.for("SHOPIFY_TTQ"),ENV:Symbol.for("ENV"),CONTEXT:Symbol.for("CONTEXT"),REPORTER:Symbol.for("REPORTER"),REPORTERS:Symbol.for("REPORTERS"),PLUGIN:Symbol.for("PLUGIN"),PLUGINS:Symbol.for("PLUGINS"),TTQ_GLOBAL_OPTIONS:Symbol.for("TTQ_GLOBAL_OPTIONS"),PERFORMANCE_PLUGIN:Symbol.for("PERFORMANCE_PLUGIN"),INTERACTION_PLUGIN:Symbol.for("INTERACTION_PLUGIN"),INTERACTION_PLUGIN_MONITOR:Symbol.for("INTERACTION_PLUGIN_MONITOR"),PERFORMANCE_PLUGIN_MONITOR:Symbol.for("PERFORMANCE_PLUGIN_MONITOR"),ADVANCED_MATCHING_PLUGIN:Symbol.for("ADVANCED_MATCHING_PLUGIN"),AUTO_ADVANCED_MATCHING_PLUGIN:Symbol.for("AUTO_ADVANCED_MATCHING_PLUGIN"),CALLBACK_PLUGIN:Symbol.for("CALLBACK_PLUGIN"),IDENTIFY_PLUGIN:Symbol.for("IDENTIFY_PLUGIN"),MONITOR_PLUGIN:Symbol.for("MONITOR_PLUGIN"),WEB_FL_PLUGIN:Symbol.for("WEB_FL_PLUGIN"),SHOPIFY_PLUGIN:Symbol.for("SHOPIFY_PLUGIN"),AUTO_CONFIG_PLUGIN:Symbol.for("AUTO_CONFIG_PLUGIN"),DIAGNOSTICS_CONSOLE_PLUGIN:Symbol.for("DIAGNOSTICS_CONSOLE_PLUGIN"),COMPETITOR_INSIGHT_PLUGIN:Symbol.for("COMPETITOR_INSIGHT_PLUGIN"),PANGLE_COOKIE_MATCHING_PLUGIN:Symbol.for("PANGLE_COOKIE_MATCHING_PLUGIN"),EVENT_BUILDER_PLUGIN:Symbol.for("EVENT_BUILDER_PLUGIN"),ENRICH_IPV6_PLUGIN:Symbol.for("ENRICH_IPV6_PLUGIN"),RUNTIME_MEASUREMENT_PLUGIN:Symbol.for("RUNTIME_MEASUREMENT_PLUGIN"),PAGE_PERFORMANCE_MONITOR:Symbol.for("PAGE_PERFORMANCE_MONITOR"),PAGE_INTERACTION_MONITOR:Symbol.for("PAGE_INTERACTION_MONITOR"),PAGEDATA_PLUGIN:Symbol.for("PAGEDATA_PLUGIN"),HISTORY_OBSERVER:Symbol.for("HISTORY_OBSERVER"),BATCH_SERVICE:Symbol.for("BATCH_SERVICE"),REPORT_SERVICE:Symbol.for("REPORT_SERVICE"),AD_SERVICE:Symbol.for("AD_SERVICE"),APP_SERVICE:Symbol.for("APP_SERVICE"),BRIDGE_SERVICE:Symbol.for("BRIDGE"),HTTP_SERVICE:Symbol.for("HTTP_SERVICE"),COOKIE_SERVICE:Symbol.for("COOKIE_SERVICE"),CONSENT_SERVICE:Symbol.for("CONSENT_SERVICE"),JS_BRIDGE:Symbol.for("JS_BRIDGE"),TTQ_REPORTERS:Symbol.for("TTQ_REPORTERS"),INTERACTION_MONITOR:Symbol.for("INTERACTION_MONITOR"),PERFORMANCE_MONITOR:Symbol.for("PERFORMANCE_MONITOR"),SANDBOX_PIXEL_API:Symbol("SANDBOX_PIXEL_API")},L=n("74");(rX=o_||(o_={})).INIT_START="initStart",rX.INIT_END="initEnd",rX.CONTEXT_INIT_START="contextInitStart",rX.CONTEXT_INIT_END="contextInitEnd",rX.PAGE_URL_WILL_CHANGE="pageUrlWillChange",rX.PAGE_URL_DID_CHANGE="pageUrlDidChange",rX.PAGE_DID_LOAD="pageDidLoad",rX.PAGE_WILL_LEAVE="pageWillLeave",rX.AD_INFO_INIT_START="adInfoInitStart",rX.AD_INFO_INIT_END="adInfoInitEnd",rX.BEFORE_AD_INFO_INIT_START="beforeAdInfoInitStart",rX.PIXEL_SEND="pixelSend",rX.PIXEL_DID_MOUNT="pixelDidMount",(rZ=ov||(ov={})).UNKNOWN="-1",rZ.LOADING="0",rZ.INTERACTIVE="1",rZ.COMPLETE="2",(rQ=og||(og={})).HISTORY_CHANGE="hc",rQ.URL_CHANGE="uc";var C=["page","track","identify"],x=["holdConsent","revokeConsent","grantConsent"],k=["identify"],D=["instance","instances","loadPixel","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"];(rz=om||(om={})).EMPTY_EVENT_TYPE_NAME="EMPTY_EVENT_TYPE_NAME",rz.MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT="MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT",rz.LONG_EVENT_TYPE_NAME="LONG_EVENT_TYPE_NAME",rz.MISSING_VALUE_PARAMETER="MISSING_VALUE_PARAMETER",rz.MISSING_CURRENCY_PARAMETER="MISSING_CURRENCY_PARAMETER",rz.MISSING_CONTENT_ID="MISSING_CONTENT_ID",rz.MISSING_EMAIL_AND_PHONE="MISSING_EMAIL_AND_PHONE",rz.INVALID_EVENT_PARAMETER_VALUE="INVALID_EVENT_PARAMETER_VALUE",rz.INVALID_CURRENCY_CODE="INVALID_CURRENCY_CODE",rz.INVALID_CONTENT_ID="INVALID_CONTENT_ID",rz.INVALID_CONTENT_TYPE="INVALID_CONTENT_TYPE",rz.INVALID_EMAIL_FORMAT="INVALID_EMAIL_FORMAT",rz.INVALID_PHONE_NUMBER_FORMAT="INVALID_PHONE_NUMBER_FORMAT",rz.INVALID_EMAIL_INFORMATION="INVALID_EMAIL_INFORMATION",rz.INVALID_PHONE_NUMBER_INFORMATION="INVALID_PHONE_NUMBER_INFORMATION",rz.DUPLICATE_PIXEL_CODE="DUPLICATE_PIXEL_CODE",rz.MISSING_PIXEL_CODE="MISSING_PIXEL_CODE",rz.INVALID_PIXEL_CODE="INVALID_PIXEL_CODE";var M=function(t,e,n){t.isBound(e)?t.rebind(e).toConstantValue(n):t.bind(e).toConstantValue(n)},j=function(){var t=(0,s.Ie)();return t&&t._i||{}},U=function(t,e,n){void 0===n&&(n=P.S.OFFSITE);try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"webTtqFactory",start:performance.now()}}catch(t){}M(t,A.ENV,e),M(t,L.M.SignalType,n);var o=t.get(A.TTQ);try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}return o},q=function(t,e){var n,r=e.id,o=e.type,i=void 0===o?od.PIXEL_CODE:o,a=e.info,c=e.options,s=e.plugins,u=void 0===s?{}:s,f=e.rules;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var l={taskName:window.ttq._pf_tn,functionName:"webReporterFactory",start:performance.now()}}catch(t){}var p=t.get(A.TTQ),h=t.get(A.TTQ_REPORTERS);if(!h.some(function(t){return t.getReporterId()===r})){M(t,L.M.ID,r),M(t,L.M.Type,i),M(t,L.M.Info,a||((n={})[i]=r,n)),M(t,L.M.Options,void 0===c?{}:c),M(t,L.M.Plugins,u),M(t,L.M.Rules,void 0===f?[]:f),p.enableFirstPartyCookie((null==a?void 0:a.firstPartyCookieEnabled)||!1);var d=t.get(A.REPORTER);if(u){var _=u.AdvancedMatching,v=u.AutoAdvancedMatching,g={};_&&Object.assign(g,_),v&&Object.assign(g,v),d.setAdvancedMatchingAvailableProperties(g)}d.on("beforeReport",function(t,e,n,r,o){p.dispatch(o_.PIXEL_SEND,t,e,n,r,o)}),h.push(d),t.rebind(A.TTQ_REPORTERS).toConstantValue(h),p.dispatch(o_.PIXEL_DID_MOUNT,d);try{window.ttq&&window.ttq._ppf&&(l.end=performance.now(),window.ttq._ppf.push(l))}catch(t){}return d}},F=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"mergeWebGlobalTtq",start:performance.now()}}catch(t){}["getReporter","usePlugin","getPlugin","resetCookieExpires"].forEach(function(n){t[n]=function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];return e[n].apply(e,r)}}),t.context=e.context,t.reporters=e.reporters;try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}return t},V=function(t,e){var n=t||{},r=n._partner,o=n._ttp,i=n._self_host_config,a=n._usd_exchange_rate,c=n._legacy;return Object.assign(e,{partner:r,ttp:o,cc:n._cc,self_host_config:i,usd_exchange_rate:a,legacy:c,variation_id:n._variation_id,vids:n._vids,server_unqiue_id:n._server_unique_id,currency_list:n._currency_list,plugins:n._plugins,aam:n._aam,auto_config:n._auto_config,cde:n._cde}),e},G=function(t,e){var n=t.get(A.TTQ_GLOBAL_OPTIONS)||{};V(e,n),t.isBound(A.TTQ_GLOBAL_OPTIONS)?t.rebind(A.TTQ_GLOBAL_OPTIONS).toConstantValue(n):t.bind(A.TTQ_GLOBAL_OPTIONS).toConstantValue(n)},H=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"freezeAPI",start:performance.now()}}catch(t){}var n=(0,s.E9)(),r=(0,s.Gp)(),o=(0,s.Ie)(),i=(0,a.wE)().pixelCode,u=void 0===i?"":i;D.forEach(function(e){Object.defineProperty(o,e,{get:function(){return function(){try{var n=Array.prototype.slice.call(arguments);return x.indexOf(e)>-1&&setTimeout(function(){(0,a.um)(c.O.CUSTOM_INFO,{pixelCode:u,custom_name:e})}),t[e].apply(t,n)}catch(t){return(0,a.vU)(c.O.API_ERROR,t,{extJSON:{api:e}}),{}}}},set:function(){}})}),C.forEach(function(e){Object.defineProperty(o,e,{get:function(){return function(){try{var n=1==arguments.length&&void 0===arguments[0]?[]:Array.prototype.slice.call(arguments);return N(e,n),t[e].apply(t,n)}catch(t){return(0,a.vU)(c.O.API_ERROR,t,{extJSON:{api:e}}),{}}}},set:function(){}})}),n[r]._mounted=!0,n[r].initialize=!0,(0,s.gM)(n[r]);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},B=function(t,e,n){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"handleCache",start:performance.now()}}catch(t){}K(e),Object.entries(j()).forEach(function(r){var o=r[0],i=r[1];if(!i._init&&("Tealium"===(0,s.kW)(o)||!!(0,s.cH)()||!!i.info)){if(e.getReporter(o)?(0,a.ZK)(om.DUPLICATE_PIXEL_CODE):(n||q)(t,{id:o,type:od.PIXEL_CODE,info:i.info,options:i.options,rules:i.rules,plugins:i.plugins}),i._init=!0,i.length>0)for(;i.length;){var u=i.shift();if(!u)continue;var f=u[0],l=u.slice(1),p=e.instance(o);if(!!p)switch(f){case"identify":e.identify(l[0],l[1]);break;case"page":e.page(l[0]);break;case"track":p.track(l[0],l[1],l[2]||{});break;default:p[f]?p[f](l[0],l[1],l[2]||{}):(0,a.vU)(c.O.CUSTOM_ERROR,Error("action not find: "+p[f]))}}}}),W(e);try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}},K=function(t){try{var e=(0,s.Ie)();[].concat(x,k).forEach(function(n){e.find(function(r,o){var i=r[0],a=r.slice(1);if(i===n)return t[n].apply(t,a),e.splice(o,1),!0})})}catch(t){(0,a.vU)(c.O.API_ERROR,t,{extJSON:{api:"handleP0APICache"}})}},W=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"handleGlobalCache",start:performance.now()}}catch(t){}var n=(0,s.Ie)();if(n.length>0)for(;n.length;){var r=n.shift();if(!!r){var o=r[0],i=r.slice(1);switch(!(0,s.ri)("Tealium")&&N(o,i),o){case"identify":t.identify(i[0],i[1]);break;case"page":t.page(i[0]);break;case"track":t.track(i[0],i[1],i[2]||{});break;case"enableCookie":t.enableCookie();break;case"disableCookie":t.disableCookie();break;case"holdConsent":t.holdConsent();break;case"revokeConsent":t.revokeConsent();break;case"grantConsent":t.grantConsent()}}}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}};function Y(t,e){return(Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function J(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(J=function(){return!!t})()}var X="_ttq_inject",Z=function(){function t(){this.bindings={},this.rebind=this.bind}var e=t.prototype;return e.bind=function(t){var e=this;return{to:function(n){return e.bindings[t.toString()]={constructor:n,singleton:!1},e},toConstantValue:function(n){return e.bindings[t.toString()]={value:n,singleton:!0},e}}},e.isBound=function(t){return!!this.bindings[t.toString()]},e.inSingletonScope=function(){var t=Object.keys(this.bindings),e=t[t.length-1];if(e){var n=this.bindings[e];n&&(this.bindings[e]=Object.assign(Object.assign({},n),{singleton:!0}))}return this},e.get=function(t){var e=this.bindings[t.toString()];if(!e)throw Error("No binding found for identifier: "+String(t));return void 0!==e.value?e.value:e.singleton?(!e.instance&&(e.instance=this.createInstance(e.constructor)),e.instance):this.createInstance(e.constructor)},e.createInstance=function(t){var e=this;if(!t)throw Error("Cannot create instance because constructor is not defined.");var n=(t[X]||[]).map(function(t){try{return e.get(t.identifier)}catch(e){if(t.optional)return;throw e}});return function(t,e,n){if(J())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var o=new(t.bind.apply(t,r));return n&&Y(o,n.prototype),o}(t,n)},t}();function Q(){return function(t){}}function z(t){return function(e,n,r){e[X]=e[X]||[],e[X][r]=Object.assign({},e[X][r],{identifier:t})}}function $(){return function(t,e,n){t[X]=t[X]||[],t[X][n]=Object.assign({},t[X][n],{optional:!0})}}function tt(t,e){try{var n=new URL(t).searchParams.getAll(e);return n[n.length-1]||""}catch(t){return""}}(r$=oy||(oy={})).BIND="bind",r$.REBIND="rebind";var te=function(t,e,n){try{var r=tt(e,t);if(r)return r;return tt(n||"",t)}catch(t){}return""},tn=function(){if(!(0,u.zd)())return{url:window.location.href,referrer:document.referrer};if(!(0,u.xD)())return{url:(null===(t=null==window?void 0:window.top)||void 0===t?void 0:t.location.href)||"",referrer:(null===(e=null==window?void 0:window.top)||void 0===e?void 0:e.document.referrer)||""};var t,e,n=window.location.href,r=document.referrer;if(/doubleclick\.net/.test(window.location.hostname)){var o=window.location.pathname,i={};return o.split(";").forEach(function(t){var e=t.split("="),n=e[0],r=e[1];i[n]=decodeURIComponent(r)}),{url:i["~oref"]||n,referrer:document.referrer}}return{url:n,referrer:r}},tr=function(t){var e=tn().url;try{return new URL(t||e)}catch(t){}return null},to=function(t){try{var e=window.sessionStorage.getItem(t);if(!e)return null;return JSON.parse(e)}catch(t){return null}},ti=function(t){try{window.sessionStorage.removeItem(t)}catch(t){}},ta=function(t,e){try{var n=JSON.stringify(e);window.sessionStorage.setItem(t,n)}catch(t){}},tc="https://analytics.tiktok.com/api/v2",ts=tc+"/pixel",tu=tc+"/performance",tf=tc+"/interaction",tl=tc+"/performance_interaction",tp=tc+"/pixel/perf",th=tc+"/pixel/inter",td=tc+"/pixel/act",t_="https://analytics-ipv6.tiktokw.us/ipv6/enrich_ipv6",tv="ttclid",tg="_toutiao_params",tm=function(t,e){try{var n=te(tv,t,e)||void 0,r=te("ext_params",t,e)||void 0,o=te(tg,t,e)||void 0,i=parseInt(te("ttuts",t,e),10)||void 0,a=o?JSON.parse(o):{},c=a.log_extra,s=void 0===c?void 0:c,u=a.idc,f=void 0===u?void 0:u,l=a.cid,p=void 0===l?void 0:l;return{callback:n,ext_params:r,log_extra:s,creative_id:p,idc:f,ttuts:i,ad_info_from:(s||f||p)&&"url"}}catch(t){return{}}},ty=function(t,e){try{var n=t.log_extra,r=t.ttuts;if(!(0,u.gn)())return!0;if(!(0,u.XA)(e)){if(null!=r)return 1!==r;return!0}if(n){var o=JSON.parse(n);return 1!==o.user_tracking_status}if(null===t.ATTStatus||void 0===t.ATTStatus)return!0;return 3===t.ATTStatus}catch(t){return!1}},tE=function(t,e){var n={};try{var r=t.creative_id,o=t.callback,i=t.idc,a=t.convert_id,c=t.ad_info_from,s=t.ad_info_status,u=t.log_extra,f=t.ext_params,l=t.ATTStatus;if(r&&(n.creative_id=r),i&&(n.idc=i),a&&(n.convert_id=a),c&&(n.ad_info_from=c),s&&(n.ad_info_status=s),f&&(n.ext_params=f),l&&(n.ATTStatus=l),u){var p=JSON.parse(u),h=p.ad_user_agent,d=p.ad_id,_=p.rit,v=p.ocbs,g=p.vid,m=p.idc,y=p.country_id;d&&(n.ad_id=d),_&&(n.rit=_),h&&(n.ad_user_agent=h),v&&(n.ocbs=v),g&&(n.vid=g),m&&(n.idc=m),y&&(n.country_id=y)}return n}catch(t){return e&&e(t),n}},tw=function(t,e,n){void 0===t&&(t=""),void 0===e&&(e="");try{var r=te("tt_test_id",t,n);return r&&r!==e&&d("tt_test_id",r,void 0,"session"),r||e}catch(t){return""}},tb=n("729"),tO=function(){var t=new Date().getTime();return{sessionId:t.toString()+"::"+(0,f.$j)(20),sessionCount:1,lastEventTS:t,isSessionStart:!0}},tI=function(t){var e={};return t.split("; ").forEach(function(t){var n=t.split("="),r=n[0],o=n[1];if(r.startsWith(p.ku)){var i=r.replace(p.ku,"").replace("_",""),a=o.split("."),c=a[0],s=a[1],u=a[2];c&&(0,l.eZ)(s)&&(0,l.eZ)(u)&&(e[i]={sessionId:c,sessionCount:Number(s),lastEventTS:Number(u)})}}),e},tT=function(t){var e=t.sessionCount,n=t.lastEventTS,r=new Date().getTime();return r-n>18e5?{sessionId:r.toString()+"::"+(0,f.$j)(20),sessionCount:e+1,lastEventTS:r,isSessionStart:!0}:Object.assign(Object.assign({},t),{lastEventTS:r,isSessionStart:!1})},tS=function(t,e,n){var r=t.sessionId,o=t.sessionCount,i=t.isSessionStart;return Object.assign({csid:r,page_csid:e.sessionId,csct:o},i?{css:1}:{},n?{pixel_code:n}:{})},tN=function(){function t(t){this.pixelCode="",this.loaded=!1,this.status=1,this.name="",this.advertiserID="",this.setupMode=0,this.partner="",this.reporterInfo={},this.plugins={},this.options={},this.rules=[],this.pixelCode=t}var e=t.prototype;return e.getParameterInfo=function(){return Promise.resolve({pixelCode:this.pixelCode,name:this.name,status:this.status,setupMode:this.setupMode,advertiserID:this.advertiserID,partner:this.partner,is_onsite:!1,advancedMatchingAvailableProperties:{}})},e.getReporterId=function(){return""},e.getReporterUniqueLoadId=function(){return""},e.getReporterPartner=function(){},e.getReporterInfo=function(){return{reporter:{}}},e.getReportResultSet=function(){return[]},e.isOnsite=function(){return!1},e.isPartnerReporter=function(){return!1},e.setAdvancedMatchingAvailableProperties=function(t){},e.clearHistory=function(){},e.page=function(t){},e.track=function(t,e,n){return Promise.resolve(null)},e.getUserInfo=function(t){return{}},e.getReporterMatchedUserFormatInfo=function(){return{}},e.getReporterMatchedUserFormatInfoV2=function(){return{}},e.assemblyData=function(){return{event:"",message_id:"",event_id:"",is_onsite:!1,properties:{},context:{ad:{},device:{},library:{name:"",version:""},page:{url:""},pageview_id:"",session_id:"",variation_id:"",user:{}},partner:"",timestamp:""}},e.assemblySelfHostData=function(){return this.assemblyData()},e.trackSync=function(){},e.getReportEventHistoryKey=function(t){return"tiktok"},e.hasReportEventHistory=function(t,e){return!1},e.getCookieBasedSession=function(){return tO()},e.setCookieBasedSession=function(t){},t}();new tN("empty");var tR=function(){function t(t,e){this.initialize=!1,this.plugins=[],this.observers=[],this.reporters=[],this.context=t,this.reportService=e}var e=t.prototype;return e.init=function(t,e){this.initContextInfo(t,e),this.initialize=!0},e.initContextInfo=function(t,e){var n=this;this.dispatch(o_.CONTEXT_INIT_START),this.initAdInfo(t,e),this.initAppInfo(t,e),this.initLocalServiceInfo(),this.reportService.pushPreposition(Promise.resolve().then(function(){return n.initUserInfo()})),this.initTestId(t,e),this.dispatch(o_.CONTEXT_INIT_END)},e.setPageIndex=function(t){},e.setPageInfo=function(t,e){var n=this.context.getPageInfo().url;if(n!==t){this.dispatch(o_.PAGE_URL_WILL_CHANGE,e||n,t);var r=this.context.setPageInfo(t,e||n);(null==r?void 0:r.pageIndex)&&this.setPageIndex(r.pageIndex),this.dispatch(o_.PAGE_URL_DID_CHANGE,t,n)}},e.initAdInfo=function(t,e){},e.initOffsiteAdInfo=function(t){},e.initAppInfo=function(t,e){},e.initUserInfo=function(){},e.initTestId=function(t,e){},e.usePlugin=function(t){try{if(!this.plugins.find(function(e){return e.name===t.name})){this.plugins.push(t);var e=t.name;e&&(this[e[0].toLowerCase()+e.slice(1)+"Plugin"]=t)}}catch(t){}},e.useObserver=function(t){try{if(!this.observers.find(function(e){return e.name===t.name})){this.observers.push(t);var e=t.name;e&&(this[""+(e[0].toLowerCase()+e.slice(1))]=t)}}catch(t){}},e.getPlugin=function(t){return this.plugins.find(function(e){return e.name===t})||null},e.getReporter=function(t){return this.reporters.find(function(e){return e.getReporterId()===t})},e.instance=function(t){var e=this.getReporter(t);return e?e:((0,a.vU)(c.O.PIXEL_EMPTY,Error(""),{pixelCode:t}),new tN(t))},e.instances=function(){return this.reporters},e.identify=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"identify_api_handler",functionName:window.ttq._pf_tn&&"identify_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_api_handler")}}catch(t){}var r=(0,f.oh)(t,e);this.context.setUserInfo(r);try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"identify_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.page=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"page_api_handler",functionName:window.ttq._pf_tn&&"page_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_api_handler")}}catch(t){}t.url!==this.context.getPageInfo().url&&(this.setPageInfo(t.url,t.referrer),this.reporters.forEach(function(t){t.clearHistory()}));var n=Object.assign({},t);delete n.url,delete n.referrer,this.reporters.forEach(function(t){t.page(n)});try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"page_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.isOnsitePage=function(){return this.context.getSignalType()===P.S.ONSITE||this.reporters.every(function(t){return t.isOnsite()})},e.track=function(t,e,n){void 0===e&&(e={}),void 0===n&&(n={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"track_api_handler",functionName:window.ttq._pf_tn&&"track_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_api_handler")}}catch(t){}this.instances().forEach(function(r,o){r.track(t,e,Object.assign({_i:o},n))});try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"track_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.dispatch=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];this.plugins.concat(this.observers).forEach(function(e){if("function"==typeof e[t])try{e[t].apply(e,n)}catch(r){(0,a.vU)(c.O.PLUGIN_ERROR,r,{extJSON:{plugin_name:e.name,cycle_name:t,data:n}})}})},e.getAllReportResultSet=function(){return this.instances().reduce(function(t,e){return t.concat(e.getReportResultSet())},[])},e.resetCookieExpires=function(){},e.enableCookie=function(){},e.disableCookie=function(){},e.enableFirstPartyCookie=function(t){},e.holdConsent=function(){},e.revokeConsent=function(){},e.grantConsent=function(){},e.initLocalServiceInfo=function(){},t}();function tP(){tP=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function tA(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}tR=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],tR);var tL=(r1=(r0=tP().mark(function t(e,n){return tP().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===n&&(n=1),!(n>=0)){t.next=13;break}return t.prev=2,t.next=5,function(t){return new Promise(function(e,n){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src=t;var o=document.getElementsByTagName("script")[0];o&&o.parentNode?o.parentNode.insertBefore(r,o):n("none element"),r.onload=function(){e(!0)},r.onerror=n})}(e);case 5:return t.abrupt("return",Promise.resolve(!0));case 8:return t.prev=8,t.t0=t.catch(2),t.abrupt("return",tL.call(null,e,n-1));case 11:t.next=14;break;case 13:throw Error;case 14:case"end":return t.stop()}},t,null,[[2,8]])}),function(){var t=this,e=arguments;return new Promise(function(n,r){var o=r0.apply(t,e);function i(t){tA(o,n,r,i,a,"next",t)}function a(t){tA(o,n,r,i,a,"throw",t)}i(void 0)})}),function(t,e){return r1.apply(this,arguments)});(r2=oE||(oE={})).TRACK="track",r2.PERFORMANCE="performance",r2.INTERACTION="interaction",r2.PCM="PCM",r2.PERFORMANCE_INTERACTION="performance_interaction",r2.SELFHOST="selfhost",r2.AUTO_CONFIG="auto_config",r2.PAGE="Pf",r2.PAGE_PERFORMANCE="page_performance",r2.PAGE_INTERACTION="page_interaction";var tC=["EnrichAM"];(r3=ow||(ow={})).LDU="limited_data_use",r3.EVENTID="eventID",r3.EVENT_ID="event_id",(r6=ob||(ob={}))[r6.defaultReport=0]="defaultReport",r6[r6.httpReport=1]="httpReport",r6[r6.htmlHttpReport=2]="htmlHttpReport",(r5=oO||(oO={}))[r5.P0=0]="P0",r5[r5.P1=1]="P1",r5[r5.P2=2]="P2";var tx=["ttuts","ad_info_from"],tk="Pageview",tD=[],tM=["AED","ALL","AMD","ARS","AUD","AZN","BDT","BGN","BHD","BIF","BOB","BRL","BYN","CAD","CHF","CLP","CNY","COP","CRC","CZK","DKK","DOP","DZD","EGP","EUR","GBP","GEL","GTQ","HKD","HNL","HUF","IDR","ILS","INR","IQD","ISK","JOD","JPY","KES","KHR","KRW","KWD","KZT","LBP","MAD","MOP","MXN","MYR","NGN","NIO","NOK","NZD","OMR","PAB","PEN","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","SAR","SEK","SGD","THB","TND","TRY","TWD","TZS","UAH","USD","UZS","VES","VND","ZAR"],tj=function(){function t(){this.events={}}var e=t.prototype;return e.on=function(t,e){var n=this.events[t]||[];n.push(e),this.events[t]=n},e.emit=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];(this.events[t]||[]).forEach(function(t){return t.apply(void 0,n)})},e.off=function(t,e){var n=this.events[t]||[];this.events[t]=n.filter(function(t){return t!==e})},t}();tj=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],tj);var tU=function(t,e){if("selfhost"===t&&e&&(0,s.My)(e))return"https://"+(0,s.My)(e)+"/api/v2/pixel";var n={track:ts,performance:tu,interaction:tf,performance_interaction:tl,auto_config:td,page_performance:tp,page_interaction:th}[t];return n?n:null};function tq(t,e){return(tq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tF=function(t){function e(e,n){var r;return(r=t.call(this)||this).reporterInfo={},r.options={},r.plugins={},r.rules=[],r.reportEventHistory={},r.reportResultSet=[],r.selfHostConfig={},r.currentHref="",r.advancedMatchingAvailableProperties={external_id:!0,partner_id:!0},r.session=tO(),r.reportService=n,r.context=e,r}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,tq(n,r);var n,r,o=e.prototype;return o.getParameterInfo=function(){var t=this;return this.getInstance().then(function(){var e=t.reporterInfo,n=e.name,r=e.status,o=e.setupMode,i=e.advertiserID,a=e.is_onsite;return{pixelCode:t.getReporterId(),name:void 0===n?"":n,status:void 0===r?1:r,setupMode:void 0===o?0:o,advertiserID:(void 0===i?"":i).toString(),partner:t.getReporterPartner()||"",is_onsite:void 0!==a&&a,advancedMatchingAvailableProperties:t.advancedMatchingAvailableProperties,rules:t.rules}})},o.getInstance=function(){return this.pixelPromise=Promise.resolve(this)},o.getReporterId=function(){return""},o.getReporterUniqueLoadId=function(){return""+this.getReporterId()},o.getReporterPartner=function(){},o.getReporterInfo=function(){return{pixel:{code:this.getReporterId()}}},o.setAdvancedMatchingAvailableProperties=function(t){this.advancedMatchingAvailableProperties=Object.assign({},this.advancedMatchingAvailableProperties,t)},o.isOnsite=function(){return!1},o.isPartnerReporter=function(){return!1},o.getReportResultSet=function(){return this.reportResultSet},o.getUserInfo=function(t){return{}},o.getReporterMatchedUserFormatInfo=function(){return{}},o.getReporterMatchedUserFormatInfoV2=function(){return{}},o.getReportEventHistoryKey=function(t){return"tiktok"},o.getCookieBasedSession=function(){return this.session},o.setCookieBasedSession=function(t){t&&(this.session=t)},o.clearHistory=function(){this.reportEventHistory={}},o.pushReport=function(t,e){void 0===e&&(e="tiktok"),!this.reportEventHistory[e]&&(this.reportEventHistory[e]=[]),this.reportEventHistory[e].push(t)},o.hasReportEventHistory=function(t,e){var n=this.getReportEventHistoryKey(e);return this.reportEventHistory[n]?!!(tD.includes(t)&&this.reportEventHistory[n].includes(t))||!1:(this.reportEventHistory[n]=[],!1)},o.page=function(t){void 0===t&&(t={})},o.track=function(t,e,n,r,o){var i=this,a=r||oE.TRACK,c=o||ob.defaultReport;return!this.reportService||this.hasReportEventHistory(t,c)?Promise.resolve(null):(this.pushReport(t,this.getReportEventHistoryKey(c)),this.reportService.reportPreTasks.then(function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"track_after_report_preposition",functionName:window.ttq._pf_tn&&"track_after_report_preposition",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_after_report_preposition")}}catch(t){}var o=i.getReporterId(),s=i.trackSync(o,t,e,n,a,c);i.trackPostTask({reporterId:o,eventType:t,properties:e,eventConfig:n,type:a,reportType:c,reportData:s});try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"track_after_report_preposition"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return Promise.resolve({reporterId:o,eventType:t,properties:e,eventConfig:n,type:a,reportType:c,reportData:s})}))},o.getEventType=function(t){return t},o.trackPostTask=function(t){},o.trackSync=function(t,e,n,r,o,i,a){void 0===o&&(o=oE.TRACK),void 0===i&&(i=ob.defaultReport);var c=o!==oE.SELFHOST?this.assemblyData(t,e,n,r,o):this.assemblySelfHostData(t,e,n,r,o),s=a||tU(o,t);if(null!==s&&!!this.reportService)return this.emit("beforeReport",t,e,c,r,o),this.reportResultSet.push(this.reportService.report(s,c,i,oO.P0)),c},o.handlePropertiesToOptions=function(t,e){var n={};return e.forEach(function(e){n[e]=t[e],delete t[e]}),n},o.assemblyData=function(t,e,n,r,o){void 0===n&&(n={}),void 0===r&&(r={}),void 0===o&&(o=oE.TRACK);var i,a=this.context.getAllData(),c=a.adInfo,s=a.userInfo,h=a.appInfo,d=a.pageSign,_=a.libraryInfo,v=a.pageInfo,g=a.signalType,m=d.sessionId,y=d.variationId,E=Object.assign({},n),w=E&&E.pixelMethod||"";E&&E.pixelMethod&&delete E.pixelMethod;var b=Object.assign({},_,{version:this.context.isLegacyPixel(t)?"legacy-"+_.version:_.version}),O=Object.assign({},(0,f.mQ)(c,tx),{device_id:h.device_id,uid:h.user_id}),I=this.handlePropertiesToOptions(E,[ow.LDU,ow.EVENTID,ow.EVENT_ID]),T=this.options.limited_data_use,S=null!==I.limited_data_use&&void 0!==I.limited_data_use?I.limited_data_use:T;null==S?delete I.limited_data_use:I.limited_data_use=!!S;var N=r&&(r.event_id||r.eventID)||"";I.event_id=N||I.event_id||I.eventID||"",delete I.eventID;var R=this.getReporterInfo();R.pixel&&(R.pixel.runtime=(0,u.Tm)(),w&&(R.pixel.mode=w));var P=this.getUserInfo(tb.Ts.Manual)||{},A=this.getUserInfo(tb.Ts.ManualV2)||{},L=this.getReporterMatchedUserFormatInfoV2()||{},C=this.getUserInfo(tb.Ts.Auto)||{};C.auto_trigger_type&&(Object.assign(E,{auto_trigger_type:C.auto_trigger_type}),delete C.auto_trigger_type),(0,u.Dt)()&&Object.assign(E,{android_version:h.android_version,device_model:h.device_model});var x={};s.anonymous_id&&(x.anonymous_id=s.anonymous_id);var k=this.getEventType(e),D=o!==oE.AUTO_CONFIG&&(null===(i=this.reporterInfo)||void 0===i?void 0:i.firstPartyCookieEnabled)?tS(this.getCookieBasedSession(),this.context.getPageCookieBasedSession()):{};return Object.assign({event:k,event_id:N,message_id:(0,l.gj)((0,l.fV)(p.WO),t),is_onsite:!!g,timestamp:new Date().toJSON(),context:Object.assign(Object.assign(Object.assign({ad:O,device:{platform:h.platform},user:Object.assign({},x,P,A,C)},R),{page:Object.assign({},v),library:Object.assign({},b),session_id:(0,l.gj)(m,t),pageview_id:(0,l.gj)(this.context.getPageViewId(),this.getReporterUniqueLoadId(),p.n2),variation_id:y||""}),D),_inspection:L,properties:E},I)},o.assemblySelfHostData=function(t,e,n,r,o){return void 0===n&&(n={}),void 0===r&&(r={}),this.assemblyData(t,e,n,r,o)},e}(tj);tF=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],tF);var tV={ViewForm:"ViewContent",ViewConsultationPage:"ViewContent",ViewDownloadPage:"ViewContent",Checkout:"PlaceAnOrder",Registration:"CompleteRegistration",AddBilling:"AddPaymentInfo",StartCheckout:"InitiateCheckout",ClickInDownloadPage:"ClickButton",ClickInConsultationPage:"ClickButton",ClickForm:"ClickButton",ClickToDownload:"Download",Consult:"Contact",ConsultByPhone:"Contact",CompletePayment:"Purchase",SubmitForm:"Lead"},tG=["event_experiment","dynamic_parameter_config","eb_version","eb_rule_id","tf"],tH=function(t){var e=t.getUserInfo().anonymous_id,n=y(p.qC);e!==n&&(t.setUserInfoWithoutIdentifyPlugin({anonymous_id:n}),(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"undetected_cookie_set",extJSON:{message:e+"||"+n}}))},tB=function(t,e,n){try{var r=window.location.hostname.split(".");if(void 0!==oI&&oI<r.length){document.cookie=t+"="+e+h(Object.assign({},n,{domain:"."+r.slice(oI).join(".")}));return}for(var o=r.length-2;o>=0;o--){var i="."+r.slice(o).join(".");if(document.cookie=t+"="+e+h(Object.assign({},n,{domain:i})),y(t)===e){oI=o;break}}}catch(t){}},tK=function(t,e,n){var r,o=tO();return e&&(o=tT(e)),tB(t?p.ku+"_"+t:p.ku,(r=o).sessionId+"."+r.sessionCount+"."+r.lastEventTS,_(n)),o};function tW(t,e){return(tW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tY=function(t){function e(e){var n,r,o=e.id,i=e.type,a=e.isOnsitePage,c=e.context,s=e.reporterInfo,u=e.ttqOptions,f=e.reportService,l=e.plugins,p=e.rules,h=e.options;return(r=t.call(this,c,f)||this).ttp="",r.loaded=!1,r.id=o,r.pixelCode=o,r.type=i,r.isOnsitePage=a,r.options=(void 0===h?{}:h)||{},r.plugins=(void 0===l?{}:l)||{},r.rules=(void 0===p?[]:p)||[],r.reporterInfo=Object.assign(s||{},((n={})[i]=o,n)),r.ttp=u.ttp||"",r.currency_list=u.currency_list||null,r.ttqPartner=u.partner||"",r.selfHostConfig=u.self_host_config||{},r.ttqOptions=u,r.pixelPromise=r.getInstance(),r}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,tW(n,r);var n,r,o=e.prototype;return o.identify=function(t,e){var n=(0,f.oh)(t,e);this.context.setUserInfo(n)},o.getReporterId=function(){return this.id||""},o.getReporterUniqueLoadId=function(){return this.reporterInfo.loadId+"-"+this.getReporterId()},o.getReporterPartner=function(){var t;return(null===(t=this.reporterInfo)||void 0===t?void 0:t.partner)||""},o.setPixelInfo=function(t,e,n){var r,o=this.type;this.reporterInfo=Object.assign(this.reporterInfo,Object.assign({},t),((r={})[o]=this.getReporterId(),r)),e&&(this.rules=e),n&&(this.plugins=n)},o.getInstance=function(){return Promise.resolve(this)},o.getReporterInfo=function(){return this.reporterInfo.pixelCode?t.prototype.getReporterInfo.call(this):{shop_id:this.reporterInfo.shopId,eventSourceId:this.reporterInfo.eventSourceId}},o.getUserInfo=function(t){var e=this.context.getUserInfo(),n=(0,f.cm)(e,Object.assign({},this.advancedMatchingAvailableProperties));switch(t){case tb.Ts.Manual:return(0,f.cm)(this.isPartnerReporter()?n:e,{external_id:!0,email:!0,phone_number:!0,ttoclid:!0});case tb.Ts.ManualV2:return(0,f.cm)(this.isPartnerReporter()?n:e,{first_name:!0,last_name:!0,city:!0,state:!0,country:!0,zip_code:!0,partner_id:!0,ttoclid:!0});case tb.Ts.Auto:var r=(0,f.cm)(n,{external_id:!0,auto_email:!0,auto_phone_number:!0,ttoclid:!0});return Object.assign(r,(r.auto_email||r.auto_phone_number)&&e.auto_trigger_type?{auto_trigger_type:e.auto_trigger_type}:{});default:return n}},o.getReporterMatchedUserFormatInfo=function(){var t=this.context.getUserFormatInfo(),e=(0,f.QO)(t,this.isPartnerReporter()?this.advancedMatchingAvailableProperties:{external_id:!0,email:!0,phone_number:!0}),n=(0,f.cm)(t,{auto_email:!0,auto_phone_number:!0});return Object.keys(n).length>0&&(!e.identity_params&&(e.identity_params={}),Object.assign(e.identity_params,n)),e},o.getReporterMatchedUserFormatInfoV2=function(){var t=this.context.getUserFormatInfoV2(),e=this.isPartnerReporter()?this.advancedMatchingAvailableProperties:{external_id:!0,email:!0,phone_number:!0,first_name:!0,last_name:!0,city:!0,state:!0,country:!0,zip_code:!0,partner_id:!0};return(0,f.te)(t,e)},o.isOnsite=function(){var t;return!!(null===(t=this.reporterInfo)||void 0===t?void 0:t.is_onsite)},o.isPartnerReporter=function(){var t=this.getReporterPartner();return!!(t&&"None"!==t)},o.getSignalDiagnosticLabels=function(){var t=this.context.getSignalDiagnosticLabels();if(!t)return Object.assign({},tb.N4);var e=this.advancedMatchingAvailableProperties,n=e.email,r=e.phone_number,o=e.auto_email,i=e.auto_phone_number;n=!this.isPartnerReporter()||n,r=!this.isPartnerReporter()||r;var a=(0,f.cm)(t,{raw_email:n,raw_phone:r,hashed_email:n,hashed_phone:r,raw_auto_email:o,raw_auto_phone:i});return Object.assign({},tb.N4,a)},o.setCookieBasedSession=function(t){t&&(this.session=t)},o.assemblyData=function(e,n,r,o,i){void 0===r&&(r={}),void 0===o&&(o={}),void 0===i&&(i=oE.TRACK);var a,c,l,p=t.prototype.assemblyData.call(this,e,n,r,o,i);p.is_onsite=this.isOnsitePage.value;var h=(0,s.kW)(e)||this.ttqPartner;h&&(p.partner=h),p.signal_diagnostic_labels=this.getSignalDiagnosticLabels();var d=(0,s.ij)();d&&(p.context.userAgent=d);var _=function(){try{var t=document.readyState;if("loading"==t)return ov.LOADING;if("interactive"==t)return ov.INTERACTIVE;else if("complete"==t)return ov.COMPLETE;return ov.UNKNOWN}catch(t){return ov.UNKNOWN}}();return _&&(p.context.page.load_progress=_),p._inspection=(a=tG,c=p.properties,void 0===(l=p._inspection)&&(l={}),a.forEach(function(t){c.hasOwnProperty(t)&&(l[t]=c[t],delete c[t])}),l),i!==oE.PAGE&&(p._inspection.ppf=(0,s.c6)()),p._inspection.vids=this.context.getVids(),p.context.ad.sdk_env=(0,u.RA)(),p.context.ad.jsb_status=(0,u.Fn)(),(i===oE.INTERACTION||i===oE.PERFORMANCE||i===oE.PERFORMANCE_INTERACTION)&&!1===this.context.getEnableAdTracking()&&!this.isOnsitePage.value&&(p.context.user={},p.context.ad=this.context.getOffsiteAdInfo(),p.context.ad=(0,f.mQ)(p.context.ad,tx)),p},o.page=function(t){void 0===t&&(t={});var e=tn().url;if(e!==this.currentHref)this.currentHref=e,this.track(tk,t,{})},o.track=function(e,n,r,o,i){var a=this;void 0===n&&(n={}),void 0===r&&(r={}),void 0===o&&(o=oE.TRACK),void 0===i&&(i=ob.defaultReport);var c=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var c={taskName:window.ttq._pf_tn||"track_after_reporter_init",functionName:window.ttq._pf_tn&&"track_after_reporter_init",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_after_reporter_init")}}catch(t){}var s=a.getReporterId();if(tC.includes(e))return t.prototype.track.call(a,e,n,r,o,i);var u=Object.assign({},r);a.selfHostConfig[s]&&!r.eventID&&(u=Object.assign({},u,{eventID:(0,l.gj)((0,l.fV)(p.qf),s)}));try{window.ttq&&window.ttq._ppf&&(c.end=performance.now(),window.ttq._ppf.push(c),"track_after_reporter_init"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return t.prototype.track.call(a,e,n,u,o,i)};return this.loaded?c():this.getInstance().then(c)},o.getEventType=function(t){return tV[t]||t},o.trackSync=function(e,n,r,o,i,s,u){if(void 0===r&&(r={}),void 0===o&&(o={}),void 0===i&&(i=oE.TRACK),void 0===s&&(s=ob.defaultReport),"track"===i&&(0,a.um)(c.O.PIXEL_SEND,{pixelCode:e,extJSON:{event:n}}),i!==oE.TRACK){t.prototype.trackSync.call(this,e,n,r,o,i,s,u);return}r&&"string"==typeof r.currency&&(r.currency=r.currency.toUpperCase());var f,l=this.context.getTestID();if(l){var p=this.assemblyData(e,n,r,o);return p.tt_test_id=l,p.context.ad={},null===(f=this===null||void 0===this?void 0:this.reportService)||void 0===f||f.report(u||ts,p,ob.htmlHttpReport,oO.P0),p}if(r&&"object"==typeof r){var h,d,_,v=r,g=v.value,m=v.currency;if(void 0!==g&&!(!isNaN(h=g)&&h>=0))(0,a.um)(c.O.CUSTOM_ERROR,{pixelCode:e,custom_name:"invalid_value",extJSON:{event:n,value:g,currency:m}});if(void 0!==m&&(d=m,void 0===(_=this.currency_list)&&(_=null),!(_||tM).includes(d)))(0,a.um)(c.O.CUSTOM_ERROR,{pixelCode:e,custom_name:"invalid_currency",extJSON:{event:n,value:g,currency:m}})}return t.prototype.trackSync.call(this,e,n,r,o,i,s,u)},o.trackPostTask=function(t){var e=t.reporterId,n=t.eventType,r=t.properties,o=t.eventConfig;if(!tC.includes(n))this.selfHostConfig[e]&&!this.hasReportEventHistory(n,ob.htmlHttpReport)&&(this.pushReport(n,this.getReportEventHistoryKey(ob.htmlHttpReport)),this.trackSync(e,n,r,o,oE.SELFHOST,ob.htmlHttpReport))},o.getReportEventHistoryKey=function(t){return t===ob.htmlHttpReport?this.selfHostConfig[this.getReporterId()]:"tiktok"},o.assemblySelfHostData=function(t,e,n,r,o){void 0===n&&(n={}),void 0===r&&(r={});var i=this.assemblyData(t,e,n,r,o),a=this.ttp;return a&&(i.context.user.ttp=a),i},e}(tF);function tJ(t,e){return(tJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tX=function(t,e){return function(n,r){e(n,r,t)}},tZ=function(t){function e(e,n,r,o,i,a,c,s,u,f){return t.call(this,{id:e,type:n,isOnsitePage:r,context:o,reporterInfo:i,ttqOptions:a,reportService:c,plugins:s,rules:u,options:f})||this}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,tJ(n,r);var n,r,o=e.prototype;return o.getInstance=function(){var t,e=this;if(this.pixelPromise)return this.pixelPromise;var n=(t=this.id,S()[t]||{});return(0,s.cH)()||n&&n.info?(this.loaded=!0,this.pixelPromise=Promise.resolve(this)):(this.pixelPromise=new Promise(function(t,n){var r=tr();tL((0,f.hi)(e.id,(null==r?void 0:r.hostname)||"")).then(function(){e.loaded=!0,t(e)}).catch(function(t){e.pixelPromise=null,n(t)})}),this.pixelPromise)},o.setCookieBasedSession=function(e){var n=tK(this.pixelCode,e,this.ttqOptions);t.prototype.setCookieBasedSession.call(this,n)},o.track=function(e,n,r,o,i){if(void 0===n&&(n={}),void 0===r&&(r={}),void 0===o&&(o=oE.TRACK),void 0===i&&(i=ob.defaultReport),r&&r.pixel_code&&this.getReporterId()!==r.pixel_code)return Promise.resolve(null);if(this.reporterInfo.firstPartyCookieEnabled){var a=tI(document.cookie);this.setCookieBasedSession(a[this.getReporterId()]),this.context.setPageCookieBasedSession(a[""])}return t.prototype.track.call(this,e,n,r,o,i)},e}(tY);tZ=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),tX(0,z(L.M.ID)),tX(1,z(L.M.Type)),tX(2,z(L.M.IsOnsitePage)),tX(3,z(A.CONTEXT)),tX(4,z(L.M.Info)),tX(5,z(A.TTQ_GLOBAL_OPTIONS)),tX(6,z(A.REPORT_SERVICE)),tX(7,z(L.M.Plugins)),tX(7,$()),tX(8,z(L.M.Rules)),tX(8,$()),tX(9,z(L.M.Options)),tX(9,$())],tZ);function tQ(t,e){return(tQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tz=function(t){function e(){return t.apply(this,arguments)||this}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,tQ(n,r);var n,r,o=e.prototype;return o.getInstance=function(){return this.pixelPromise=Promise.resolve(this),this.pixelPromise},o.track=function(t,e,n){return void 0===e&&(e={}),void 0===n&&(n={}),R(this.getReporterId(),"track",[t,e,n]),Promise.resolve(null)},e}(tZ);function t$(){t$=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function t0(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}function t1(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){t0(i,r,o,a,c,"next",t)}function c(t){t0(i,r,o,a,c,"throw",t)}a(void 0)})}}function t2(t,e){return(t2=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var t3=function(t,e){return function(n,r){e(n,r,t)}},t6=function(t){function e(e,n,r,o,i,c,s,u,f,l,p,h,d,_,v,g,m,y,E,w,b,O,I){(T=t.call(this,e,i)||this).context=e,T.env=u,T.reporters=n,T.cookieService=c,T.reportService=i,T.consentService=s,T.adService=r,T.appService=o,T.historyObserver=O,T.autoAdvancedMatchingPlugin=l,T.callbackPlugin=p,T.identifyPlugin=h,T.monitorPlugin=f,T.webFLPlugin=d,T.shopifyPlugin=_,T.autoConfigPlugin=v,T.diagnosticsConsolePlugin=g,T.competitorInsightPlugin=m,T.pangleCookieMatchingPlugin=y,T.eventBuilderPlugin=E,T.pagedataPlugin=b,T.enrichIpv6Plugin=w,T.runtimeMeasurementPlugin=I,T.historyObserver&&T.useObserver(T.historyObserver),T.autoAdvancedMatchingPlugin&&T.usePlugin(T.autoAdvancedMatchingPlugin),T.callbackPlugin&&T.usePlugin(T.callbackPlugin),T.identifyPlugin&&T.usePlugin(T.identifyPlugin),T.monitorPlugin&&T.usePlugin(T.monitorPlugin),T.webFLPlugin&&T.usePlugin(T.webFLPlugin),T.shopifyPlugin&&T.usePlugin(T.shopifyPlugin),T.autoConfigPlugin&&T.usePlugin(T.autoConfigPlugin),T.diagnosticsConsolePlugin&&T.usePlugin(T.diagnosticsConsolePlugin),T.competitorInsightPlugin&&T.usePlugin(T.competitorInsightPlugin),T.pangleCookieMatchingPlugin&&T.usePlugin(T.pangleCookieMatchingPlugin),T.eventBuilderPlugin&&T.usePlugin(T.eventBuilderPlugin),T.enrichIpv6Plugin&&T.usePlugin(T.enrichIpv6Plugin),T.runtimeMeasurementPlugin&&T.usePlugin(T.runtimeMeasurementPlugin),T.monitorPlugin&&(a.qe.info.forEach(function(t){var e;null===(e=T.monitorPlugin)||void 0===e||e.info(t.event,t.detail,t.withoutJSB)}),a.qe.error.forEach(function(t){var e;null===(e=T.monitorPlugin)||void 0===e||e.error(t.event,t.err,t.detail,t.withoutJSB)}),a.qe.info=[],a.qe.error=[]),T.dispatch(o_.INIT_START),T.pagedataPlugin&&T.usePlugin(T.pagedataPlugin),T.onPageLoaded(),T.onPageLeave();var T,S=tn(),N=S.url,R=S.referrer;return T.init(N,R),T.setPageInfo(N,R),T.dispatch(o_.INIT_END),T}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,t2(n,r);var n,r,o,i=e.prototype;return i.initAdInfo=function(t,e){this.dispatch(o_.BEFORE_AD_INFO_INIT_START);var n=to(p.dm);if(n){this.initAdCache(n);return}if((0,u.zP)()){var r=tm(t,e);r&&(r.creative_id&&r.log_extra||r.callback)&&(this.dispatch(o_.AD_INFO_INIT_START),ta(p.dm,r),this.setAdInfo(r),this.initOffsiteAdInfo(r));return}this.initBaseAdInfo(t,e)},i.initAdCache=function(t){this.dispatch(o_.AD_INFO_INIT_START),t.ad_info_from="cache",t.ad_info_status="fulfilled(cache)",this.setAdInfo(t),this.initOffsiteAdInfo(t)},i.initBaseAdInfo=function(t,e){var n=this;this.adService.webBridgeService.jsbridge&&this.dispatch(o_.AD_INFO_INIT_START),this.reportService.pushPreposition(t1(t$().mark(function r(){var o;return t$().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,n.adService.getAdInfo(t,e);case 3:o=r.sent,n.context.setAdInfo(o),n.initOffsiteAdInfo(o),r.next=11;break;case 8:r.prev=8,r.t0=r.catch(0),(0,a.vU)(c.O.INIT_ERROR,r.t0,{extJSON:{position:"initAdInfo"}});case 11:case"end":return r.stop()}},r,null,[[0,8]])}))())},i.initOffsiteAdInfo=function(t){var e=tE(t,function(t){(0,a.vU)(c.O.INIT_ERROR,t,{extJSON:{position:"handleAdInfoOfficial"}})});this.context.setOffsiteAdInfo(e);var n=ty(t,this.env);this.context.setEnableAdTracking(n),this.dispatch(o_.AD_INFO_INIT_END,{extJSON:{enabledAdTracking:n}})},i.initAppInfo=function(t,e){var n=this,r=to(p.Cd);if(r){this.context.setAppInfo(r);return}this.reportService.pushPreposition(t1(t$().mark(function r(){var o;return t$().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,n.initBaseAppInfo(t,e);case 2:return o=r.sent,r.abrupt("return",o);case 4:case"end":return r.stop()}},r)}))())},i.initBaseAppInfo=(o=t1(t$().mark(function t(e,n){var r;return t$().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.appService.getAppInfo(e,n);case 2:return r=t.sent,this.context.setAppInfo(r),t.abrupt("return",r);case 5:case"end":return t.stop()}},t,this)})),function(t,e){return o.apply(this,arguments)}),i.initTestId=function(t,e){if(!this.context.getTestID()){var n=tw(t,y("tt_test_id"),e);this.context.setTestID(n)}},i.initUserInfo=function(){this.setCookieInfo()},i.initLocalServiceInfo=function(){var t,e,n=to(p.RC);if(n){this.context.setUserInfoWithoutIdentifyPlugin(((t={})[p.RC]=n,t));return}var r=tn(),o=r.url,i=r.referrer,a=te(p.bO[location.hostname]?p.bO[location.hostname]:p.RC,o,i);a&&(ta(p.RC,a),this.context.setUserInfoWithoutIdentifyPlugin(((e={})[p.RC]=a,e)))},i.setPageIndex=function(t){t&&w(t)},i.instance=function(t){this.beforeAPIExecution();var e=this.getReporter(t);return e?e:new tz(t,od.PIXEL_CODE,{value:!1},this.context,{pixelCode:t},{},{})},i.instances=function(){return this.beforeAPIExecution(),this.reporters},i.page=function(e){this.beforeAPIExecution();var n=tn(),r=n.url,o=n.referrer;t.prototype.page.call(this,Object.assign({url:(null==e?void 0:e.page)||r,referrer:(null==e?void 0:e.referrer)||o},e))},i.track=function(e,n,r){void 0===n&&(n={}),void 0===r&&(r={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var o={taskName:window.ttq._pf_tn,functionName:"web_track_handler",start:performance.now()}}catch(t){}this.beforeAPIExecution();var i=r.pixel_code;if(void 0===i&&t.prototype.track.call(this,e,n,r),void 0!==i){var a=this.instance(i);!(a instanceof tz)&&a.track(e,n,r)}try{window.ttq&&window.ttq._ppf&&(o.end=performance.now(),window.ttq._ppf.push(o))}catch(t){}},i.identify=function(e,n){this.beforeAPIExecution(),t.prototype.identify.call(this,e,n)},i.setAdInfo=function(t){this.context.setAdInfo(t)},i.enableFirstPartyCookie=function(t){this.cookieService.enableFirstPartyCookie(t),t&&this.setCookieInfo()},i.enableCookie=function(){this.cookieService.enableFirstPartyCookie(!0),this.setCookieInfo(),this.cookieService.enableCookie()},i.disableCookie=function(){this.cookieService.disableCookie(),this.context.setUserInfoWithoutIdentifyPlugin({anonymous_id:void 0}),this.disablePangleCookie()},i.holdConsent=function(){this.consentService.setConsentMode(tb._R.HOLD)},i.revokeConsent=function(){this.consentService.setConsentMode(tb._R.REVOKE)},i.grantConsent=function(){this.consentService.setConsentMode(tb._R.GRANT)},i.disablePangleCookie=function(){this.pangleCookieMatchingPlugin&&this.pangleCookieMatchingPlugin.disablePangleCookie()},i.setAnonymousId=function(t){this.cookieService.setAnonymousId(t),this.initUserInfo()},i.resetCookieExpires=function(){this.cookieService.resetExpires()},i.setCookieInfo=function(){if(!this.cookieService.canUseCookie())return;var t=this.cookieService.getAnonymousId();if(!!t)this.context.setUserInfoWithoutIdentifyPlugin({anonymous_id:t})},i.onPageLoaded=function(){var t=this;window.addEventListener("load",function(){t.dispatch(o_.PAGE_DID_LOAD)},{once:!0})},i.onPageLeave=function(){var t=this,e=function(){var e=Date.now();t.dispatch(o_.PAGE_WILL_LEAVE,e),t.consentService.updateCache()};window.addEventListener("beforeunload",e,{once:!0}),(0,u.gn)()&&window.addEventListener("onpagehide"in window?"pagehide":"unload",e)},i.beforeAPIExecution=function(){try{var t,e;t=this.context,tn().url===t.getPageInfo().url||this.setPageInfo(tn().url),e=this.context,y(p.qC)===e.getUserInfo().anonymous_id||tH(this.context)}catch(t){(0,a.vU)(c.O.API_ERROR,t,{extJSON:{position:"beforeAPIExecution"}})}},i.loadPixel=function(t,e){if(!!t){if(this.reporters.find(function(e){return e.getReporterId()===t})){(0,a.ZK)(om.DUPLICATE_PIXEL_CODE);return}(0,s.Ie)().load(t,e||{})}},e}(tR),t5=t6=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),t3(0,z(A.CONTEXT)),t3(1,z(A.TTQ_REPORTERS)),t3(2,z(A.AD_SERVICE)),t3(3,z(A.APP_SERVICE)),t3(4,z(A.REPORT_SERVICE)),t3(5,z(A.COOKIE_SERVICE)),t3(6,z(A.CONSENT_SERVICE)),t3(7,z(A.ENV)),t3(8,z(A.MONITOR_PLUGIN)),t3(8,$()),t3(9,z(A.AUTO_ADVANCED_MATCHING_PLUGIN)),t3(9,$()),t3(10,z(A.CALLBACK_PLUGIN)),t3(10,$()),t3(11,z(A.IDENTIFY_PLUGIN)),t3(11,$()),t3(12,z(A.WEB_FL_PLUGIN)),t3(12,$()),t3(13,z(A.SHOPIFY_PLUGIN)),t3(13,$()),t3(14,z(A.AUTO_CONFIG_PLUGIN)),t3(14,$()),t3(15,z(A.DIAGNOSTICS_CONSOLE_PLUGIN)),t3(15,$()),t3(16,z(A.COMPETITOR_INSIGHT_PLUGIN)),t3(16,$()),t3(17,z(A.PANGLE_COOKIE_MATCHING_PLUGIN)),t3(17,$()),t3(18,z(A.EVENT_BUILDER_PLUGIN)),t3(18,$()),t3(19,z(A.ENRICH_IPV6_PLUGIN)),t3(19,$()),t3(20,z(A.PAGEDATA_PLUGIN)),t3(20,$()),t3(21,z(A.HISTORY_OBSERVER)),t3(21,$()),t3(22,z(A.RUNTIME_MEASUREMENT_PLUGIN)),t3(22,$())],t6),t8=n("118"),t4=function(){function t(t){this.userFormatInfo={},this.userFormatInfoV2={},this.enableAdTracking=!0,this.offsiteAdInfo={},this.tt_test_id="",this.signalDiagnosticLabels=Object.assign({},tb.N4),this.pageCookieBasedSession=tO(),this.init(t)}var e=t.prototype;return e.init=function(t){this.userInfo={},this.adInfo={},this.appInfo={},this.pageInfo={url:"",referrer:""},this.pageSign={sessionId:"",pageId:""},this.libraryInfo=t},e.getAllData=function(){return{userInfo:this.userInfo,adInfo:this.adInfo,appInfo:this.appInfo,libraryInfo:this.libraryInfo,pageInfo:this.pageInfo,pageSign:this.pageSign,signalType:this.signalType,userFormatInfo:this.userFormatInfo,userFormatInfoV2:this.userFormatInfoV2,enableAdTracking:this.enableAdTracking,offsiteAdInfo:this.offsiteAdInfo,tt_test_id:this.tt_test_id}},e.getLibraryInfo=function(){return this.libraryInfo},e.setSignalType=function(t){this.signalType=t},e.getSignalType=function(){return this.signalType},e.setTestID=function(t){this.tt_test_id=t},e.getTestID=function(){return this.tt_test_id},e.setEnableAdTracking=function(t){this.enableAdTracking=t},e.getEnableAdTracking=function(){return this.enableAdTracking},e.setOffsiteAdInfo=function(t){this.offsiteAdInfo=Object.assign({},this.offsiteAdInfo,t)},e.getOffsiteAdInfo=function(){return this.offsiteAdInfo},e.getUserFormatInfo=function(){return this.userFormatInfo},e.setUserFormatInfo=function(t){void 0===t&&(t={}),Object.assign(this.userFormatInfo,t)},e.getUserFormatInfoV2=function(){return this.userFormatInfoV2},e.setUserFormatInfoV2=function(t){void 0===t&&(t={}),Object.assign(this.userFormatInfoV2,t)},e.setUserInfo=function(t){void 0===t&&(t={}),Object.assign(this.userInfo,t)},e.setUserInfoWithoutIdentifyPlugin=function(t){if(!!t)Object.assign(this.userInfo,t)},e.getUserInfo=function(){return this.userInfo},e.getAdInfo=function(){return this.adInfo},e.setAdInfo=function(t){if(!!t)this.adInfo?this.adInfo=Object.assign({},this.adInfo,t):this.adInfo=t},e.getAppInfo=function(){return this.appInfo},e.setAppInfo=function(t){if(!!t)this.appInfo=Object.assign({},this.appInfo,t)},e.getPageInfo=function(){return this.pageInfo},e.getPageSign=function(){return this.pageSign},e.setPageInfo=function(t,e){var n=Object.assign({},this.pageInfo),r=Object.assign({},this.pageSign);if(n.url!==t){var o=n.url;if(void 0!==n.url&&(n.referrer=n.url),void 0!==e&&(n.referrer=e),void 0!==r.pageIndex){var i=r.pageIndex,a=i.index,c=i.sub,s=i.main;r.pageIndex={index:++a,sub:++c,main:s}}return n.url=t,this.pageInfo=n,this.pageSign=r,{from:o,pageIndex:r.pageIndex}}},e.setPageInfoData=function(t){this.pageInfo=Object.assign({},this.pageInfo,t)},e.getSessionIdFromCache=function(){return null},e.setSessionIdToCache=function(t){},e.setSignalDiagnosticLabels=function(t){Object.assign(this.signalDiagnosticLabels,t)},e.getSignalDiagnosticLabels=function(){return this.signalDiagnosticLabels},e.getPageId=function(t){return void 0===t&&(t=""+Date.now()),t+"-"+(0,f.$j)(5)},e.getPageViewId=function(){var t=this.pageSign,e=t.pageId,n=t.pageIndex;return""+e+(n?"."+n.main+"."+n.sub:"")},e.getVariationId=function(){return""},e.getVids=function(){return""},e.isLegacyPixel=function(t){return!1},e.initPageSign=function(){var t=this.getSessionIdFromCache();null===t&&(t=(0,l.fV)(p.Qu),this.setSessionIdToCache(t));var e={sessionId:t,pageId:(0,l.fV)(p.M0)};this.pageSign=e},e.getPageCookieBasedSession=function(){return this.pageCookieBasedSession},e.setPageCookieBasedSession=function(t){t&&(this.pageCookieBasedSession=t)},t}();t4=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],t4);function t9(t,e){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var t7=function(t,e){return function(n,r){e(n,r,t)}},et=function(t){function e(e,n,r,o,i){var a;return(a=t.call(this,e)||this).setSignalType(i||P.S.OFFSITE),a.pageSign={sessionId:"",pageId:"",variationId:"",vids:"",pageIndex:{main:-1,sub:-1,index:-1}},a.legacy=r.legacy||[],a.variationId=r.variation_id||"",a.vids=r.vids||"",a.serverUniqueId=r.server_unqiue_id||"",a.ttqOptions=r,a.reportService=n,a.initPageSign(),(0,u.XA)(o)&&(0,u.gn)()&&(a.enableAdTracking=!1),a.data=a,a}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,t9(n,r);var n,r,o=e.prototype;return o.getSessionIdFromCache=function(){var t=null;try{t=JSON.parse(sessionStorage.getItem(p.Bp)||"")}catch(t){}return t},o.setSessionIdToCache=function(t){ta(p.Bp,t)},o.getVariationId=function(){return this.variationId},o.getVids=function(){return this.vids},o.isLegacyPixel=function(t){return(0,s.FH)(t,this.legacy)},o.assignPageInfo=function(t){Object.assign(this.pageInfo,t)},o.getSessionIndex=function(){var t={main:-1,sub:-1,index:-1};try{var e=JSON.parse(sessionStorage.getItem(p.AU)||"{}");if(e)return Object.assign({},t,e)}catch(t){}return t},o.setUserInfo=function(t){var e=this;if(void 0===t&&(t={}),0!==Object.keys(t).length){var n={};Object.entries(t).forEach(function(t){var r,o=t[0],i=t[1];if(!!i){if(o===t8.Sk){e.setUserInfoWithoutIdentifyPlugin(((r={})[t8.Sk]=i,r));return}n[o]=String(i).trim()}});var r=(0,s.Ie)(),o=null==r?void 0:r.getPlugin("Identify");o&&this.reportService.pushPreposition(o.handleUserProperties(n,t).then(function(t){void 0===t&&(t={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"identify_after_encryption",functionName:window.ttq._pf_tn&&"identify_after_encryption",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_after_encryption")}}catch(t){}var i=t,a=i.userProperties,c=i.userDataFormat,s=i.userDataFormatV2;if(a){Object.assign(e.userInfo,a);var u=e.getUserFormatInfo()||{},f=e.getUserFormatInfoV2()||{},l=e.getSignalDiagnosticLabels()||{};if(e.setUserFormatInfo(Object.assign({},u,c)),e.setUserFormatInfoV2(Object.assign({},f,s)),e.setSignalDiagnosticLabels(Object.assign({},l,t.identifierLabel||{})),0===Object.keys(e.userInfo).length||1===Object.keys(n).length&&Object.keys(n).includes("external_id"))return;var p=o.reporters[0]||null,h=p?Object.keys(Object.assign({},p.getUserInfo(tb.Ts.Manual),p.getUserInfo(tb.Ts.Auto))):[];p&&h.length&&p.track("EnrichAM",{},{},oE.TRACK)}try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"identify_after_encryption"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}}).catch(function(t){(0,a.vU)(c.O.API_ERROR,t,{extJSON:{api:"identify"}})}))}},o.initPageSign=function(){var t=this.getSessionIdFromCache();null===t&&(t=(0,f.LT)(this.serverUniqueId),this.setSessionIdToCache(t));var e=this.getPageId((0,f.eD)(t)),n=this.getVariationId(),r=this.getSessionIndex();r.main++,this.pageSign={sessionId:t,pageId:e,variationId:n,pageIndex:r}},o.setPageCookieBasedSession=function(e){var n=tK("",e,this.ttqOptions);t.prototype.setPageCookieBasedSession.call(this,n)},e}(t4);et=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),t7(0,z(L.M.WebLibraryInfo)),t7(1,z(A.REPORT_SERVICE)),t7(2,z(A.TTQ_GLOBAL_OPTIONS)),t7(3,z(A.ENV)),t7(3,$()),t7(4,z(L.M.SignalType)),t7(4,$())],et);var ee=String.fromCharCode.bind(String),en=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),er=function(t){for(var e,n,r,o,i="",a=t.length%3,c=0;c<t.length;){if((n=t.charCodeAt(c++))>255||(r=t.charCodeAt(c++))>255||(o=t.charCodeAt(c++))>255)throw TypeError("invalid character found");i+=en[(e=n<<16|r<<8|o)>>18&63]+en[e>>12&63]+en[e>>6&63]+en[63&e]}return a?i.slice(0,a-3)+"===".substring(a):i},eo=function(t){if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?ee(192|e>>>6)+ee(128|63&e):ee(224|e>>>12&15)+ee(128|e>>>6&63)+ee(128|63&e)}var n=65536+(t.charCodeAt(0)-55296)*1024+(t.charCodeAt(1)-56320);return ee(240|n>>>18&7)+ee(128|n>>>12&63)+ee(128|n>>>6&63)+ee(128|63&n)},ei=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,ea=function(t){return er(t.replace(ei,eo)).replace(/=/g,"").replace(/[+\/]/g,function(t){return"+"===t?"-":"_"})},ec=function(t){return ea(JSON.stringify(t))};function es(){es=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function eu(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}var ef=function(){function t(t,e,n){this.httpService=t,this.bridgeService=e,this.reportPreTasks=n}var e,n,r=t.prototype;return r.pushPreposition=function(t){this.reportPreTasks.push(t)},r.report=(n=(e=es().mark(function t(e,n,r,o){return es().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Promise.resolve());case 1:case"end":return t.stop()}},t)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(t){eu(i,r,o,a,c,"next",t)}function c(t){eu(i,r,o,a,c,"throw",t)}a(void 0)})}),function(t,e,r,o){return n.apply(this,arguments)}),t}();ef=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],ef);var el=function(t,e){var n=new URL(t);return Object.keys(e).forEach(function(t){var r=e[t].toJSON?e[t].toJSON():String(e[t]);n.searchParams.set(t,r)}),n.toString()},ep=function(t){var e=Array(t.length),n=0;return new Promise(function(r,o){for(var i=function(o){var i=t[o];i&&"function"==typeof i.then?i.then(function(i){e[o]={status:"fulfilled",value:i},++n===t.length&&r(e)}).catch(function(i){e[o]={status:"rejected",reason:i},++n===t.length&&r(e)}):(e[o]={status:"fulfilled",value:i},++n===t.length&&r(e))},a=0;a<t.length;a++)i(a)})};(r8=oT||(oT={}))[r8.PENDING=0]="PENDING",r8[r8.FULFILLED=1]="FULFILLED";var eh=function(){function t(){this.promiseQueue=[],this.status=oT.FULFILLED}var e=t.prototype;return e.push=function(t){this.status=oT.PENDING,this.promiseQueue.push(t)},e.then=function(t){var e,n=this;if(this.status===oT.FULFILLED)return Promise.resolve(t());return(e=this.promiseQueue,"function"==typeof Promise.allSettled?Promise.allSettled(e):ep(e)).then(function(){return n.status=oT.FULFILLED,t()})},t}();function ed(){ed=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function e_(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}function ev(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){e_(i,r,o,a,c,"next",t)}function c(t){e_(i,r,o,a,c,"throw",t)}a(void 0)})}}function eg(t,e){return(eg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var em=function(t,e){return function(n,r){e(n,r,t)}},ey=function(t){function e(e,n,r,o){var i;return(i=t.call(this,e,n,new eh)||this).supportSendAnalyticsEvent=!0,i.consentService=r,i.consentService.on("queue",function(t){t.forEach(function(t){var e=t.url,n=t.data,r=t.type;i.report(e,n,r,oO.P0)})}),i.env=o,i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,eg(n,r);var n,r,o,i,s,l=e.prototype;return l.send=(o=ev(ed().mark(function t(e,n,r){var o,i,s,l,p,h,d;return ed().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.bridgeService.jsbridge){t.next=2;break}return t.abrupt("return");case 2:return h=!!(p=n).context&&(null===(i=null===(o=p.context)||void 0===o?void 0:o.ad)||void 0===i?void 0:i.ad_info_status)==="timeout",d={},t.prev=5,t.next=8,this.bridgeService.send(p,h);case 8:if(!(!(d=t.sent)||1!==d.code)){t.next=11;break}throw Error("[fetch bridge] sendLog error: code "+(d&&d.code)+", data: "+(d&&JSON.stringify(d)));case 11:return(0,f.WZ)(p.event)&&(0,a.um)(c.O.JSB_SEND,{pixelCode:null===(s=p.context.pixel)||void 0===s?void 0:s.code,app_name:(0,u.bp)()?"ultralite":"",extJSON:{event:p.event,event_id:p.event_id,need_inject_ad_info:h}}),t.abrupt("return",d);case 15:t.prev=15,t.t0=t.catch(5),(0,f.WZ)(p.event)&&(0,a.vU)(c.O.JSB_ERROR,t.t0,{pixelCode:null===(l=p.context.pixel)||void 0===l?void 0:l.code,custom_name:"sendReportData",custom_enum:d&&d.code?""+d.code:"non",app_name:(0,u.PO)()||"",extJSON:{position:"sendReportData"}}),(0,u.bp)()&&(0,u.Dt)()&&this.sendHttpReport(e,p,r);case 19:case"end":return t.stop()}},t,this,[[5,15]])})),function(t,e,n){return o.apply(this,arguments)}),l.sendHttpReport=(i=ev(ed().mark(function t(e,n,r,o,i){var s;return ed().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===o&&(o=!0),t.next=3,this.httpService.send(e,n,i);case 3:!t.sent&&this.httpService.sendByImage(e,{analytics_message:r}),o&&(0,a.um)(c.O.HTTP_SEND,{pixelCode:null===(s=n.context.pixel)||void 0===s?void 0:s.code,extJSON:{event:n.event,event_id:n.event_id}});case 6:case"end":return t.stop()}},t,this)})),function(t,e,n,r,o){return i.apply(this,arguments)}),l.beforeReport=function(t,e,n){void 0===n&&(n=ob.defaultReport);var r=this.consentService.getConsentMode();return r!==tb._R.REVOKE&&(r!==tb._R.HOLD||(this.consentService.cacheReportTask(t,e,n),!1))},l.report=(s=ev(ed().mark(function t(e,n,r,o){var i,s,l,p;return ed().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===r&&(r=ob.defaultReport),void 0===o&&(o=oO.P0),!(o!==oO.P0)){t.next=8;break}return t.next=5,(0,f._v)(0);case 5:return t.next=7,this.report(e,n,r,oO.P0);case 7:case 15:return t.abrupt("return",t.sent);case 8:if(this.beforeReport(e,n,r)){t.next=11;break}return t.abrupt("return");case 11:if(i=ec(n),!(r===ob.defaultReport&&this.bridgeService.jsbridge)){t.next=16;break}return t.next=15,this.send(e,n,i);case 16:if(!(r===ob.httpReport&&this.bridgeService.jsbridge&&(0,u.XA)(this.env)&&!(0,u.NC)()&&this.supportSendAnalyticsEvent)){t.next=35;break}return s=e,t.prev=18,s=new URL(e).pathname,t.next=22,this.bridgeService.sendAnalyticsEvent({path:s,method:"POST",data:n});case 22:if(p=Error("sendAnalyticsEvent not support: code "+(l=t.sent)+", path: "+s+", data: "+JSON.stringify(n)),!(null==l||-2===l)){t.next=27;break}throw this.supportSendAnalyticsEvent=!1,p;case 27:if(1!==l){t.next=29;break}return t.abrupt("return");case 29:throw p;case 32:t.prev=32,t.t0=t.catch(18),(0,a.vU)(c.O.CUSTOM_ERROR,t.t0,{custom_name:"sendAnalyticsEvent",custom_enum:String(l)},!0);case 35:this.sendHttpReport(e,n,i,!!((0,f.WZ)(n.event)&&(0,f.Pm)(n)),n.action?3:void 0);case 36:case"end":return t.stop()}},t,this,[[18,32]])})),function(t,e,n,r){return s.apply(this,arguments)}),l.reportFL=function(t){this.bridgeService.jsbridge&&this.bridgeService.updateWebFlData(t)},e}(ef);function eE(){eE=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function ew(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}ey=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),em(0,z(A.HTTP_SERVICE)),em(1,z(A.BRIDGE_SERVICE)),em(2,z(A.CONSENT_SERVICE)),em(3,z(A.ENV)),em(3,$())],ey);var eb=function(){function t(t){this.webBridgeService=t}var e,n,r=t.prototype;return r.getAdInfo=(n=(e=eE().mark(function t(e,n){var r,o,i;return eE().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.getAdInfoFromURL(e,n),!this.webBridgeService.jsbridge){t.next=7;break}return t.next=4,this.webBridgeService.getAdInfo();case 4:t.t0=t.sent,t.next=8;break;case 7:t.t0={};case 8:return o=t.t0,(i=Object.assign({},r,o))&&(i.creative_id&&i.log_extra||i.callback)&&ta(p.dm,i),t.abrupt("return",i);case 12:case"end":return t.stop()}},t,this)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(t){ew(i,r,o,a,c,"next",t)}function c(t){ew(i,r,o,a,c,"throw",t)}a(void 0)})}),function(t,e){return n.apply(this,arguments)}),r.getAdInfoFromURL=function(t,e){return tm(t,e)},t}();function eO(){eO=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function eI(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}eb=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),(r4=0,r9=z(A.BRIDGE_SERVICE),function(t,e){r9(t,e,0)})],eb);var eT=function(){function t(t){this.webBridgeService=t}var e,n,r=t.prototype;return r.getAppInfo=(n=(e=eO().mark(function t(e,n){var r,o,i,a;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if((r=this.getAppInfoFromURL(e,n)).platform=(0,u.Xf)(),!(0,u.Dt)()){t.next=10;break}return t.next=5,(0,u.sH)();case 5:i=(o=t.sent).model,a=o.platformVersion,r.device_model=i,r.android_version=a;case 10:return!(0,l.Qr)(r)&&ta(p.Cd,r),t.abrupt("return",r);case 12:case"end":return t.stop()}},t,this)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(t){eI(i,r,o,a,c,"next",t)}function c(t){eI(i,r,o,a,c,"throw",t)}a(void 0)})}),function(t,e){return n.apply(this,arguments)}),r.getAppInfoFromURL=function(t,e){try{var n=te(tg,t,e),r=n&&JSON.parse(n),o=r.device_id,i=r.uid;return{device_id:o,user_id:i}}catch(t){return{}}},t}();eT=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),(r7=0,ot=z(A.BRIDGE_SERVICE),function(t,e){ot(t,e,0)})],eT);var eS="ad_analytics_msg";function eN(){eN=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function eR(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}function eP(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){eR(i,r,o,a,c,"next",t)}function c(t){eR(i,r,o,a,c,"throw",t)}a(void 0)})}}var eA=function(t,e){return function(n,r){e(n,r,t)}},eL=function(){function t(t,e){this.env=t,(0,u.Z0)(this.env)&&(this.jsbridge=e),this.bridgeTimeout=400}var e,n,r,o,i,s,l,p=t.prototype;return p.getAdInfo=(e=eP(eN().mark(function t(){var e=this;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.jsbridge){t.next=3;break}return(0,a.vU)(c.O.JSB_ERROR,Error("tt bridge error when getting ad info"),{extJSON:{position:"getAdInfo"}}),t.abrupt("return",Promise.resolve({}));case 3:return t.abrupt("return",new Promise(function(){var t=eP(eN().mark(function t(n){var r;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.callAdInfo();case 3:(r=t.sent).ad_info_from="jsb",r.ad_info_status=r.ad_info_status||"fulfilled",n(r),t.next=13;break;case 9:t.prev=9,t.t0=t.catch(0),n({}),(0,a.vU)(c.O.JSB_ERROR,t.t0,{extJSON:{position:"getAdInfo"}});case 13:case"end":return t.stop()}},t,null,[[0,9]])}));return function(e){return t.apply(this,arguments)}}()));case 4:case"end":return t.stop()}},t,this)})),function(){return e.apply(this,arguments)}),p.callAdInfo=(n=eP(eN().mark(function t(){var e,n;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.call("adInfo",{},(0,u.zd)()?3500:5e3);case 3:if((e=t.sent).data){t.next=6;break}return t.abrupt("return",Promise.reject("adInfo no data"));case 6:return n={creative_id:e.data.cid,log_extra:e.data.log_extra},t.abrupt("return",n);case 10:if(t.prev=10,t.t0=t.catch(0),"JSBRIDGE TIMEOUT"!==t.t0){t.next=17;break}return(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"ad_info_init_timeout"}),t.abrupt("return",{ad_info_status:"timeout"});case 17:return(0,a.vU)(c.O.JSB_ERROR,t.t0,{extJSON:{position:"getAdInfo"}}),t.abrupt("return",{});case 19:case"end":return t.stop()}},t,this,[[0,10]])})),function(){return n.apply(this,arguments)}),p.getAppInfo=(r=eP(eN().mark(function t(){return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Promise.resolve({}));case 1:case"end":return t.stop()}},t)})),function(){return r.apply(this,arguments)}),p.send=(o=eP(eN().mark(function t(e,n){var r,o,i,s,l,p,h,d,_,v,g,m;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.jsbridge){t.next=2;break}return t.abrupt("return",Promise.resolve());case 2:return i=(null===(o=null===(r=null==e?void 0:e.context)||void 0===r?void 0:r.ad)||void 0===o?void 0:o.creative_id)||"0",s=ec(e),l=(0,f.Xu)(e),p={analytics_message:s,trackLogData:JSON.stringify(e),category:"ad_analytics_msg",tag:eS,label:l},(0,u.NC)()&&(p.is_ad_event=1),_={eventName:eS,labelName:l,value:i,extValue:"0",extJson:p},"insight_log_monitor"===l&&(0,u.z1)()?(d="x.reportAppLog",v={eventName:"insight_log_monitor",params:p},h=this.call("x.reportAppLog",v,this.bridgeTimeout)):(0,u.zP)()||(0,f.iE)(e)?(d="sendLog",h=this.call("sendLog",_,this.bridgeTimeout)):(0,u.XA)(this.env)?(0,u.gn)()&&n?(g={eventName:l,params:p},d="sendLogWithAdInfo",h=this.call("sendLogWithAdInfo",g,this.bridgeTimeout)):(d="sendLog",h=this.call("sendLog",_,this.bridgeTimeout)):(m={event_name:l,version:2,properties:p},d="track_event",h=this.call("track_event",m,400)),setTimeout(function(){(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"send_report_data",extJSON:{api_name:d}})}),t.abrupt("return",h);case 11:case"end":return t.stop()}},t,this)})),function(t,e){return o.apply(this,arguments)}),p.call=(i=eP(eN().mark(function t(e,n,r,o){var i=this;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===n&&(n={}),void 0===r&&(r=400),void 0===o&&(o=!0),t.abrupt("return",new Promise(function(t,s){var u;if(!i.jsbridge){s("JSBRIDGE ERROR"),o&&(0,a.vU)(c.O.JSB_ERROR,Error("JSBRIDGE ERROR"),{extJSON:{position:"getCallPromise"}});return}r>0&&(u=window.setTimeout(function(){s("JSBRIDGE TIMEOUT"),o&&(0,a.vU)(c.O.JSB_ERROR,Error("JSBRIDGE TIMEOUT"),{extJSON:{position:"getCallPromise",method:e}})},r)),i.jsbridge&&i.jsbridge.call&&i.jsbridge.call(e,n,function(e){t(function(t,e){void 0===e&&(e=!0);var n,r={};try{if("string"==typeof t)r.data=JSON.parse(t);else{;if((n=t).code&&n.data&&n.ret?0:1){if(void 0!==t.code){var o=Object.assign({},t),i=o.code;r.code=i,delete o.code,o.data?r.data=o.data:r.data=o}else r.data=t}else(r=t).__data&&(r.data=r.__data)}}catch(t){e&&(0,a.vU)(c.O.JSB_ERROR,t,{extJSON:{position:"getCallPromise bridge.call"}})}return r}(e,o)),window.clearTimeout(u)})}));case 4:case"end":return t.stop()}},t)})),function(t,e,n,r){return i.apply(this,arguments)}),p.sendAnalyticsEvent=(s=eP(eN().mark(function t(e){var n,r,o,i,a;return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.method,r=e.path,o=e.params,i=e.data,t.next=3,this.call("sendAnalyticsEvent",{method:n,path:r,params:o,data:i,header:{"Content-Type":"application/json"}},0,!1);case 3:return a=t.sent,t.abrupt("return",null==a?void 0:a.code);case 5:case"end":return t.stop()}},t,this)})),function(t){return s.apply(this,arguments)}),p.updateWebFlData=(l=eP(eN().mark(function t(e){return eN().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!((0,u.XA)(this.env)&&(0,u.gn)())){t.next=2;break}return t.abrupt("return",this.call("updateFLLocalConv",e,this.bridgeTimeout));case 2:case"end":return t.stop()}},t,this)})),function(t){return l.apply(this,arguments)}),t}();function eC(){eC=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function ex(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}eL=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),eA(0,z(A.ENV)),eA(0,$()),eA(1,z(A.JS_BRIDGE)),eA(1,$())],eL);var ek=function(){function t(){}var e,n,r=t.prototype;return r.send=(n=(e=eC().mark(function t(e,n,r){var o;return eC().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===r&&(r=0),t.prev=1,!(!navigator||!navigator.sendBeacon)){t.next=4;break}return t.abrupt("return",!1);case 4:if(!(!(o=navigator.sendBeacon(e,JSON.stringify(n)))&&"number"==typeof r&&r>0)){t.next=10;break}return r--,t.next=9,(0,f._v)(200);case 9:return t.abrupt("return",this.send(e,n,r));case 10:return t.abrupt("return",o);case 13:return t.prev=13,t.t0=t.catch(1),t.abrupt("return",!1);case 16:case"end":return t.stop()}},t,this,[[1,13]])}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(t){ex(i,r,o,a,c,"next",t)}function c(t){ex(i,r,o,a,c,"throw",t)}a(void 0)})}),function(t,e,r){return n.apply(this,arguments)}),r.sendByImage=function(t,e){new Image().src=el(t,e)},t}();function eD(){eD=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function eM(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}ek=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],ek);var ej=function(){function t(t){this.cookieExpireOption=_(t)}var e,n,r=t.prototype;return r.genCookieID=function(){return(0,f.d$)()},r.enableCookie=(n=(e=eD().mark(function t(){return eD().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return E(p.ct,"1",this.cookieExpireOption),t.abrupt("return",tL((0,f.TX)()));case 2:case"end":return t.stop()}},t,this)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(t){eM(i,r,o,a,c,"next",t)}function c(t){eM(i,r,o,a,c,"throw",t)}a(void 0)})}),function(){return n.apply(this,arguments)}),r.enableFirstPartyCookie=function(t){if(!!t){E(p.ct,"1",this.cookieExpireOption);var e=this.getAnonymousId();this.setAnonymousId(e||this.genCookieID())}},r.disableCookie=function(){E(p.ct,"0",this.cookieExpireOption),E(p.qC,"",Object.assign(this.cookieExpireOption,{expires:-1})),tL((0,f.X1)())},r.setAnonymousId=function(t){var e=this.getAnonymousId()||t;if(!!e){var n=e.split(".")[0];E(p.qC,n,this.cookieExpireOption)}},r.getAnonymousId=function(){return y(p.qC)||""},r.canUseCookie=function(){try{return"0"!==y(p.ct)}catch(t){}return!1},r.resetExpires=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"resetExpires",start:performance.now()}}catch(t){}var e=y(p.ct);e&&E(p.ct,e,this.cookieExpireOption);var n=this.getAnonymousId();n&&this.setAnonymousId(n);try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}},t}();ej=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),(oe=0,on=z(A.TTQ_GLOBAL_OPTIONS),function(t,e){on(t,e,0)})],ej),(or=oS||(oS={}))[or.P0=4]="P0",or[or.P1=3]="P1",or[or.P2=2]="P2",or[or.P3=1]="P3";function eU(t,e){return(eU=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var eq="tt_hold_events",eF=function(t){function e(){var e;return e=t.apply(this,arguments)||this,e.consentMode=tb._R.UNKNOWN,e.queue=[],e.debounceUpdateCache=(0,f.Ds)(function(){e.updateCache()},200,e),e.handleHistoryQueue=(0,f.IH)(function(){var t=to(eq);Array.isArray(t)&&(e.queue=e.queue.concat(t),e.changeQueueWithConsent())}),e}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,eU(n,r);var n,r,o=e.prototype;return o.on=function(e,n){t.prototype.on.call(this,e,n),this.handleHistoryQueue()},o.setConsentMode=function(t){this.consentMode=t,this.changeQueueWithConsent()},o.changeQueueWithConsent=function(){switch(this.consentMode){case tb._R.REVOKE:this.cleanQueue();break;case tb._R.GRANT:this.releaseQueue(),this.cleanQueue();case tb._R.HOLD:case tb._R.UNKNOWN:}},o.getConsentMode=function(){return this.consentMode},o.cacheReportTask=function(t,e,n){void 0===n&&(n=ob.defaultReport),this.queue.push({url:t,data:e,type:n}),this.debounceUpdateCache()},o.cleanQueue=function(){this.queue=[],ti(eq)},o.updateCache=function(){this.queue&&this.queue.length>0&&ta(eq,this.queue)},o.releaseQueue=function(){var t=this;this.queue.sort(function(e,n){return t.getEventPriority(n.data)-t.getEventPriority(e.data)}),this.emit("queue",this.queue)},o.getEventPriority=function(t){return t.event&&t.event.length>0?oS.P0:t.action&&t.action.length>0?oS.P1:""===t.event?oS.P2:oS.P3},e}(tj);eF=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],eF);var eV=function(){function t(t){var e=t.name,n=t.context,r=t.reporters;this.reporters=[],this.context=n,this.reporters=r,this.name=e}var e=t.prototype;return e.initStart=function(){},e.initEnd=function(){},e.adInfoInitStart=function(){},e.adInfoInitEnd=function(){},e.contextInitStart=function(){},e.contextInitEnd=function(){},e.pageUrlWillChange=function(t,e){},e.pageUrlDidChange=function(t,e){},e.pageDidLoad=function(){},e.pageWillLeave=function(t){},e.pixelSend=function(t,e,n,r,o){},e.pixelDidMount=function(t){},t}();eV=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q()],eV);function eG(t,e){return(eG=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var eH=function(t){var e,n;function r(){return t.apply(this,arguments)||this}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,eG(e,n),r}(eV);function eB(t,e){return(eB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var eK=function(t,e){return function(n,r){e(n,r,t)}},eW=function(t){var e,n;function r(e,n){var r;return(r=t.call(this,{name:"Callback",reporters:n,context:e})||this).ttclidCookieValue="undefined"!=typeof document?y(tv):"",r}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,eB(e,n),r.prototype.pixelDidMount=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"callback_plugin_pixelDidMount",start:performance.now()}}catch(t){}var n=tn(),r=te(tv,n.url,n.referrer);r&&this.ttclidCookieValue!==r&&d(tv,r);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},r}(eH);eW=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),eK(0,z(A.CONTEXT)),eK(1,z(A.TTQ_REPORTERS))],eW);var eY={isHash:function(t){return!1},genIdentifierLabelByUserProperties:function(t){return{}}},eJ={validatePhoneNumberLength:function(t){},parsePhoneNumberFromString:function(t){}},eX={tryDecodeHashedBase64String:function(t){return null},tryDecodeHashedBase64Hex:function(t){return null}},eZ={checkEmailFormat:function(t){return!1},checkMDNEmailFormat:function(t){return!1}},eQ=function(t){var e=t.parsePhoneNumberFromString,n=t.validatePhoneNumberLength;eJ.parsePhoneNumberFromString=e,eJ.validatePhoneNumberLength=n},ez=function(t){var e=t.isHash,n=t.genIdentifierLabelByUserProperties;eY.isHash=e,eY.genIdentifierLabelByUserProperties=n},e$=function(t){var e=t.tryDecodeHashedBase64String,n=t.tryDecodeHashedBase64Hex;eX.tryDecodeHashedBase64String=e,eX.tryDecodeHashedBase64Hex=n},e0=function(t){var e=t.checkEmailFormat,n=t.checkMDNEmailFormat;eZ.checkEmailFormat=e,eZ.checkMDNEmailFormat=n},e1=function(t){var e=t.parsePhoneNumberFromString,n=t.validatePhoneNumberLength,r=t.isHash,o=t.genIdentifierLabelByUserProperties,i=t.tryDecodeHashedBase64String,a=t.tryDecodeHashedBase64Hex,c=t.checkEmailFormat,s=t.checkMDNEmailFormat,u=t.sha256;e0({checkEmailFormat:c,checkMDNEmailFormat:s}),e$({tryDecodeHashedBase64String:i,tryDecodeHashedBase64Hex:a}),ez({isHash:r,genIdentifierLabelByUserProperties:o}),eQ({parsePhoneNumberFromString:e,validatePhoneNumberLength:n}),(0,f.ZV)(u)},e2=function(t,e,n){void 0===e&&(e=""),void 0===n&&(n=eJ.parsePhoneNumberFromString);var r=t,o=e?n(t,e):n(t);return o?r="86"===o.countryCallingCode?o.nationalNumber:o.number:t.replace(/[^0-9]/g,"").length>0&&(r=t.replace(/[^0-9]/g,"")),r},e3=["(null)","","''\"",void 0,"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855","eb045d78d273107348b0300c01d29b7552d622abbc6faf81b3ec55359aa9950c","not set",null,"6181738008c985a1b5f106b796c98e719efcc3c0ff68ddcd14a049825f4900a8","2a539d6520266b56c3b0c525b9e6128858baeccb5ee9b694a2906e123c8d6dd3","c6e52c372287175a895926604fa738a0ad279538a67371cd56909c7917e69ea1","None","74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b","f24f02d3c35894296522abac8c4b2439b1c1b650e1fb4c97c0f3c50b580b0a3c","no","a683c5c5349f6f7fb903ba8a9e7e55d0ba1b8f03579f95be83f4954c33e81098","f18a2548c063c5a2b1560c6f2b9ec44bf9ed9017884404016d74f330119aaefe","449f06574cd639e1826848ff5d70ba95904574be84f34e61baa526d517dfb493","fcbcf165908dd18a9e49f7ff27810176db8e9f63b4352213741664245224f8aa","NA","bc857c49633bbc75644c51f36b16b2f768cc0ee13f65402ec7c32c96308272dd","42cbf37902c6911d7b4e371fe8f8708a0ceda6946249d4a3e23de8d5e60ae8b7"];function e6(t,e){return(e6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var e5=function(t){function e(e){var n=e.name,r=e.context,o=e.reporters;return t.call(this,{name:n,reporters:o,context:r})||this}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,e6(n,r);var n,r,o=e.prototype;return o.setIdentifyUtils=function(t){var e=t.isHash,n=t.sha256,r=t.genIdentifierLabelByUserProperties,o=t.tryDecodeHashedBase64String,i=t.tryDecodeHashedBase64Hex,a=t.validatePhoneNumberLength,c=t.parsePhoneNumberFromString,s=t.checkEmailFormat,u=t.checkMDNEmailFormat,f=t.getCookieDeprecationLabel,l=t.getAllTopics;e1({isHash:e,sha256:n,genIdentifierLabelByUserProperties:r,tryDecodeHashedBase64String:o,tryDecodeHashedBase64Hex:i,validatePhoneNumberLength:a,parsePhoneNumberFromString:c,checkEmailFormat:s,checkMDNEmailFormat:u}),this.parsePhoneNumberFromString=c,this.checkMDNEmailFormat=u,this.checkEmailFormat=s,this.sha256=n,this.getCookieDeprecationLabel=void 0===f?function(){}:f,this.getAllTopics=void 0===l?function(){}:l},o.baseHandleUserProperties=function(t,e){var n=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"identify_encryption",functionName:window.ttq._pf_tn&&"identify_encryption",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_encryption")}}catch(t){}if(!!t){var o=this.identifyParamsFormattedInfo(t),i=this.identifyParamsFormattedInfoV2(t),a=eY.genIdentifierLabelByUserProperties(e);this.handlePIIDiagnostics(i),Object.entries(t).forEach(function(e){var r=e[0],o=e[1],a=void 0===o?"":o;if(!!a){var c=String(a);if(["email","phone_number","sha256_email","sha256_phone_number"].includes(r)){var s=n.getUserDataFormatInfoV2KeyName(r),u=eX.tryDecodeHashedBase64Hex(c);if(null!==u)t[r]=u,null!==s&&(i=n.updateUserDataFormatV2Label(s,tb.Vp.BASE64_HEX_HASHED,i));else{var f=eX.tryDecodeHashedBase64String(c);f&&(t[r]=f,null!==s&&(i=n.updateUserDataFormatV2Label(s,tb.Vp.BASE64_STRING_HASHED,i)))}}switch("zip_code"===r&&c&&(eY.isHash(c)?i=n.updateUserDataFormatV2Label("zip_code",tb.Vp.ZIP_CODE_IS_HASHED,i):(i=n.updateUserDataFormatV2Label("zip_code",tb.Vp.ZIP_CODE_IS_NOT_HASHED,i),n.isZipFromUs(t)?(t.zip_code=n.sha256(n.truncateString(c,5)),i=n.updateUserDataFormatV2Label("zip_code",tb.Vp.ZIP_CODE_IS_US,i)):(t.zip_code=n.sha256(c),i=n.updateUserDataFormatV2Label("zip_code",tb.Vp.ZIP_CODE_IS_NOT_US,i)))),r){case"email":t.email=eY.isHash(c)&&!n.checkEmailFormat(c)?c:n.sha256(n.handleEmail(c));break;case"phone_number":t.phone_number=eY.isHash(c)?c:n.sha256(n.handlePhoneNumber(c));break;case"auto_email":t.auto_email=n.sha256(n.handleEmail(c));break;case"auto_phone_number":t.auto_phone_number=n.sha256(n.handlePhoneNumber(c));break;case"first_name":t.first_name=eY.isHash(c)?c:n.sha256(c);break;case"last_name":t.last_name=eY.isHash(c)?c:n.sha256(c);break;case"city":t.city=n.truncateString(c,80);break;case"state":t.state=n.truncateString(c,80);break;case"country":t.country=n.truncateString(c,80);break;default:return}}}),t.sha256_email&&(t.email=this.handleCheckHashedEmailValue(String(t.sha256_email),o)),t.sha256_phone_number&&(t.phone_number=this.handleCheckHashedPhoneValue(String(t.sha256_phone_number),o));try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"identify_encryption"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return{userProperties:t,userDataFormat:o,userDataFormatV2:i,identifierLabel:a}}},o.identifyParamsFormattedInfo=function(t){var e=this,n={},r=/^sha256_(.*)$/;return Object.entries(t).forEach(function(t){var o=t[0],i=t[1],a=String(void 0===i?"":i),c=o.match(r);switch(o){case"email":e.handleEmailFormat(a,"email",n);break;case"phone_number":e.handlePhoneNumberFormat(a,"phone_number",n);break;case"auto_email":e.handleEmailFormat(a,"auto_email",n);break;case"auto_phone_number":e.handlePhoneNumberFormat(a,"auto_phone_number",n);break;case(c||{}).input:var s=null==c?void 0:c.pop();s&&t8.Ae.includes(s)&&(n[s]=[tb.hZ.HASHED]);break;case"first_name":case"last_name":case"city":case"state":case"country":case"zip_code":case"partner_id":e.handleNewPiisFormat(a,o,n);break;default:n[o]=[tb.hZ.CORRECT_FORMAT]}}),n},o.identifyParamsFormattedInfoV2=function(t){var e=this,n={};return Object.entries(t).forEach(function(t){var r=t[0],o=t[1],i=String(void 0===o?"":o);switch(r){case"email":e.handlePixelValidation(i,t8.X2.EMAIL_IS_HASHED,n);break;case"phone_number":e.handlePixelValidation(i,t8.X2.PHONE_IS_HASHED,n);break;case"sha256_email":e.handlePixelValidation(i,t8.X2.SHA256_EMAIL,n);break;case"sha256_phone_number":e.handlePixelValidation(i,t8.X2.SHA256_PHONE,n);break;case"first_name":case"last_name":case"city":case"state":case"country":case"zip_code":case"partner_id":break;default:n[r]=[tb.Vp.UNKNOWN_INVALID]}}),n},o.updateUserDataFormatV2Label=function(t,e,n){var r,o;return(null===n[t]||void 0===n[t]||(null===(r=n[t])||void 0===r?void 0:r.includes(tb.Vp.UNKNOWN_INVALID)))&&(n[t]=[]),null===(o=n[t])||void 0===o||o.push(e),n},o.getUserDataFormatInfoV2KeyName=function(t){return({email:"email_is_hashed",phone_number:"phone_is_hashed",sha256_email:"sha256_email",sha256_phone_number:"sha256_phone",zip_code:"zip_code"})[t]||null},o.handlePIIDiagnostics=function(t){},o.handleEmail=function(t){return t.toLowerCase()},o.handlePhoneNumber=function(t,e){return void 0===e&&(e=this.parsePhoneNumberFromString),e2(t,"",e)},o.handleCheckHashedEmailValue=function(t,e,n){return(void 0===n&&(n=this.checkEmailFormat),e.email=e.email||[],eY.isHash(t))?(null==e||e.email.push(tb.hZ.HASHED_CORRECT),t):n(t)?(null==e||e.email.push(tb.hZ.PLAINTEXT_EMAIL),this.sha256(this.handleEmail(String(t)))):(null==e||e.email.push(tb.hZ.HASHED_ERR),this.sha256(t))},o.handleCheckHashedPhoneValue=function(t,e,n){return(void 0===n&&(n=this.parsePhoneNumberFromString),e.phone_number=e.phone_number||[],eY.isHash(t))?(null==e||e.phone_number.push(tb.hZ.HASHED_CORRECT),t):n(t)?(e.phone_number.push(tb.hZ.PLAINTEXT_PHONE),this.sha256(this.handlePhoneNumber(String(t),n))):(null==e||e.phone_number.push(tb.hZ.HASHED_ERR),this.sha256(t))},o.handlePixelValidation=function(t,e,n){n[e]=[],e3.includes(t)&&n[e].push(tb.Vp.FILTER_EVENTS),t&&eY.isHash(t)&&n[e].push(tb.Vp.HASHED),t&&this.checkEmailFormat(t)&&n[e].push(tb.Vp.PLAIN_EMAIL),t&&this.checkMDNEmailFormat(t)&&n[e].push(tb.Vp.PLAIN_MDN_EMAIL),t&&this.parsePhoneNumberFromString(t)&&n[e].push(tb.Vp.PLAIN_PHONE),t&&0===n[e].length&&n[e].push(tb.Vp.UNKNOWN_INVALID)},o.isZipFromUs=function(t){var e;return(null===(e=t.country)||void 0===e?void 0:e.toLowerCase())==="us"},o.truncateString=function(t,e){var n=Array.from(t);return n.length<=e?t:n.slice(0,e).join("")},o.handlePhoneNumberFormat=function(t,e,n){var r=this.handleCheckPhoneNumber(String(t),this.parsePhoneNumberFromString);n[e]=r},o.handleCheckPhoneNumber=function(t,e){void 0===e&&(e=this.parsePhoneNumberFromString);var n=[];return t?eY.isHash(t)?(n.push(tb.hZ.HASHED),n):e(t)?(n.push(tb.hZ.CORRECT_FORMAT),n):(n.push(tb.hZ.WRONG_FORMAT),n):(n.push(tb.hZ.EMPTY_VALUE),n)},o.handleCheckEmail=function(t,e){void 0===e&&(e=this.checkEmailFormat);var n=[];return t?eY.isHash(t)?(n.push(tb.hZ.HASHED),n):e(t)?(n.push(tb.hZ.CORRECT_FORMAT),n):(n.push(tb.hZ.WRONG_FORMAT),n):(n.push(tb.hZ.EMPTY_VALUE),n)},o.handleEmailFormat=function(t,e,n){var r=this.handleCheckEmail(String(t),this.checkEmailFormat);!(n&&n[e]&&(n[e]||[]).includes(tb.hZ.HASHED))&&(n[e]=r)},o.handleNewPiisFormat=function(t,e,n){t&&(n[e]=[tb.hZ.CORRECT_FORMAT])},e}(eH);function e8(){e8=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(e,n,r,i){var a=Object._ttq_create((n&&n.prototype instanceof v?n:v).prototype);return o(a,"_invoke",{value:function(e,n,r){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=function e(n,r){var o=r.method,i=n.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),_;var a=l(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var c=a.arg;return c?c.done?(r[n.resultName]=c.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):c:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,_)}(c,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?d:"suspendedYield",u.arg===_)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=d,r.method="throw",r.arg=u.arg)}}}(e,r,new N(i||[]))}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="executing",d="completed",_={};function v(){}function g(){}function m(){}var y={};u(y,a,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(R([])));w&&w!==n&&r.call(w,a)&&(y=w);var b=m.prototype=v.prototype=Object._ttq_create(y);function O(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function I(t,e){var n;o(this,"_invoke",{value:function(o,i){function a(){return new e(function(n,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw TypeError(typeof e+" is not iterable")}return g.prototype=m,o(b,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=u(m,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object._ttq_create(b),t},e.awrap=function(t){return{__await:t}},O(I.prototype),u(I.prototype,c,function(){return this}),e.AsyncIterator=I,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new I(f(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},O(b),u(b,s,"Generator"),u(b,a,function(){return this}),u(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=R,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:R(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function e4(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}function e9(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){e4(i,r,o,a,c,"next",t)}function c(t){e4(i,r,o,a,c,"throw",t)}a(void 0)})}}function e7(t,e){return(e7=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var nt=function(t,e){return function(n,r){e(n,r,t)}},ne=function(t){function e(e,n){var r;return(r=t.call(this,{name:"Identify",reporters:n,context:e})||this).init(),r}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,e7(n,r);var n,r,o,i,s=e.prototype;return s.init=function(){var t=this;return this.pluginPromise?this.pluginPromise:((0,a.um)(c.O.IDENTIFY_INIT_START),this.pluginPromise=tL((0,f.vO)()).then(function(){t.detectTopics(),(0,a.um)(c.O.IDENTIFY_INIT_END)}).catch(function(){var t=Error("Loading chunk identify failed.\n(error: "+window.location.host+"/static/identify.js)");return t.name="ChunkLoadError",Promise.reject(t)}),this.pluginPromise)},s.handleUserProperties=(o=e9(e8().mark(function t(e,n){return e8().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return",void 0);case 2:return t.next=4,this.init();case 4:return t.abrupt("return",this.baseHandleUserProperties(e,n));case 5:case"end":return t.stop()}},t,this)})),function(t,e){return o.apply(this,arguments)}),s.handlePIIDiagnostics=function(t){try{var e=t.email_is_hashed,n=void 0===e?[]:e,r=t.sha256_email,o=void 0===r?[]:r,i=t.phone_is_hashed,c=void 0===i?[]:i,s=t.sha256_phone,u=void 0===s?[]:s;if(n.includes(tb.Vp.UNKNOWN_INVALID)||o.includes(tb.Vp.UNKNOWN_INVALID)){(0,a.ZK)(om.INVALID_EMAIL_FORMAT);return}if(c.includes(tb.Vp.UNKNOWN_INVALID)||u.includes(tb.Vp.UNKNOWN_INVALID)){(0,a.ZK)(om.INVALID_PHONE_NUMBER_FORMAT);return}if(n.includes(tb.Vp.FILTER_EVENTS)||o.includes(tb.Vp.FILTER_EVENTS)){(0,a.ZK)(om.INVALID_EMAIL_INFORMATION);return}if(c.includes(tb.Vp.FILTER_EVENTS)||u.includes(tb.Vp.FILTER_EVENTS)){(0,a.ZK)(om.INVALID_PHONE_NUMBER_INFORMATION);return}}catch(t){}},s.detectTopics=(i=e9(e8().mark(function t(){var e,n;return e8().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.getCookieDeprecationLabel();case 3:return e=t.sent,t.next=6,this.getAllTopics();case 6:(n=t.sent)&&(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"topics",extJSON:{cookie_label:String(e),stack:n.toString()}}),t.next=12;break;case 10:t.prev=10,t.t0=t.catch(0);case 12:case"end":return t.stop()}},t,this,[[0,10]])})),function(){return i.apply(this,arguments)}),e}(e5);ne=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),nt(0,z(A.CONTEXT)),nt(1,z(A.TTQ_REPORTERS))],ne);function nn(t,e){return(nn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var nr=function(t,e){return function(n,r){e(n,r,t)}},no=function(t){function e(e,n,r,o){var i;return(i=t.call(this,{name:"WebFL",reporters:n,context:e})||this).reportService=r,i.ttqOptions=o,i.useExchangeRate=o.usd_exchange_rate,i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,nn(n,r);var n,r,o=e.prototype;return o.pixelSend=function(t,e,n){var r,o;(null===(o=null===(r=this.ttqOptions)||void 0===r?void 0:r.plugins)||void 0===o?void 0:o.WebFL)&&n&&this.reportFlConv(n)},o.reportFlConv=function(t){if(!!t&&"Pageview"!==t.event){var e,n,r,o,i,s=t.context,u=t.properties,f=void 0===u?{}:u,l=void 0!==s.ad.log_extra?s.ad.log_extra:"{}";try{i=JSON.parse(l)}catch(t){i={}}var p={req_id:i.req_id||"",cid:s.ad.creative_id||"",value:f.value||"",currency:f.currency||"",raw:Object.assign({},f)},h=f.value,d=f.currency,_=this.useExchangeRate||null;var v=(e=h,n=d,r=_,isNaN(e)||e<0||null===r||!r[n]?"":(e/r[n]*1e5).toFixed(0)),g=s.pixel?s.pixel.code:"";v&&(p.usd_value=v,(0,a.um)(c.O.CUSTOM_INFO,{pixelCode:g,custom_name:"odfl_rate_exchange",extJSON:{message_id:t.message_id,cid:p.cid,event:t.event,value:h,currency:d,usdValue:v}}));var m={business:"devicefl_join_label",entrance:"app_to_web_conversion",inputParams:{message_id:t.message_id,event:t.event,event_props:p,event_source_id:null===(o=s.pixel)||void 0===o?void 0:o.code,event_source_type:"web"}};this.reportService&&this.reportService.reportFL&&(this.reportService.reportFL(m),(0,a.um)(c.O.CUSTOM_INFO,{pixelCode:g,custom_name:"fl_jsb_report",extJSON:{message_id:t.message_id,cid:p.cid,event:t.event}}))}},e}(eH);no=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),nr(0,z(A.CONTEXT)),nr(1,z(A.TTQ_REPORTERS)),nr(2,z(A.REPORT_SERVICE)),nr(3,z(A.TTQ_GLOBAL_OPTIONS))],no),(oo=oN||(oN={})).ERROR_FORMAT="error_format",oo.OVER_LENGTH="over_length_3e4",oo.FILTER_SENSITIVE_FIELDS="filter_sensitive_fields";var ni="form_detail_error";(oi=oR||(oR={})).GET_ELEMENTS_ERROR="get_elements_error",oi.INIT_ERROR="init_error",oi.ASSEMBLE_FORM_DETAIL_ERROR="assemble_form_detail_error",oi.DETECT_FORM_ELEMENT_ERROR="detect_form_element_error",oi.GET_OVERALL_FORM_DETAIL_ERROR="get_overall_form_detail_error",oi.FORM_OBSERVER_ERROR="form_observer_error",oi.OVER_LENGTH="over_length_3e4",(oa=oP||(oP={})).METADATA="Metadata",oa.CLICK="Click",(oc=oA||(oA={})).AUTO_COLLECTION="AUTO_COLLECTION",oc.AUTO_FORM="AUTO_FORM",oc.AUTO_CLICK="AUTO_CLICK",oc.AUTO_VC="AUTO_VC",oc.AUTO_VC_REVERSE="AUTO_VC_REVERSE",(os=oL||(oL={})).AUTO_FORM="form_rules",os.AUTO_VC="vc_rules",os.AUTO_VC_REVERSE="vc_rules_reverse",(ou=oC||(oC={})).PAGE_LEAVE="PageLeave",ou.PAGE_VIEW="PageView",ou.DOM_CHANGE="DomChange",ou.URL_CHANGE="UrlChange",ou.CLICK="Click",ou.SCROLL="Scroll";var na=["AnatomicalStructure","AnatomicalSystem","ApprovedIndication","ArriveAction","Artery","BioChemEntity","BloodTest","Bone","BorrowAction","BrainStructure","BrokerageAccount","CDCPMDRecord","ChemicalSubstance","CovidTestingFacility","DDxElement","DepartAction","DepositAccount","DiagnosticLab","DiagnosticProcedure","Diet","DietarySupplement","DoseSchedule","ElementarySchool","HighSchool","ExercisePlan","Gene","GovernmentBenefitsType","GovernmentService","HealthAspectEnumeration","HealthInsurancePlan","HealthPlanCostSharingSpecification","HealthTopicContent","Hospital","ImagingTest","InfectiousAgentClass","InvestmentFund","InvestmentOrDeposit","Invoice","Joint","LendAction","LifestyleModification","Ligament","LoanOrCredit","LymphaticVessel","MaximumDoseSchedule","MedicalAudience","MedicalAudienceType","MedicalCause","MedicalCode","MedicalCondition","MedicalConditionStage","MedicalContraindication","MedicalDevice","MedicalEntity","MedicalEvidenceLevel","MedicalGuidelineContraindication","MedicalIndication","MedicalIntangible","MedicalObservationalStudy","MedicalOrganization","MedicalProcedure","MedicalProcedureType","MedicalRiskCalculator","MedicalRiskFactor","MedicalRiskScore","MedicalSign","MedicalSignOrSymptom","MedicalStudy","MedicalSymptom","MedicalTest","MedicalTestPanel","MedicalTherapy","MedicalTrial","MiddleSchool","MoneyTransfer","Muscle","Nerve","OccupationalTherapy","Order","PalliativeProcedure","ParentAudience","PathologyTest","Patient","PeopleAudience","Person","Pharmacy","PhysicalActivity","PhysicalTherapy","Physician","PoliticalParty","Preschool","PreventionIndication","Protein","PsychologicalTreatment","RadiationTherapy","RecommendedDoseSchedule","ReportedDoseSchedule","School","Substance","SuperficialAnatomy","SurgicalProcedure","Text","TherapeuticProcedure","TreatmentIndication","URL","Vein","Vessel","VitalSign","WorkersUnion"];function nc(t){return/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi.test(t)||/(\+?0?86-?)?1[3-9]\d{9}/g.test(t)||/(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/g.test(t)||/^[\-!$><-==&_\/\?\.,0-9:; \]\[%~\"\{\}\)\(\+\@\^\`]/g.test(t)||na.some(function(e){return t.toLowerCase().indexOf(e.toLowerCase())>-1})}var ns=function t(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return"";if(e===document.documentElement)return"/HTML";for(var n=1,r=e.previousSibling;r;)r.nodeType===Node.ELEMENT_NODE&&r.tagName===e.tagName&&n++,r=r.previousSibling;var o=e.tagName.toLowerCase();return t(e.parentNode)+"/"+o+"["+n+"]"};function nu(t){return ns(t)}function nf(t,e){return function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];window.requestIdleCallback&&window.requestIdleCallback(t.bind.apply(t,[e].concat(r))),t.apply(e,r)}}function nl(t){var e=t.options,n=t.plugins;return e&&!1!==e.autoConfig&&n&&n[rt]}function np(t,e){if(!oL[e])return!0;var n=t.plugins;return e===oA.AUTO_VC_REVERSE?nl(t)&&n[rt]&&!n[rt][oL.AUTO_VC]:nl(t)&&n[rt]&&n[rt][oL[e]]}var nh=function(t){var e=t.parentNode,n=0,r=!1;try{r=(0,f.iK)(t)}catch(e){(0,a.vU)(c.O.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}}),r=!1}if(r)return t;for(;n<5&&e&&e!==document;){var o=!1;try{o=(0,f.iK)(e)}catch(e){(0,a.vU)(c.O.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}}),o=!1}if(o)return e;e=e.parentNode,n++}return t},nd=function t(e){for(var n=0,r=e.children,o=0;o<r.length;o++){var i=r[o],s=!1;try{s=(0,f.iK)(i)}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:i}}),s=!1}s&&n++,n+=t(i)}return n},n_=function(t){var e,n,r,o,i="";if("A"===t.tagName.toUpperCase())i=null!==(e=t.getAttribute("href"))&&void 0!==e?e:"";else if("BUTTON"===t.tagName.toUpperCase()){var a=null!==(n=t.getAttribute("onclick"))&&void 0!==n?n:"",c=null!==(r=t.getAttribute("formaction"))&&void 0!==r?r:"",s=a.match(/^.*=(['"])(.*)\1.*$/);c?i=c:s&&(i=s[2])}else"FORM"===t.tagName.toUpperCase()&&(i=null!==(o=t.getAttribute("action"))&&void 0!==o?o:"");return i},nv=function(t,e){void 0===e&&(e=!0);for(var n,r,o,i,s,u,f,l=t.attributes,p={type:"",value:"",name:"",class:"",dataset:"",id:"",tag:"",destination:"",xpath:"",inner_text:"",image_url:"",num_child_buttons:0},h=0;h<l.length;h++){var d=l[h];p[d.name]=d.value}var _=t.dataset;p.dataset=JSON.stringify(_),p.id=null!==(n=t.id)&&void 0!==n?n:"",p.class=null!==(r=t.className)&&void 0!==r?r:"",p.tag=t.tagName,p.destination=n_(t),p.name=null!==(o=t.getAttribute("name"))&&void 0!==o?o:"",p.value=null!==(i=t.getAttribute("value"))&&void 0!==i?i:"",p.type=null!==(s=t.getAttribute("type"))&&void 0!==s?s:"";var v=t.getBoundingClientRect();p.rect=v;try{p.xpath=ns(t)}catch(e){p.xpath="",(0,a.vU)(c.O.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}})}return p.inner_text=null!==(u=t.innerText)&&void 0!==u?u:"",p.image_url=null!==(f=t.getAttribute("src"))&&void 0!==f?f:"",p.num_child_buttons=e?nd(t):0,p},ng=function(t){var e=t.tag,n=t.class,r=t.destination,o=t.id,i=t.name,a=t.type,c=t.value,s=t.rect,u=t.xpath,f=t.inner_text,l=t.image_url,p={tag:e,attributes:{},inner_text:f,xpath:u,num_child_buttons:t.num_child_buttons,timestamp:new Date().toISOString(),position:s?{x:s.x,y:s.y}:{x:"",y:""}};return n&&(p.attributes.class=n),r&&(p.attributes.destination=r),o&&(p.attributes.id=o),i&&(p.attributes.name=i),a&&(p.attributes.type=a),c&&(p.attributes.value=c),l&&(p.image_url=l),p},nm={getMicroData:!0,getJSONLD:!0,getOpenGraph:!0,getMetaData:!0};function ny(t,e){return(void 0===e&&(e=500),"string"!=typeof t)?"":(t=t.trim()).length<e?t:t.slice(0,500)}function nE(t){return void 0===t&&(t=[]),{items:t,has:function(t){return this.items.some(function(e){return e===t})},add:function(t){!this.has(t)&&this.items.push(t)}}}function nw(t,e){if("object"!=typeof t)return t;if(Array.isArray(t))return t.map(function(t){return nw(t,e)});var n={};for(var r in t)!function(t,e){return!!e&&!!(e.length>0)&&e.some(function(e){return t.toLowerCase()===e.toLowerCase()})}(r,e)&&(n[r]=nw(t[r],e));return n}function nb(t){return Array.isArray(t)?t.some(function(t){return nb(t)}):"string"==typeof t&&(t=t.toLowerCase().replace(/https?:\/\/schema\.org\//,""),na.some(function(e){return t===e.toLowerCase()}))}var nO=["form","[id*=form], [class*=form]"],nI=["label"],nT=["input,select,textarea"],nS=["radio","checkbox"],nN=["hidden","reset","submit","password"];function nR(t,e){try{for(var n=0;n<t.length;n++){var r=e.querySelectorAll(t[n]);if(r&&r.length>0)return Array.from(r)}return[]}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.GET_ELEMENTS_ERROR}),[]}}(of=ox||(ox={}))[of.CONTAIN=0]="CONTAIN",of[of.ID=1]="ID",of[of.SELECTOR=2]="SELECTOR";function nP(t){var e="";return!function t(n){for(;n;)n.nodeType===Node.TEXT_NODE?e+=n.textContent:"SELECT"!==n.nodeName&&n.firstChild&&t(n.firstChild),n=n.nextSibling}(t.firstChild),e.replace(/[\t\n]/g,"").trim()}function nA(t){if(!t)return!1;var e=window.getComputedStyle(t);return!("none"===e.display||"visible"!==e.visibility||function t(e){return!(!e||e.isSameNode(document.body)||e.isSameNode(document))&&("0"==window.getComputedStyle(e).opacity||t(e.parentElement))}(t))&&(0!==t.offsetWidth||0!==t.offsetHeight)}function nL(t){var e=t.getAttribute("type");return!!e&&nN.indexOf(e)>-1}function nC(t){return t&&nc(t)?"__Text__":t}var nx=function(){function t(t){this.formUpdateHandlers=[],this.answerMap={},this.rules=this.getRules(t),this.init()}var e=t.prototype;return e.getRules=function(t){var e=t.plugins&&t.plugins.AutoConfig;return e&&e[oL.AUTO_FORM]},e.init=function(){var t=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"initAutoForm_init",start:performance.now()}}catch(t){}try{this.forms=this.detectFormElement(),this.forms&&this.forms.forEach(function(e){e.formDetail=t.assembleFormDetail(e),t.startFormObserver(e,t.formUpdateHandlers)})}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.INIT_ERROR})}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},e.getOverallFormDetail=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"initAutoForm_getOverallFormDetail",start:performance.now()}}catch(t){}var e="[]";try{this.forms&&this.forms.length>0&&(this.forms.some(function(t){var e=t.el;return!document.body.contains(e)})&&this.init(),e=JSON.stringify(this.forms.map(function(t){return t.formDetail}).filter(function(t){return t})))}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.GET_OVERALL_FORM_DETAIL_ERROR})}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}return e},e.addFormUpdateHandler=function(t){this.formUpdateHandlers.push(t)},e.startFormObserver=function(t,e){var n=this;try{var r=(0,f.Ds)(function(){var r=n.assembleFormDetail(t);(!t.formDetail||r&&(0,f.eh)(r,t.formDetail))&&(t.formDetail=r,e.forEach(function(e){return e.call(n,t.formDetail)}))},2e3,this);if(t.el.parentNode){var o=t.el.parentNode;this.observer&&this.observer.disconnect(),this.observer=new MutationObserver(r),this.observer.observe(o,{attributes:!0,childList:!0,subtree:!0}),o.addEventListener("click",r,{capture:!0})}}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.FORM_OBSERVER_ERROR})}},e.detectFormElement=function(){try{var t,e,n=[0,0,0];var r=(t=this.rules,(e=nR(t||nO,document)).filter(function(t){return!e.some(function(e){return e.contains(t)&&e!==t})}));if(!r)return[];var o=r.map(function(t){return{el:t,questions:[]}});return o.forEach(function(t){var e,r,o=(e=t.el,nR(nI,e)),i=new Set([]);o.forEach(function(e){var r=function(t,e){var n,r=nR(nT,t);if(r&&r.length)return{els:r,from:ox.CONTAIN};var o=t.getAttribute("for");if(o){;if(r=nR(["input[id='"+(n=o)+"'],select[id='"+n+"'],textarea[id='"+n+"']"],e))return{els:r,from:ox.ID}}return!1}(e,t.el);if(r){var o=r.els,a=r.from,c=o.filter(function(t){return!nL(t)}).map(function(t){return i.add(t),{el:t,from:a}});c&&c.length&&(n[a]=1,t.questions.push({el:e,answers:c}))}}),(r=t.el,nR(nT,r)).filter(function(t){return!nL(t)}).forEach(function(e){if(!i.has(e)){n[ox.SELECTOR]=1;var r,o,a=(r=e,o=t.el,function t(e){return null==e||e.isSameNode(o)?o:nP(e).length>0?e:t(e.parentNode)}(r.parentNode));t.questions.push({el:a,answers:[{el:e,from:ox.SELECTOR}]})}})}),(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"form_detail_answer_from",custom_enum:n.join("")}),o}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.DETECT_FORM_ELEMENT_ERROR}),[]}},e.calculateQuestionFilledTime=function(t){var e=t.el,n=t.answers,r=ns(e),o=n.reduce(function(t,e){var n=e.el,r=n.getAttribute("type");return r&&nS.indexOf(r.toLowerCase())>-1?t+","+n.checked:t+","+n.value},"");!this.answerMap[r]&&(this.answerMap[r]={defaultValue:o,value:o});var i=this.answerMap[r],a=i.defaultValue,c=i.filledTime;if(this.answerMap[r].value=o,a===o){delete this.answerMap[r].filledTime;return}return c||(this.answerMap[r].filledTime=+new Date)},e.assembleFormDetail=function(t){var e=this,n=t.el,r=t.questions;try{var o,i={xpath:(o=n,ns(o)),id:n.id,name:nC(n.getAttribute("name")),tag:n.tagName.toLowerCase(),class_name:n.className,questions:[],width:n.offsetWidth,height:n.offsetHeight,is_visible:nA(n)};return i.questions=r.map(function(t){var n,r=t.el,o=t.answers;var i={xpath:(n=r,ns(n)),id:r.id,name:nC(nP(r)),tag:r.tagName.toLowerCase(),class_name:r.className,filled_time:e.calculateQuestionFilledTime(t),answers:[],width:r.offsetWidth,height:r.offsetHeight,is_visible:nA(r)};return o.forEach(function(t){var e,n=t.el,r=t.from;if(n&&"SELECT"===n.tagName.toUpperCase())i.answers=i.answers.concat(Array.from(n.querySelectorAll("option")).map(function(t){var e;return{xpath:(e=t,ns(e)),id:t.id,name:nC(t.value||t.innerText),tag:t.tagName.toLowerCase(),class_name:t.className,from:r,width:t.offsetWidth,height:t.offsetHeight,is_visible:nA(n)}}));else{;i.answers.push({xpath:(e=n,ns(e)),id:n.id,name:nC(n.getAttribute("name")),tag:n.tagName.toLowerCase(),class_name:n.className,input_type:n.getAttribute("type"),placeholder:nC(n.getAttribute("placeholder")),from:r,width:n.offsetWidth,height:n.offsetHeight,is_visible:nA(n)})}}),i}),i}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:ni,custom_enum:oR.ASSEMBLE_FORM_DETAIL_ERROR});return}},t}(),nk=["United States","US","Canada","CA","Australia","AU","Mexico","MX","Argentina","AR","Chile","CL","Colombia","CO","Fiji","FJ","Liberia","LR","Namibia","NA","New Zealand","NZ","Singapore","SG","Solomon Islands","SB","Suriname","SR","South Africa","ZA","Barbados","BB","Belize","BZ","Cuba","CU","Dominican Republic","DO","Guyana","GY","Jamaica","JM","Cayman Islands","KY","Trinidad and Tobago","TT","Tuvalu","TV","Zimbabwe","ZW","United Kingdom","GB","Egypt","EG","Falkland Islands","FK","Gibraltar","GI","Guernsey","GG","Isle of Man","IM","Jersey","JE","Lebanon","LB","Saint Helena","SH","Syria","SY","Sudan","SD","Japan","JP","China","CN","Japan","JP","CN","South Korea","KR","Philippines","PH","Cuba","CU","Sweden","SE","Norway","NO","Denmark","DK","Iceland","IS","Costa Rica","CR","El Salvador","SV","Bolivia","BO","Venezuela","VE","Bahamas","BS","Brunei","BN","Ethiopia","ET","Eritrea","ER","Iran","IR","Oman","OM","Qatar","QA","Saudi Arabia","SA","Yemen","YE","Bulgaria","BG","Kyrgyzstan","KG","Central African CFA franc zone","XAF","West African CFA franc zone","XOF"].map(function(t){return t.toUpperCase()}),nD=["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTC","BTN","BWP","BYN","BYR","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNH","CNY","COP","COU","CRC","CUC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EEK","EGP","ERN","ETB","ETH","EUR","FJD","FKP","GBP","GEL","GGP","GHC","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","IMP","INR","IQD","IRR","ISK","JEP","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LTC","LTL","LVL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRO","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RMB","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLL","SOS","SRD","SSP","STD","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRL","TRY","TTD","TVD","TWD","TZS","UAH","UGX","USD","UYI","UYU","UYW","UZS","VEF","VES","VND","VUV","WST","XAF","XBT","XCD","XOF","XPF","XSU","XUA","YER","ZAR","ZMW","ZWD","ZWL"];function nM(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return nj(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?nj(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function nj(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var nU=["firstidxpath","absolutexpath","threeleveltagclassxpath"],nq=["modelxpath"],nF=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"updateParameterInferenceData",start:performance.now()}}catch(t){}try{var r=t.getPageInfo(),o=nY(r.url,e);r.url.includes("checkout")&&o&&nz(o,r.url,e),o&&nW(o)}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData"})}try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}},nV=function(t){try{var e=t.plugins&&t.plugins.AutoConfig;return e&&e.vc_rules}catch(t){return}},nG=function(t){try{if(!t)return null;for(var e,n=nM(t);!(e=n()).done;){var r=e.value;if(r.parameter_enhancement)return r.parameter_enhancement}}catch(t){t(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getEnhancementRules"})}return null},nH=function(t){var e;try{if(!t)return null;var n=null,r=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(r){var o=(null===(e=r.textContent)||void 0===e?void 0:e.trim())||null;o&&(n=o)}return n}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getEnhancementRuleDataByXPath"}),null}},nB=function(t){if(!t)return null;var e=t.split("|||").filter(function(t){return""!==t});if(e.length<2)return null;var n=e[0],r=e[1],o=e[2];return{pattern:n,replacement:r,flags:void 0===o?"":o}},nK=function(t,e){if(!t)return null;for(var n=null,r,o=nM(e);!(r=o()).done&&!(n=nH(t[r.value])););if(!n)return null;var i=n,s=(null==t?void 0:t.format_method)&&nB(t.format_method);if(s)try{var u=s.pattern,f=s.replacement,l=s.flags,p=new RegExp(u,l);i=n.replace(p,f)}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"parseFormatMethod"})}var h=Object.assign({},t,{parameterOriValue:n,parameterValue:i});return nU.forEach(function(t){delete h[t]}),nq.forEach(function(t){delete h[t]}),h},nW=function(t){try{var e=nG(t);if(!e)return null;var n={};for(var r in e)if(e.hasOwnProperty(r)){var o=e[r];if(o){var i=void 0;(i=(i=nK(o.helper_rule,nU))&&i.parameterValue?i:nK(o.model_rule,nq))&&(n[r]=Object.assign({},i),(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_rule_result",extJSON:{message:JSON.stringify(o)+"||"+JSON.stringify(i)}}))}}return n$(n3,n),(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_properties",extJSON:{message:""+JSON.stringify(n)}}),Object.keys(n).length>0?n:null}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_parameter_inference"}),null}},nY=function(t,e){var n=null;for(var r in e)if(t.includes(r))return n=e[r];return null},nJ=function(t,e){var n=function(t,e){try{for(var n,r=document.evaluate(t,document,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null),o=null;n=r.iterateNext();)/\d/.test(n.innerText)&&(o=n);if(!o&&e){for(var i=document.getElementsByClassName(e),s=0;s<i.length;s++){var u=i[s];u instanceof HTMLElement&&/\d/.test(u.innerText)&&(o=u)}}return o}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getLastElementWithNumber"}),null}}(t,e),r=null==n?void 0:n.textContent;return{priceNumberString:r?function(t){var e=t.match(/(?:\d[\d\s,.]*\d|\d)/g);if(e){for(var n,r,o,i,a,c=e[0],s=nM(e);!(a=s()).done;)if(a.value!==c)return null;return r=(n=c).replace(/[\s,\.]/g,""),o=Math.max(n.lastIndexOf("."),n.lastIndexOf(",")),i=!1,-1!==o&&o>=n.length-3&&(i=!0),i&&(r=r.slice(0,o-(n.length-1))+"."+r.slice(o-(n.length-1))),r}return null}(r):null,textContent:r}},nX=function(t){if(!t)return{currencyCode:null,currencyCodeFromeXpath:null};var e,n=null===(e=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue)||void 0===e?void 0:e.textContent,r=((null==n?void 0:n.trim())?nD.includes(n.toUpperCase().trim()):null)?null==n?void 0:n.toUpperCase().trim():null;return{currencyCode:n,currencyCodeFromeXpath:r}},nZ=function(t){if(!t)return{countryCode:null,countryCodeFromXpath:null};var e,n=null===(e=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue)||void 0===e?void 0:e.textContent,r=((null==n?void 0:n.trim())?nk.includes(n.toUpperCase().trim()):null)?null==n?void 0:n.toUpperCase().trim():null;return{countryCode:n,countryCodeFromXpath:r}},nQ=function(t){try{var e=new URL(t).hostname.split(".");for(var n in e)if(nk.includes(e[n].toUpperCase()))return e[n].toUpperCase()}catch(t){}return null},nz=function(t,e,n){try{if(t)for(var r,o=nM(t);!(r=o()).done;){var i=r.value;if(i.valueXpath&&i.currency&&i.currency.val){var s=i.currency.val,u=nJ(i.valueXpath,i.currency.val),f=u.textContent,l=u.priceNumberString;if(l&&f){var p=nX(i.currency.xpath),h=p.currencyCode,d=p.currencyCodeFromeXpath;h&&(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"auto_value_currency_currency_code_form_xpath",extJSON:{url:e,currencyCode:h,vcConfig:n}});var _=nZ(i.countryCodeXpath),v=_.countryCode,g=_.countryCodeFromXpath;v&&(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"auto_value_currency_country_form_xpath",extJSON:{url:e,country:v,vcConfig:n}});var m={value:l,currency:d||s,ori_value:f,rule_key:i.rule_key,country_code:g};if(!g){var y=nQ(e);m.country_code=y}return(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"auto_value_currency_update_info",extJSON:{url:e,autoProperties:m,vcConfig:n}}),n$(n2,m),m}}}return null}catch(t){return(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"updateAutoVCData"}),null}},n$=function(t,e){try{sessionStorage.setItem(t,JSON.stringify(e))}catch(t){}},n0=function(t){try{var e=sessionStorage.getItem(t);return e?JSON.parse(e):null}catch(t){return null}},n1=function(t){try{sessionStorage.removeItem(t)}catch(t){}},n2="value_currency_rule",n3="parameter_inference_rule",n6=[oC.CLICK,oC.SCROLL],n5=function(){function t(t){var e=this;this.handlerArray=t,n6.forEach(function(t){window.addEventListener(t.toLowerCase(),(0,f.Ds)(function(){e.interactionHandler(t)},2e3,e),{capture:!0,passive:!0})})}var e=t.prototype;return e.iterateHandlerArray=function(t){this.handlerArray.forEach(function(e){return e(t)})},e.interactionHandler=function(t){var e=this;this.timeoutId&&clearTimeout(this.timeoutId),this.iterateHandlerArray(t),this.timeoutId=setTimeout(function(){e.iterateHandlerArray(t)},2e3)},t}(),n8=function(){function t(){this.history={}}var e=t.prototype;return e.hasReport=function(t,e,n){var r=this.genHistoryKey(t,e);return this.history[r]&&this.history[r].indexOf(n)>-1},e.addHistory=function(t,e,n){var r=this.genHistoryKey(t,e);!this.history[r]&&(this.history[r]=[]),this.history[r].push(n)},e.clearHistory=function(){this.history={}},e.genHistoryKey=function(t,e){return t+":"+e},t}(),n4=function(){function t(t,e,n){this.context=t,this.reportHistory=new n8,this.reporters=e,this.reportService=n}var e=t.prototype;return e.report=function(t,e,n){var r=this,o=tU(oE.AUTO_CONFIG),i=this.getReportPixelList(e,n),a=this.assemblyReportData(t,n,i);a&&o&&this.reportService.reportPreTasks.then(function(){r.reportService.report(o,a,ob.defaultReport,oO.P0)})},e.clearHistory=function(){this.reportHistory.clearHistory()},e.getReportPixelList=function(t,e){var n=this,r=JSON.stringify(Object.assign({},e,{page_trigger:void 0}));return this.reporters.filter(function(e){return!!nl(e)&&np(e,t)}).filter(function(e){var o=e.getReporterId();return!([oA.AUTO_COLLECTION,oA.AUTO_FORM].indexOf(t)>-1&&n.reportHistory.hasReport(t,o,r))&&(n.reportHistory.addHistory(t,o,r),e)})},e.getCookieBasedSessions=function(t){var e=[],n=tI(document.cookie),r=t.filter(function(t){var e;return!!(null===(e=null==t?void 0:t.reporterInfo)||void 0===e?void 0:e.firstPartyCookieEnabled)});if(0===r.length)return e;var o=this.context.getPageCookieBasedSession();return r.forEach(function(t){var r=t.getReporterId();t.setCookieBasedSession(n[r]),e.push(tS(t.getCookieBasedSession(),o,r))}),e},e.assemblyReportData=function(t,e,n){if(0!==n.length){var r,o=n.map(function(t){return t.getReporterId()}),i=this.context.getPageSign(),a=this.getCookieBasedSessions(n),c=n[0],s=c.assemblyData(c.getReporterId(),"",{},{},oE.AUTO_CONFIG);return delete s.event,s.action=t,s.auto_collected_properties=e,!s.context.pixel&&(s.context.pixel={}),s.context.pixel.code=o[0],s.context.pixel.codes=o.join("|"),s.context.index=null===(r=i.pageIndex)||void 0===r?void 0:r.index,s.context.session_id=i.sessionId,s.context.pageview_id=(0,l.gj)(this.context.getPageViewId(),c.reporterInfo.loadId,p.n2),s.context.sessions=a,s.message_id=s.message_id.replace(/-[^-]*$/,""),s}},t}();function n9(t,e){return(n9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var n7=function(t,e){return function(n,r){e(n,r,t)}},rt="AutoConfig",re=function(t){function e(e,n,r,o){var i;return(i=t.call(this,{name:rt,reporters:n,context:e})||this).autoCollectedMetadata={},i.initialize=!1,i.autoFormUpdateHandler=(0,f.Ds)(function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"auto_config_form_handler",functionName:window.ttq._pf_tn&&"auto_config_form_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_form_handler")}}catch(t){}if(i.autoForm){if(i.autoCollectedFormDetail=i.autoForm.getOverallFormDetail(),i.autoCollectedFormDetail.length>3e4){(0,a.vU)(c.O.CUSTOM_ERROR,{message:""+String(i.autoCollectedFormDetail.length)},{custom_name:ni,custom_enum:oR.OVER_LENGTH});return}i.actTracker.report(oP.METADATA,oA.AUTO_FORM,{page_trigger:t,form_details:i.autoCollectedFormDetail})}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"auto_config_form_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},200,i),i.autoCollectionUpdateHandler=(0,f.Ds)(function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"auto_config_metadata_handler",functionName:window.ttq._pf_tn&&"auto_config_metadata_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_metadata_handler")}}catch(t){}i.autoCollectedMetadata=function(t){var e={open_graph:"{}",microdata:"[]",json_ld:"[]",meta:"{}"};try{e.microdata=function(){if(!nm.getMetaData)return"[]";for(var t=document.querySelectorAll("[itemscope]"),e=[],n=nE(),r=0;r<t.length;r++)n.add(t[r]);for(var o=t.length-1;o>=0;o--){var i=t[o],a=i.getAttribute("itemtype");if("string"==typeof a&&""!==a){for(var c={},s=i.querySelectorAll("[itemprop]"),u=0;u<s.length;u++){var f=s[u];if(!n.has(f)){n.add(f);var l=f.getAttribute("itemprop");if("string"==typeof l&&""!==l){var p=function(t){var e;switch(t.tagName.toLowerCase()){case"meta":e=t.getAttribute("content");break;case"audio":case"embed":case"iframe":case"img":case"source":case"track":case"video":e=t.getAttribute("src");break;case"a":case"area":case"link":e=t.getAttribute("href");break;case"object":e=t.getAttribute("data");break;case"data":case"meter":e=t.getAttribute("value");break;case"time":e=t.getAttribute("datetime");break;default:e=function(t){if(t){if(t.innerText&&t.innerText.length>0)return t.innerText;if(t.textContent&&t.textContent.length>0)return t.textContent}return""}(t)||""}return"string"==typeof e?ny(e):""}(f);if(null!=p){var h=c[l];null!=h&&function(t,e){return null!=[{property:"image",type:"Product"}].filter(function(n){return(t==="https://schema.org/"+n.type||t==="http://schema.org/"+n.type)&&n.property===e})[0]}(a,l)?Array.isArray(h)?c[l].push(p):c[l]=[h,p]:c[l]=p}}}}e.unshift({schema:{dimensions:{h:i.clientHeight,w:i.clientWidth},properties:nb(a)?{}:c,subscopes:[],type:a},scope:i})}}for(var d=[],_=[],v=0;v<e.length;v++){for(var g=e[v],m=g.scope,y=g.schema,E=d.length-1;E>=0;E--){if(d[E].scope.contains(m)){d[E].schema.subscopes.push(y);break}d.pop()}0===d.length&&_.push(y),d.push({schema:y,scope:m})}var w=JSON.stringify(_);return w.length>3e4&&(w="[]",nm.getMicroData=!1),w}()}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"microdata"})}try{var n=function(){if(!nm.getJSONLD)return{data:"[]",errors:[]};for(var t=[],e=[],n=document.querySelectorAll('script[type="application/ld+json"]'),r=0,o=0;o<n.length;o++){var i=n[o].innerText;if(null!=i&&""!==i){if((r+=i.length)>3e4)return nm.getJSONLD=!1,{data:JSON.stringify([]),errors:[{name:oN.OVER_LENGTH,message:""+String(r)}]};var a=void 0;try{a=JSON.parse(i.replace(/[\n\r\t]+/g," "))}catch(t){e.push({name:oN.ERROR_FORMAT,message:t.message})}try{a=function t(e){if("object"!=typeof e)return e;if(Array.isArray(e))return e.map(function(e){return t(e)});var n=Object.assign({},e),r=n["@type"];for(var o in n){if("@type"!==o&&"@context"!==o)"object"==typeof n[o]?n[o]=t(n[o]):r&&nb(r)&&delete n[o]}return n}(a)}catch(t){return{data:JSON.stringify([]),errors:[{name:oN.FILTER_SENSITIVE_FIELDS,message:t.message}]}}a&&t.push(a)}}return{data:JSON.stringify(t),errors:e}}(),r=n.data,o=n.errors;e.json_ld=r,o&&o.forEach(function(t){var e=t.name,n=t.message;(0,a.vU)(c.O.CUSTOM_ERROR,{message:n},{custom_name:"parse_json_ld_failed",custom_enum:e})})}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"json_ld"})}try{e.open_graph=function(t){if(!nm.getOpenGraph)return"{}";for(var e=nE(["og","product","music","video","article","book","profile","website","twitter"]),n={},r=document.querySelectorAll("meta[property],meta[name]"),o=0;o<r.length;o++){var i=r[o],a=i.getAttribute("property")||i.getAttribute("name"),c=ny(i.getAttribute("content"));if("string"==typeof a&&-1!==a.indexOf(":")&&"string"==typeof c&&e.has(a.split(":")[0])){var s=n[a];null!=s&&function(t){return null!=["og:image"].filter(function(e){return e===t})[0]}(a)?Array.isArray(s)?n[a].push(c):n[a]=[s,c]:n[a]=c}}return JSON.stringify(nw(n,t))}(t.open_graph)}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"open_graph"})}try{e.meta=function(t){if(!nm.getMetaData)return"{}";var e={},n=nE(["description","keywords","keyword"]),r=document.querySelector("title");r&&(e.title=ny(r.innerText));for(var o=document.querySelectorAll("meta[property],meta[name]"),i=0;i<o.length;i++){var a=o[i],c=a.getAttribute("name")||a.getAttribute("property"),s=ny(a.getAttribute("content"));"string"==typeof c&&"string"==typeof s&&n.has(c)&&(e[c]=s)}return JSON.stringify(nw({title:e.title,"meta:description":e.description,"meta:keywords":e.keywords||e.keyword},t))}(t.meta)}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"meta"})}return e}(i.filter),i.actTracker.report(oP.METADATA,oA.AUTO_COLLECTION,{page_trigger:t,content_data:i.autoCollectedMetadata});try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"auto_config_metadata_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},200,i),i.autoClickCallback=function(t){try{i.signal_insights_config&&nF(i.context,i.signal_insights_config);var e=nh(t.target);if(e){var n=ng(nv(e));if(function(t){var e,n,r=(null===(e=t.inner_text)||void 0===e?void 0:e.toString().toLowerCase())||"",o=(null===(n=t.image_url)||void 0===n?void 0:n.toString().toLowerCase())||"",i=[];if(t.attributes){var a=t.attributes;i=[a.class,a.id,a.name,a.type,a.value,a.destination].map(function(t){return(null==t?void 0:t.toString().toLowerCase())||""})}return[r,o].concat(i).some(nc)}(n)){(0,a.um)(c.O.CUSTOM_INFO,{custom_name:"sensitive_button",extJSON:{attributes:{id:n.attributes.id,tag:n.tag,class:n.attributes.class}}});return}i.reportButtonClick(n)}}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"auto_click_callback_error",custom_enum:"auto_click"})}},i.filter=r.auto_config||{open_graph:[],microdata:[],json_ld:[],meta:[]},i.actTracker=new n4(e,n,o),i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,n9(n,r);var n,r,o=e.prototype;return o.initBaseFeature=function(t){!this.initialize&&nl(t)&&(this.initAutoClick(),this.initAutoCollection(),this.initInteractionListener(t),this.initialize=!0)},o.initExtraFeature=function(t){!this.autoForm&&this.initialize&&np(t,oA.AUTO_FORM)&&this.initAutoForm(t),this.initAutoVC(t)},o.initInteractionListener=function(t){var e=this,n=t.options;!this.initialize&&n&&!1!==n.autoConfigListener&&new n5([function(t){nf(e.autoCollectionUpdateHandler,e)(t)},function(t){nf(e.autoFormUpdateHandler,e)(t)}])},o.initAutoClick=function(){var t,e;t=this.autoClickCallback,e=(0,l.P2)(function(e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"auto_config_click_handler",functionName:window.ttq._pf_tn&&"auto_config_click_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_click_handler")}}catch(t){}t(e);try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"auto_config_click_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},1e3),document.querySelectorAll(f.zh.join(", ")).forEach(function(t){!f.P6.some(function(e){return t.matches(e)})&&t.addEventListener("click",e,{capture:!0})})},o.initAutoVC=function(t){var e=nV(t);e&&(this.signal_insights_config=e)},o.initAutoCollection=function(){},o.initAutoForm=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"initAutoForm",start:performance.now()}}catch(t){}this.autoForm=new nx(t),this.autoForm.addFormUpdateHandler(this.autoFormUpdateHandler.bind(this)),this.autoCollectedFormDetail=this.autoForm.getOverallFormDetail();try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},o.pixelDidMount=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"auto_config_plugin_pixelDidMount",start:performance.now()}}catch(t){}this.initBaseFeature(t),this.initExtraFeature(t);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},o.pixelSend=function(t,e,n){var r=this.reporters.find(function(e){return e.getReporterId()===t});if(this.signal_insights_config&&nF(this.context,this.signal_insights_config),r&&nV(r)&&(n0(n2)||n0(n3))){((0,f.Zq)(e)||"Pageview"===e||"InitiateCheckout"===e||"PlaceAnOrder"===e)&&(o=n0(n2));var o,i=n0(n3),a={vc_properties:Object.assign(Object.assign({},o&&{value:o.value,currency:o.currency,ori_value:o.ori_value,rule_key:o.rule_key}),i&&{parameter_inference:i})};n.auto_collected_properties=a}if((0,f.Zq)(e)&&setTimeout(function(){n1(n2)},500),"Pageview"===e&&(!r||!!nl(r)))nf(this.autoCollectionUpdateHandler,this)(oC.PAGE_VIEW),nf(this.autoFormUpdateHandler,this)(oC.PAGE_VIEW)},o.pageUrlDidChange=function(t,e){null!=e&&""!=e&&(this.autoCollectionUpdateHandler(oC.URL_CHANGE),this.autoFormUpdateHandler(oC.URL_CHANGE))},o.pageWillLeave=function(){this.autoCollectionUpdateHandler(oC.PAGE_LEAVE),this.autoFormUpdateHandler(oC.PAGE_LEAVE)},o.reportButtonClick=function(t){this.actTracker.report(oP.CLICK,oA.AUTO_VC,{page_trigger:oC.CLICK,trigger_element:t,vc_properties:n0(n2)?n0(n2):void 0}),this.actTracker.report(oP.CLICK,oA.AUTO_VC_REVERSE,{page_trigger:oC.CLICK,trigger_element:t})},e}(eH);re=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),n7(0,z(A.CONTEXT)),n7(1,z(A.TTQ_REPORTERS)),n7(2,z(A.TTQ_GLOBAL_OPTIONS)),n7(3,z(A.REPORT_SERVICE))],re);var rn=n("278"),rr=n.n(rn),ro={EMPTY_EVENT_TYPE_NAME:{title:"Missing Event Name",desc:"The event name for one or more of your events is empty. This can affect the accuracy of reporting for your conversions.",suggestion:"Go to your source code and add a name that follows our format requirements and TikTok policies.",link:"https://ads.tiktok.com/marketing_api/docs?rid=5ipocbxyw8v&id=1701890973258754"},INVALID_CONTENT_ID:{title:"Missing value for content ID",desc:"Include a value for your 'content_id' parameter. This is required for Video Shopping Ads (VSA).",suggestion:"If you are or plan to run Video Shopping Ads (VSA), go to your source code and include a value for the 'content_id' parameter.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_CONTENT_TYPE:{title:"Invalid content type",desc:'The content type for one or more of your events is not valid. Content type must be either "product", "product_group", "destination", "hotel", "flight" or "vehicle".',suggestion:"Go to your source code and update the content type.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_CURRENCY_CODE:{title:"Invalid currency code",desc:"The currency code for one or more of your events isn't supported. This can affect the accuracy of reporting for your ROAS.",suggestion:"Go to your source code and update the 'currency' parameters with a supported currency code.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_EMAIL_FORMAT:{title:"Incorrect email format",desc:"The email format for your events does not match the format supported. This can impact Advanced Matching and your ad performance.",suggestion:"Go to your source code and update the format of your shared emails. It should follow '<EMAIL>' format.",link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_EMAIL_INFORMATION:{title:"Invalid email information",desc:"The emails shared with your events were invalid.",suggestion:'Go to your source code to double check shared emails. Leave your string empty when customer information isn\'t available. Avoid spaces, "undefined", or other hardcoded values.',link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_EVENT_PARAMETER_VALUE:{title:"Invalid value parameter",desc:"The 'value' parameter for one or more of your events is invalid. This is used calculate ROAS for people and the bid for your highest value customers. Parameters must be an integer or in the decimal format (e.g. 9.99). Also, they can't contain currency symbols, special characters, letters, or commas.",suggestion:"Go to your source code and update the 'value' parameter. It can only include numbers greater than or equal to zero (e.g. 9.99). Do not include currency symbols, special characters, letters, or commas.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_PHONE_NUMBER_FORMAT:{title:"Incorrect phone number format",desc:"The phone number format for your events doesn't follow the E.164 format. This can affect Advanced Matching and your ad performance.",suggestion:"Go to your source code and update your shared phone numbers. It should follow the E.164 format.",link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_PHONE_NUMBER_INFORMATION:{title:"Invalid phone number information",desc:"The phone numbers shared with your events were invalid.",suggestion:'Go to your source code to double check shared phone numbers. Leave your string empty when customer information isn\'t available. Avoid spaces, "undefined", or other hardcoded values.',link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},LONG_EVENT_TYPE_NAME:{title:"Event Name Too Long",desc:"1 event type exceeds the 50 character limit.",suggestion:"Go to your source code and make these event names 50 characters or less.",link:"https://ads.tiktok.com/help/article/custom-events?lang=en"},MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT:{title:"Invalid Event Name Format",desc:"1 event name was rejected for not following TikTok format requirements.",suggestion:"Go to your source code and update these event types according to TikTok format requirements.",link:"https://ads.tiktok.com/help/article/custom-events?lang=en"},MISSING_CONTENT_ID:{title:"Missing 'content_id' paramter",desc:"The 'content_id' parameter isn't being received. This is required for Video Shopping Ads (VSA).",suggestion:"Include the 'content_id' parameter in your source code. This is required for Video Shopping Ads (VSA).",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},MISSING_CURRENCY_PARAMETER:{title:'Missing "currency" parameter',desc:"Events shared are missing a 'currency' parameter. This impacts our ability to receive the value amount correctly, which can affect the accuracy of reporting for your return on ad spend.",suggestion:'Go to your source code and include the "currency" parameter. You can check supported currency codes. {{learn_more}}',link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},MISSING_EMAIL_AND_PHONE:{title:"Missing email and phone number",desc:"Email and phone number info isn't being received. This information is required for Complete Payment events.",suggestion:"Improve your email and phone coverage. This allows you to attribute more conversions and reach more people with your ads.",link:"https://ads.tiktok.com/marketing_api/docs?rid=5ipocbxyw8v&id=1701890972946433"},MISSING_VALUE_PARAMETER:{title:'Missing "value" parameter',desc:"Events shared are missing a 'value' parameter'. This is used calculate ROAS for people and the bid for your highest value customers. ",suggestion:'Go to your source code and include the "value" parameter.',link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},DUPLICATE_PIXEL_CODE:{title:"Duplicate Pixel ID",desc:"The pixel ID is duplicate. This could impact the pixel data accuracy.",suggestion:"Please double check and delete any unnecessary pixel code.",link:""},MISSING_PIXEL_CODE:{title:"Missing pixel ID",desc:"Some of the events sent to your TikTok account are missing a pixel ID.",suggestion:"Go to your source code and double check that the 20-character pixel ID has been added to the ttq.load() function. Don't send null values or spaces. If you edited the base code, ensure the event.js has the 'sdkid' in the Chrome network panel.",link:""},INVALID_PIXEL_CODE:{title:"Invalid pixel ID",desc:"The pixel ID is invalid. This could prevent your pixel from receiving data.",suggestion:"Please go to Events Manager and find the correct pixel ID.",link:""}},ri=/^[a-zA-Z\d]([a-zA-Z_\-\d ]{0,}[a-zA-Z_\-\d])?$/,ra=["product","product_group","destination","hotel","flight","vehicle"],rc=["email_is_hashed","phone_is_hashed","sha256_email","sha256_phone"],rs=["Purchase","CompletePayment","InitiateCheckout","AddToCart","PlaceAnOrder","ViewContent","AddToWishlist"],ru=function(t){return void 0===t},rf=function(t,e){if(!!om[t]){var n=(0,s.E9)(),r=ro[t],o="[TikTok Pixel] - "+r.title;r.desc&&(o+="\nIssue: "+r.desc),r.suggestion&&(o+="\nSuggestion: "+r.suggestion),e&&Object.keys(e).forEach(function(t){o=o.split("{{"+t+"}}").join(e[t])}),o=o.trim(),r.link&&(o+=" See "+r.link+" for more information."),n&&n.console&&n.console.warn&&n.console.warn(o)}};(ol=ok||(ok={}))[ol.Live=0]="Live",ol[ol.NoRecord=1]="NoRecord";var rl=function(t){var e=t.event,n=void 0===e?"":e;return!!(["null","undefined"].includes(n)||/^\s*$/.test(n))||!n},rp=function(t){var e=t.event;return!!rl(t)||ri.test(void 0===e?"":e)},rh=function(t){var e=t.event;return(void 0===e?"":e).length<=50},rd=function(t){var e=t.event,n=t._inspection;if((0,f.Zq)(e)){var r=(void 0===n?{}:n).identity_params,o=void 0===r?{}:r;return 0===Object.keys(o).length||rc.every(function(t){return(o[t]||[]).includes(tb.Vp.EMPTY_VALUE)})}return!1},r_=function(t){var e=t._inspection,n=void 0===e?{}:e;return!!n&&!!n.identity_params&&((n.identity_params.email_is_hashed||[]).includes(tb.Vp.FILTER_EVENTS)||(n.identity_params.sha256_email||[]).includes(tb.Vp.FILTER_EVENTS))},rv=function(t){var e=t._inspection,n=void 0===e?{}:e;return!!n&&!!n.identity_params&&((n.identity_params.email_is_hashed||[]).includes(tb.Vp.UNKNOWN_INVALID)||(n.identity_params.sha256_email||[]).includes(tb.Vp.UNKNOWN_INVALID))},rg=function(t){var e=t._inspection,n=void 0===e?{}:e;return!!n&&!!n.identity_params&&((n.identity_params.phone_is_hashed||[]).includes(tb.Vp.FILTER_EVENTS)||(n.identity_params.sha256_phone||[]).includes(tb.Vp.FILTER_EVENTS))},rm=function(t){var e=t._inspection,n=void 0===e?{}:e;return!!n&&!!n.identity_params&&((n.identity_params.phone_is_hashed||[]).includes(tb.Vp.UNKNOWN_INVALID)||(n.identity_params.sha256_phone||[]).includes(tb.Vp.UNKNOWN_INVALID))},ry=function(t){var e=t.event,n=t.properties,r=void 0===n?{}:n;if(rs.includes(void 0===e?"":e)){if(ru(r.contents)&&ru(r.content_id))return!0;if(!ru(r.contents))return!Array.isArray(r.contents)||!!(r.contents.length<1)||!r.contents.every(function(t){return t&&!ru(t.content_id)})}return!1},rE=function(t){var e=t.properties,n=void 0===e?{}:e,r=n.content_id,o=n.contents;return!(!ru(r)&&/^\s*$/.test(r))&&(!(!ru(o)&&Array.isArray(o))||o.every(function(t){return t&&!ru(t.content_id)&&!/^\s*$/.test(t.content_id)}))},rw=function(t){var e=t.properties.content_type;return!e||ra.includes(e)},rb=function(t){var e=t.properties.value;if(e)return!!("number"==typeof e||"string"==typeof e&&(/^\d+(\.\d+)?$/.test(e)||/^\d+$/.test(e)))||!1;return!0},rO=function(t){var e=t.event,n=t.properties,r=void 0===n?{}:n;return!!((0,f.Zq)(void 0===e?"":e)&&ru(r.value)||!ru(r.currency)&&ru(r.value))||!1},rI=function(t){var e=t.properties.currency;return!e||tM.includes(e)},rT=function(t){var e=t.event,n=t.properties,r=void 0===n?{}:n;return!!((0,f.Zq)(void 0===e?"":e)&&ru(r.currency)||!ru(r.value)&&ru(r.currency))||!1};function rS(t,e){return(rS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rN=function(t,e){return function(n,r){e(n,r,t)}},rR=function(t){function e(e,n,r){var o,i;return(o=t.call(this,{name:"DiagnosticsConsole",reporters:n,context:e})||this).isEnableDiagnosticsConsole=!1,o.isEnableDiagnosticsConsole=!!(null===(i=null==r?void 0:r.plugins)||void 0===i?void 0:i.DiagnosticsConsole),o}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,rS(n,r);var n,r,o=e.prototype;return o.isDisableDiagnosticsConsole=function(){try{if(this.isEnableDiagnosticsConsole)return!!Object.values(this.reporters).some(function(t){var e,n;return void 0!==(null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.diagnostics)&&!(null===(n=null==t?void 0:t.options)||void 0===n?void 0:n.diagnostics)});return!0}catch(t){return!1}},o.warn=function(t,e){try{if(this.isDisableDiagnosticsConsole())return;rf(t,e)}catch(e){(0,a.vU)(c.O.CUSTOM_ERROR,e,{custom_name:"diagnostics_console",custom_enum:t})}},o.pixelDidMount=function(t){var e=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"console_plugin_pixelDidMount",start:performance.now()}}catch(t){}t.getParameterInfo().then(function(t){e.handlePixelInfoValidate(t)}).catch(function(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"diagnostics_console",custom_enum:"pixel"})});try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}},o.pixelSend=function(t,e,n,r,o){void 0===r&&(r={});try{(!r||!r._i)&&o===oE.TRACK&&e!==tk&&this.handleEventPayloadValidate(rr()(n||{}))}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"diagnostics_console",custom_enum:"track"})}},o.handlePixelInfoValidate=function(t){if(t.status!==ok.Live){this.warn(om.INVALID_PIXEL_CODE);return}},o.handleEventPayloadValidate=function(t){!t.properties&&(t.properties={}),rl(t)&&this.warn(om.EMPTY_EVENT_TYPE_NAME),!rp(t)&&this.warn(om.MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT),!rh(t)&&this.warn(om.LONG_EVENT_TYPE_NAME),rd(t)&&this.warn(om.MISSING_EMAIL_AND_PHONE),r_(t)&&this.warn(om.INVALID_EMAIL_INFORMATION),rv(t)&&this.warn(om.INVALID_EMAIL_FORMAT),rg(t)&&this.warn(om.INVALID_PHONE_NUMBER_INFORMATION),rm(t)&&this.warn(om.INVALID_PHONE_NUMBER_FORMAT),ry(t)&&this.warn(om.MISSING_CONTENT_ID),!rE(t)&&this.warn(om.INVALID_CONTENT_ID),!rw(t)&&this.warn(om.INVALID_CONTENT_TYPE),!rb(t)&&this.warn(om.INVALID_EVENT_PARAMETER_VALUE),rO(t)&&this.warn(om.MISSING_VALUE_PARAMETER),!rI(t)&&this.warn(om.INVALID_CURRENCY_CODE),rT(t)&&this.warn(om.MISSING_CURRENCY_PARAMETER,{learn_more:""})},e}(eH);rR=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),rN(0,z(A.CONTEXT)),rN(1,z(A.TTQ_REPORTERS)),rN(2,z(A.TTQ_GLOBAL_OPTIONS))],rR);function rP(t,e){return(rP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rA=function(t,e){return function(n,r){e(n,r,t)}},rL=function(t){function e(e,n,r,o){var i;return(i=t.call(this,{name:"PangleCookieMatching",reporters:n,context:e})||this).hasReport=!1,i.reportService=r,i.env=o,i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,rP(n,r);var n,r,o=e.prototype;return o.isPixelPangleCookieMatching=function(t){void 0===t&&(t="");var e=this.reporters;if(t){var n=e.find(function(e){return e.getReporterId()===t});if(n&&n.plugins.PangleCookieMatching)return!0}else if(e.some(function(t){return!!t.plugins.PangleCookieMatching}))return!0;return!1},o.disablePangleCookie=function(){this.isPixelPangleCookieMatching()&&tL("https://analytics.pangle-ads.com/api/v2/pangle_disable_cookie")},o.pixelSend=function(t,e,n,r,o){void 0===r&&(r={});try{var i,s=this.context.getPageSign();if((null===(i=s.pageIndex)||void 0===i?void 0:i.index)===0&&!(0,u.Z0)(this.env)&&n&&n.message_id&&this.isPixelPangleCookieMatching(t)&&!this.hasReport){var f=n.event,l=n.message_id,p=n.context.library,h={event:f,message_id:l,context:{library:p},timestamp:new Date().toJSON()};this.hasReport=!0,this.reportService.report("https://analytics.pangle-ads.com/api/v2/pangle_pixel",h,ob.httpReport)}}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"pangle_report"})}},e}(eH);rL=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),rA(0,z(A.CONTEXT)),rA(1,z(A.TTQ_REPORTERS)),rA(2,z(A.REPORT_SERVICE)),rA(3,z(A.ENV))],rL);var rC="https://analytics.tiktok.com/i18n/pixel/eb.js",rx="_tt_event_builder";(op=oD||(oD={})).EVENT_BUILD_BOOTSTRAP_ACK="event_builder_bootstrap_ack",op.EVENT_BUILD_WRONG_CODE="event_builder_wrong_code",op.EVENT_BUILD_BOOTSTRAP="event_builder_bootstrap";function rk(t,e){return(rk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rD=function(t,e){return function(n,r){e(n,r,t)}},rM=function(t){var e,n;function r(e,n){var r;return(r=t.call(this,{name:"EventBuilder",reporters:n,context:e})||this).pluginMounted=!1,r}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,rk(e,n),r.prototype.pixelDidMount=function(t){var e=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"event_builder_plugin_pixelDidMount",start:performance.now()}}catch(t){}if(!this.pluginMounted){this.pluginMounted=!0;var r=(0,s.Ie)(),o=function(t){t.data.type===oD.EVENT_BUILD_BOOTSTRAP&&!r._event_builder_pickup_sdk_loaded&&(e.reporters.find(function(e){return e.getReporterId()===t.data.pixelCode})?(r._event_builder_pickup_sdk_loaded=!0,ta(rx,t.data),tL(rC).then(function(){window.opener.window.postMessage({type:oD.EVENT_BUILD_BOOTSTRAP_ACK},"*")}).catch(function(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"event_builder_load_error",custom_enum:"load_ebjs"})})):!r._event_builder_pickup_sdk_verify_flag&&(setTimeout(function(){!e.reporters.find(function(e){return e.getReporterId()===t.data.pixelCode})&&window.opener.window.postMessage({type:oD.EVENT_BUILD_WRONG_CODE},"*")},5e3),r._event_builder_pickup_sdk_verify_flag=!0))};!r._event_builder_pickup_sdk_loaded&&(to(rx)?tL(rC).then(function(){r._event_builder_pickup_sdk_loaded=!0}):window.opener&&(window.addEventListener("message",o),setTimeout(function(){window.removeEventListener("message",o)},8e3)));try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}}},r}(eH);rM=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),rD(0,z(A.CONTEXT)),rD(1,z(A.TTQ_REPORTERS))],rM);function rj(t,e){return(rj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rU=function(t,e){return function(n,r){e(n,r,t)}},rq="tt_pixel_is_enrich_ipv6_triggered_by_enrich_am",rF=function(t){function e(e,n,r,o){var i;return(i=t.call(this,{name:"EnrichIpv6",reporters:n,context:e})||this).hasReported=!1,i.shouldReportAfterEnrichAM=!1,i.reportService=r,i.env=o,i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,rj(n,r);var n,r,o=e.prototype;return o.isPixelEnrichIpv6=function(){var t=this.reporters;return!!t&&0!==t.length&&t.every(function(t){return t&&t.plugins&&!0===t.plugins.EnrichIpv6})},o.isEnrichIpv6V2SwitchOn=function(){var t="EnrichIpv6V2";try{var e=(0,s.Ie)()._plugins||{};if(null!=e[t])return!!e[t];return!1}catch(t){return!1}},o.buildEnrichIpv6Data=function(t){var e=this.isEnrichIpv6V2SwitchOn()?"#source=2":"#source=1";return Object.assign(Object.assign({},t),{event:"EnrichIpv6",trigger_event:t.event,message_id:t.message_id+e})},o.pixelSend=function(t,e,n,r,o){void 0===r&&(r={});try{if(o!==oE.TRACK)return;if(!("Shopify"===(0,s.kW)(t)||this.isEnrichIpv6V2SwitchOn()))return;if((0,u.Z0)(this.env)||!(n&&n.message_id))return;var i,f=this.context.getPageSign();if((null===(i=f.pageIndex)||void 0===i?void 0:i.index)===0&&!this.hasReported&&this.isPixelEnrichIpv6()&&(this.hasReported=!0,this.reportService.report(t_,this.buildEnrichIpv6Data(n),ob.htmlHttpReport)),"true"===sessionStorage.getItem(rq))return;"EnrichAM"===e&&(this.shouldReportAfterEnrichAM=!0),this.shouldReportAfterEnrichAM&&this.isPixelEnrichIpv6()&&(this.shouldReportAfterEnrichAM=!1,sessionStorage.setItem(rq,"true"),this.reportService.report(t_,this.buildEnrichIpv6Data(n),ob.htmlHttpReport))}catch(t){(0,a.vU)(c.O.CUSTOM_ERROR,t,{custom_name:"enrich_ipv6_report"})}},e}(eH);rF=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),rU(0,z(A.CONTEXT)),rU(1,z(A.TTQ_REPORTERS)),rU(2,z(A.REPORT_SERVICE)),rU(3,z(A.ENV))],rF);(oh=oM||(oM={})).FIRST_CONTENTFUL_PAINT="fcp",oh.LARGEST_CONTENTFUL_PAINT="lcp",oh.FIRST_INPUT_DELAY="fid",oh.TIME_TO_FIRST_BYTE="ttfb",oh.PAGE_LEAVE="pl",oh.LOAD_FINISH="lf",oh.TIME_WINDOW_TRACKER="twt",oh.TIME_TO_INTERACTIVE="tti";function rV(t,e){return function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];window.requestIdleCallback?window.requestIdleCallback(t.bind.apply(t,[e].concat(r))):t.apply(e,r)}}function rG(t,e){void 0===e&&(e=4);try{if(Number.isInteger(t))return t;return parseFloat(t.toFixed(e))}catch(t){return -1}}var rH=function(){function t(t,e,n,r){r?(this.reportService=n,this.context=t,this.reporters=e,this.reportUrl=r):(0,a.vU)(c.O.CUSTOM_ERROR,Error("empty reportUrl"),{custom_name:"empty_reportUrl",custom_enum:"page_perf_monitor"})}var e=t.prototype;return e.getResult=function(t){return{action_event:t}},e.report=function(t){var e=this;if(void 0!==t){var n=this.getReportPixelList(),r=this.assemblyReportData(oE.PAGE,t,n);r&&this.reportService.reportPreTasks.then(function(){e.reportService.report(e.reportUrl,r,ob.defaultReport,oO.P0)}),this.resetAfterReport()}},e.getReportPixelList=function(){return this.reporters},e.assemblyReportData=function(t,e,n){if(0!==n.length){var r,o=n.map(function(t){return t.getReporterId()}),i=this.context.getPageSign(),a=n[0],c=a.assemblyData(a.getReporterId(),"",{},{},oE.PAGE);return delete c.event,c.action=t,c.auto_collected_properties=e,!c.context.pixel&&(c.context.pixel={}),c.context.pixel.code=o[0],c.context.pixel.codes=o.join("|"),c.context.index=null===(r=i.pageIndex)||void 0===r?void 0:r.index,c.context.session_id=i.sessionId,c.context.pageview_id=(0,l.gj)(this.context.getPageViewId(),a.reporterInfo.loadId,p.n2),c.message_id=c.message_id.replace(/-[^-]*$/,""),c}},t}();function rB(t,e){return(rB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rK=function(t){function e(e,n,r,o){var i;return(i=t.call(this,e,n,r,o)||this).clickTimes=0,i.scrollTimes=0,i.init(),i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,rB(n,r);var n,r,o=e.prototype;return o.init=function(){var t,e,n,r,o=this;t=rV(this.updateClickTimes,this),e=(0,l.P2)(function(e){t()},100),window.addEventListener("click",e,{capture:!0}),n=rV(this.updateScrollTimes,this),r=(0,l.P2)(function(){n()},500),window.addEventListener("scroll",r,{passive:!0}),setInterval(function(){o.reportInteraction()},1e4)},o.reportInteraction=function(){if(!!this.isUpdated())this.report(this.getResult(oM.TIME_WINDOW_TRACKER)),this.resetAfterReport()},o.getResult=function(t){return{action_event:t,inter:{ct:this.clickTimes,st:this.scrollTimes},page_inter:{metrics_type:"ct|st",ct:this.clickTimes,st:this.scrollTimes}}},o.updateClickTimes=function(){this.clickTimes+=1},o.updateScrollTimes=function(){this.scrollTimes+=1},o.isUpdated=function(){return 0!=this.clickTimes||0!=this.scrollTimes},o.resetAfterReport=function(){this.clickTimes=0,this.scrollTimes=0},e}(rH),rW=function(t){setTimeout(function(){t(oM.TIME_TO_INTERACTIVE,-1)},0)},rY=function(t){var e=function(){new Promise(function(t,e){setTimeout(function(){var n=performance.getEntriesByType("navigation");if(n.length>0){var r=n[0];t(r.loadEventEnd-r.startTime)}else window.performance.timing?t(window.performance.timing.loadEventEnd-window.performance.timing.navigationStart||-1):e("Performance timing not supported")},0)}).then(function(e){t(oM.LOAD_FINISH,e)}).catch(function(e){t(oM.LOAD_FINISH,-1)})};"complete"===window.document.readyState?e():window.addEventListener("load",e)},rJ,rX,rZ,rQ,rz,r$,r0,r1,r2,r3,r6,r5,r8,r4,r9,r7,ot,oe,on,or,oo,oi,oa,oc,os,ou,of,ol,op,oh,od,o_,ov,og,om,oy,oE,ow,ob,oO,oI,oT,oS,oN,oR,oP,oA,oL,oC,ox,ok,oD,oM,oj,oU,oq,oF,oV=-1,oG=function(t){addEventListener("pageshow",function(e){e.persisted&&(oV=e.timeStamp,t(e))},!0)},oH=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},oB=function(){var t=oH();return t&&t.activationStart||0},oK=function(t,e){var n=oH(),r="navigate";return oV>=0?r="back-forward-cache":n&&(document.prerendering||oB()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:r}},oW=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver(function(t){Promise.resolve().then(function(){e(t.getEntries())})});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},oY=function(t,e,n,r){var o,i;return function(a){var c,s;e.value>=0&&(a||r)&&((i=e.value-(o||0))||void 0===o)&&(o=e.value,e.delta=i,e.rating=(c=e.value,c>(s=n)[1]?"poor":c>s[0]?"needs-improvement":"good"),t(e))}},oJ=function(t){requestAnimationFrame(function(){return requestAnimationFrame(function(){return t()})})},oX=function(t){var e=function(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||t(e)};addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0)},oZ=function(t){var e=!1;return function(n){e||(t(n),e=!0)}},oQ=-1,oz=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},o$=function(t){"hidden"===document.visibilityState&&oQ>-1&&(oQ="visibilitychange"===t.type?t.timeStamp:0,o1())},o0=function(){addEventListener("visibilitychange",o$,!0),addEventListener("prerenderingchange",o$,!0)},o1=function(){removeEventListener("visibilitychange",o$,!0),removeEventListener("prerenderingchange",o$,!0)},o2=function(){return oQ<0&&(oQ=oz(),o0(),oG(function(){setTimeout(function(){oQ=oz(),o0()},0)})),{get firstHiddenTime(){return oQ}}},o3=function(t){document.prerendering?addEventListener("prerenderingchange",function(){return t()},!0):t()},o6=[1800,3e3],o5=function(t,e){e=e||{},o3(function(){var n,r=o2(),o=oK("FCP"),i=oW("paint",function(t){t.forEach(function(t){"first-contentful-paint"===t.name&&(i.disconnect(),t.startTime<r.firstHiddenTime&&(o.value=Math.max(t.startTime-oB(),0),o.entries.push(t),n(!0)))})});i&&(n=oY(t,o,o6,e.reportAllChanges),oG(function(r){n=oY(t,o=oK("FCP"),o6,e.reportAllChanges),oJ(function(){o.value=performance.now()-r.timeStamp,n(!0)})}))})},o8=[.1,.25],o4=function(t,e){e=e||{},o5(oZ(function(){var n,r=oK("CLS",0),o=0,i=[],a=function(t){t.forEach(function(t){if(!t.hadRecentInput){var e=i[0],n=i[i.length-1];o&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(o+=t.value,i.push(t)):(o=t.value,i=[t])}}),o>r.value&&(r.value=o,r.entries=i,n())},c=oW("layout-shift",a);c&&(n=oY(t,r,o8,e.reportAllChanges),oX(function(){a(c.takeRecords()),n(!0)}),oG(function(){o=0,n=oY(t,r=oK("CLS",0),o8,e.reportAllChanges),oJ(function(){return n()})}),setTimeout(n,0))}))},o9={passive:!0,capture:!0},o7=new Date,it=function(t,e){oj||(oj=e,oU=t,oq=new Date,io(removeEventListener),ie())},ie=function(){if(oU>=0&&oU<oq-o7){var t={entryType:"first-input",name:oj.type,target:oj.target,cancelable:oj.cancelable,startTime:oj.timeStamp,processingStart:oj.timeStamp+oU};oF.forEach(function(e){e(t)}),oF=[]}},ir=function(t){if(t.cancelable){var e,n,r,o,i,a=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;"pointerdown"==t.type?(e=a,n=t,r=function(){it(e,n),i()},o=function(){i()},i=function(){removeEventListener("pointerup",r,o9),removeEventListener("pointercancel",o,o9)},addEventListener("pointerup",r,o9),addEventListener("pointercancel",o,o9)):it(a,t)}},io=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,ir,o9)})},ii=[100,300],ia=function(t,e){e=e||{},o3(function(){var n,r=o2(),o=oK("FID"),i=function(t){t.startTime<r.firstHiddenTime&&(o.value=t.processingStart-t.startTime,o.entries.push(t),n(!0))},a=function(t){t.forEach(i)},c=oW("first-input",a);n=oY(t,o,ii,e.reportAllChanges),c&&oX(oZ(function(){a(c.takeRecords()),c.disconnect()})),c&&oG(function(){n=oY(t,o=oK("FID"),ii,e.reportAllChanges),oF=[],oU=-1,oj=null,io(addEventListener),oF.push(i),ie()})})},ic=[2500,4e3],is={},iu=function(t,e){e=e||{},o3(function(){var n,r=o2(),o=oK("LCP"),i=function(t){var e=t[t.length-1];e&&e.startTime<r.firstHiddenTime&&(o.value=Math.max(e.startTime-oB(),0),o.entries=[e],n())},a=oW("largest-contentful-paint",i);if(a){n=oY(t,o,ic,e.reportAllChanges);var c=oZ(function(){is[o.id]||(i(a.takeRecords()),a.disconnect(),is[o.id]=!0,n(!0))});["keydown","click"].forEach(function(t){addEventListener(t,function(){return setTimeout(c,0)},!0)}),oX(c),oG(function(r){n=oY(t,o=oK("LCP"),ic,e.reportAllChanges),oJ(function(){o.value=performance.now()-r.timeStamp,is[o.id]=!0,n(!0)})})}})},il=[800,1800],ip=function t(e){document.prerendering?o3(function(){return t(e)}):"complete"!==document.readyState?addEventListener("load",function(){return t(e)},!0):setTimeout(e,0)},ih=function(t,e){e=e||{};var n=oK("TTFB"),r=oY(t,n,il,e.reportAllChanges);ip(function(){var o=oH();if(o){var i=o.responseStart;if(i<=0||i>performance.now())return;n.value=Math.max(i-oB(),0),n.entries=[o],r(!0),oG(function(){(r=oY(t,n=oK("TTFB",0),il,e.reportAllChanges))(!0)})}})};function id(t,e){return(id=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var i_=function(t,e){return function(n,r){e(n,r,t)}},iv=function(t){function e(e,n,r,o){var i;return(i=t.call(this,e,n,r,o)||this).cls=-1,i.init(),i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,id(n,r);var n,r,o=e.prototype;return o.init=function(){o4(this.clsHandler.bind(this),{reportAllChanges:!0}),ih(this.webVitalHandler.bind(this)),o5(this.webVitalHandler.bind(this)),iu(this.webVitalHandler.bind(this),{reportAllChanges:!0}),ia(this.webVitalHandler.bind(this)),rW(this.baseHandler.bind(this)),rY(this.baseHandler.bind(this))},o.getResult=function(t){var e,n=Math.floor(performance&&performance.timing?Date.now()-performance.timing.navigationStart:-1),r={ttns:n,cls:rG(this.cls),idx:this.getSessionIndex(),pep:rG(this.getPep())},o=["cls","idx","pep"];o.unshift(t);var i={metrics_type:o.join("|"),cls:r.cls,idx:r.idx,pep:r.pep};return i[t]=n,{action_event:t,perf:r,page_perf:i}},o.resetAfterReport=function(){},o.clsHandler=function(t){this.cls=t.value||-1},o.webVitalHandler=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e,n={taskName:window.ttq._pf_tn||"page_data_web_vital_handler",functionName:window.ttq._pf_tn&&"page_data_web_vital_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_data_web_vital_handler")}}catch(t){}var r=this.getResult(t.name.toLocaleLowerCase());r.perf&&(r.perf.ttns=(null==t?void 0:t.value)?Math.floor(t.value):-1),r.page_perf&&(r.page_perf[t.name.toLocaleLowerCase()]=null===(e=null==r?void 0:r.perf)||void 0===e?void 0:e.ttns),this.report(r),this.resetAfterReport();try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"page_data_web_vital_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},o.baseHandler=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n,r={taskName:window.ttq._pf_tn||"page_data_base_handler",functionName:window.ttq._pf_tn&&"page_data_base_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_data_base_handler")}}catch(t){}var o=this.getResult(t);o.perf&&(o.perf.ttns=e?Math.floor(e):-1),o.page_perf&&(o.page_perf[t]=null===(n=o.perf)||void 0===n?void 0:n.ttns),this.report(o),this.resetAfterReport();try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"page_data_base_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},o.getSessionIndex=function(){var t,e=null===(t=this.context.getPageSign().pageIndex)||void 0===t?void 0:t.main;return null==e?-1:e},o.getCurrScrollPosition=function(){return document.documentElement.scrollTop||document.body.scrollTop},o.getViewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight},o.getMaxHeight=function(){var t=document.body,e=document.documentElement;return Math.max(t.scrollHeight,t.offsetHeight,e.clientHeight,e.scrollHeight,e.offsetHeight)},o.getPep=function(){var t=this.getCurrScrollPosition(),e=this.getViewportHeight(),n=this.getMaxHeight();return(t+e)/n},e}(rH);iv=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([i_(0,z(A.CONTEXT)),i_(1,z(A.TTQ_REPORTERS)),i_(2,z(A.REPORT_SERVICE))],iv);function ig(t,e){return(ig=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var im=function(t,e){return function(n,r){e(n,r,t)}},iy=function(t){function e(e,n,r,o){var i;return(i=t.call(this,{name:"PageData",reporters:n,context:e})||this).monitors=[],i.ttqOptions={},i.reportService=r,i.context=e,i.reporters=n,i.ttqOptions=o,i.isPluginInit=!1,i}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,ig(n,r);var n,r,o=e.prototype;return o.init=function(){if(!!this.isPageDataEnabled()){var t=this.isHitNewVersion();this.interactionMonitor=new rK(this.context,this.reporters,this.reportService,t?tU(oE.PAGE_INTERACTION):tU(oE.AUTO_CONFIG)),this.performanceMonitor=new iv(this.context,this.reporters,this.reportService,t?tU(oE.PAGE_PERFORMANCE):tU(oE.AUTO_CONFIG)),this.monitors.push(this.performanceMonitor),this.monitors.push(this.interactionMonitor)}},o.isPageDataEnabled=function(){var t,e;return null===(e=null===(t=this.ttqOptions)||void 0===t?void 0:t.plugins)||void 0===e?void 0:e.PageData},o.isHitNewVersion=function(){var t,e;return null===(e=null===(t=this.ttqOptions)||void 0===t?void 0:t.plugins)||void 0===e?void 0:e.HitReservoir},o.report=function(t){var e=this.performanceMonitor,n=this.interactionMonitor,r=this.performanceMonitor.getResult(t),o=this.interactionMonitor.getResult(t),i=this.mergeReportData(t,o,r);e.report(i),n.report(i),this.interactionMonitor.resetAfterReport(),this.performanceMonitor.resetAfterReport()},o.mergeReportData=function(t,e,n){var r={action_event:t};return r.perf=n.perf,r.inter=e.inter,r.page_perf=n.page_perf,r.page_inter=e.page_inter,r},o.pageWillLeave=function(t){this.report(oM.PAGE_LEAVE)},o.pixelDidMount=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"page_plugin_pixelDidMount",start:performance.now()}}catch(t){}!this.isPluginInit&&(this.init(),this.isPluginInit=!0);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},e}(eH);iy=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),im(0,z(A.CONTEXT)),im(1,z(A.TTQ_REPORTERS)),im(2,z(A.REPORT_SERVICE)),im(3,z(A.TTQ_GLOBAL_OPTIONS))],iy);var iE={initAllModule:1,webTtqFactory:2,handleCache:3,webReporterFactory:4,initAutoForm:5,auto_config_plugin_pixelDidMount:6,callback_plugin_pixelDidMount:7,console_plugin_pixelDidMount:8,event_builder_plugin_pixelDidMount:9,shopify_plugin_pixelDidMount:10,page_plugin_pixelDidMount:11,competitor_insight_plugin_init:12,getPixelInstalledPosition:13,getPixelScriptByPixelCode:14,resetExpires:15,freezeAPI:16,handlePixelRules:17,mergeWebGlobalTtq:18,handleGlobalCache:22,getPixelDetail:19,basettq_init_context_info:20,initAutoForm_getOverallFormDetail:21,web_track_handler:23,identify_api_handler:24,updateParameterInferenceData:25},iw={identify_api_handler:{id:1,fn:[]},identify_encryption:{id:2,fn:[]},identify_after_encryption:{id:3,fn:[]},track_api_handler:{id:4,fn:[]},track_after_reporter_init:{id:5,fn:[]},track_after_report_preposition:{id:6,fn:[]},init:{id:7,fn:[iE.initAllModule,iE.webTtqFactory,iE.handleCache,iE.webReporterFactory,iE.initAutoForm,iE.freezeAPI,iE.handlePixelRules,iE.resetExpires,iE.mergeWebGlobalTtq,iE.auto_config_plugin_pixelDidMount,iE.callback_plugin_pixelDidMount,iE.console_plugin_pixelDidMount,iE.event_builder_plugin_pixelDidMount,iE.shopify_plugin_pixelDidMount,iE.page_plugin_pixelDidMount,iE.competitor_insight_plugin_init,iE.getPixelInstalledPosition,iE.getPixelScriptByPixelCode,iE.handleGlobalCache,iE.basettq_init_context_info,iE.initAutoForm_getOverallFormDetail,iE.web_track_handler,iE.identify_api_handler,iE.updateParameterInferenceData]},page_api_handler:{id:8,fn:[]},auto_advanced_matching_handler:{id:9,fn:[]},auto_config_metadata_handler:{id:10,fn:[]},auto_config_click_handler:{id:11,fn:[]},auto_config_form_handler:{id:12,fn:[]},event_builder_dispatcher:{id:13,fn:[]},page_data_web_vital_handler:{id:14,fn:[]},page_data_base_handler:{id:15,fn:[]}},ib=function(){function t(){this.queue=[],this.currentTaskMap={}}var e=t.prototype;return e.handleCache=function(t){var e=this;t.forEach(function(t){e.push(t)})},e.push=function(t){var e=t.taskName,n=t.functionName,r=t.start,o=Math.round((t.end-r)*1e3),i=e&&this.getTaskIdFromName(e);if(!!i){var a=this.currentTaskMap[i];if(!a&&(this.currentTaskMap[i]=a={id:i,d:-1}),n){var c=this.getFunctionIdFromName(n);c&&(a.f=[].concat(a.f||[],[{id:c,d:o}]))}else a.d=o,this.queue.push(a),delete this.currentTaskMap[i]}},e.print=function(){return this.queue},e.printAndClear=function(){var t=this.print();return this.clear(),t},e.clear=function(){this.queue=[]},e.getTaskIdFromName=function(t){var e;return null===(e=iw[t])||void 0===e?void 0:e.id},e.getFunctionIdFromName=function(t){return iE[t]},t}();function iO(t,e){return(iO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var iI=function(t,e){return function(n,r){e(n,r,t)}},iT=function(t){var e,n;function r(e,n){var r;return(r=t.call(this,{name:"RuntimeMeasurement",reporters:n,context:e})||this).initialize=!1,r.init(),r}return e=r,n=t,e.prototype=Object._ttq_create(n.prototype),e.prototype.constructor=e,iO(e,n),r.prototype.init=function(){if(!this.initialize){this.performanceDataQueue=new ib;var t=(0,s.Ie)();t&&t._ppf&&(this.performanceDataQueue.handleCache(t._ppf),t._ppf=this.performanceDataQueue),this.initialize=!0}},r}(eH);iT=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),iI(0,z(A.CONTEXT)),iI(1,z(A.TTQ_REPORTERS))],iT);var iS=[{identifier:A.CALLBACK_PLUGIN,to:eW,name:"Callback"},{identifier:A.IDENTIFY_PLUGIN,to:ne,name:"Identify",required:!0},{identifier:A.WEB_FL_PLUGIN,to:no,name:"WebFL"},{identifier:A.AUTO_CONFIG_PLUGIN,to:re,name:"AutoConfig"},{identifier:A.DIAGNOSTICS_CONSOLE_PLUGIN,to:rR,name:"DiagnosticsConsole"},{identifier:A.PANGLE_COOKIE_MATCHING_PLUGIN,to:rL,name:"PangleCookieMatching"},{identifier:A.EVENT_BUILDER_PLUGIN,to:rM,name:"EventBuilder"},{identifier:A.ENRICH_IPV6_PLUGIN,to:rF,name:"EnrichIpv6"},{identifier:A.PAGEDATA_PLUGIN,to:iy,name:"PageData"},{identifier:A.RUNTIME_MEASUREMENT_PLUGIN,to:iT,name:"RuntimeMeasurement"}],iN=function(t,e){};function iR(t,e){return(iR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var iP=function(t){function e(e){var n;return(n=t.call(this,e)||this).observers=new Set([]),n}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,iR(n,r);var n,r,o=e.prototype;return o.addObserver=function(t){!this.observers.has(t)&&this.observers.add(t)},o.removeObserver=function(t){this.observers.delete(t)},o.notifyObservers=function(t,e){this.observers.forEach(function(n){return n.call(e,t)})},e}(eV),iA="HistoryObserver";(r||(r={})).DYNAMIC_WEB_PAGEVIEW="dynamic_web_pageview";var iL=["GoogleTagManagerClient","GoogleTagManagerServer"];function iC(t,e){var n=history[t];return function(){var t=Array.prototype.slice.call(arguments);n.apply(history,t),e()}}function ix(t){var e=t.options,n=t.plugins;return e&&!1!==e.historyObserver&&n&&n[iA]}function ik(t,e){var n,o=tn().url,i=t.context.getPageInfo().url;if(o!==i){var a=t.context.setPageInfo(o,i);a&&a.pageIndex&&w(a.pageIndex),t.reporters.filter((n=r.DYNAMIC_WEB_PAGEVIEW,function(t){var e=t.plugins;return!!(ix(t)&&e[iA]&&e[iA][n])})).forEach(function(t){setTimeout(function(){t.page(e)})})}}function iD(t,e){return(iD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var iM=function(t,e){return function(n,r){e(n,r,t)}},ij=function(t){function e(e,n){var r;return(r=t.call(this,{name:iA,reporters:n,context:e})||this).enableListenSPAHistoryChange=!1,r.listenSPAHistoryChange=(0,f.IH)(function(){var t=function(){r.enableListenSPAHistoryChange&&r.notifyObservers({tf:og.HISTORY_CHANGE})},e=(0,f.bJ)(t);window.addEventListener("popstate",e),history.pushState=iC("pushState",t),history.replaceState=iC("replaceState",t)}),r.dynamicWebPageviewHandler=ik.bind(null,r),r}n=e,r=t,n.prototype=Object._ttq_create(r.prototype),n.prototype.constructor=n,iD(n,r);var n,r,o=e.prototype;return o.initListener=function(t){this.enableListenSPAHistoryChange=function(t){if(!t)return!1;var e=(0,s.kW)(),n=t.getReporterPartner();return!(e&&!iL.includes(e)||n&&t.isPartnerReporter()&&!iL.includes(n))&&!0}(t),this.enableListenSPAHistoryChange&&this.listenSPAHistoryChange()},o.pixelSend=function(t,e){var n=this.reporters.find(function(e){return e.getReporterId()===t});if(!!n&&!!ix(n))e&&"pageview"===e.toLowerCase()&&this.addObserver(this.dynamicWebPageviewHandler),this.initListener(n)},o.pageUrlWillChange=function(){if(this.enableListenSPAHistoryChange){this.notifyObservers({tf:og.HISTORY_CHANGE});return}this.notifyObservers({tf:og.URL_CHANGE,event_experiment:"pageview"})},e}(iP);ij=function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);return i>3&&a&&Object.defineProperty(e,n,a),a}([Q(),iM(0,z(A.CONTEXT)),iM(1,z(A.TTQ_REPORTERS))],ij);var iU=[{identifier:A.HISTORY_OBSERVER,to:ij,name:"HistoryObserver"}],iq=(0,s.Ie)(),iF=(null==iq?void 0:iq._container)||new Z,iV=(null==iq?void 0:iq._container)?oy.REBIND:oy.BIND;(0,u.RA)();var iG=iF[iV](A.ENV),iH=iF[iV](L.M.SignalType);iF[iV](L.M.ID),iF[iV](L.M.Type),iF[iV](L.M.Options),iF[iV](L.M.Plugins),iF[iV](L.M.Rules),iF[iV](L.M.Info);var iB=iF[iV](L.M.WebLibraryInfo),iK=iF[iV](A.TTQ_GLOBAL_OPTIONS);try{if(!iF.get(A.TTQ_GLOBAL_OPTIONS))throw Error("")}catch(t){iK.toConstantValue({})}var iW=function(t,e){var n,r={name:"pixel.js",version:p.Jn,options:void 0},o=(0,u.b4)();iB.toConstantValue(r),iG.toConstantValue(e),iH.toConstantValue(o)},iY=function(t){!(null==t?void 0:t._container)&&(iF.bind(A.TTQ).to(t5).inSingletonScope(),iF.bind(A.CONTEXT).to(et).inSingletonScope(),iF.bind(A.REPORTER).to(tZ),iF.bind(A.TTQ_REPORTERS).toConstantValue([]),iF.bind(A.REPORT_SERVICE).to(ey).inSingletonScope(),iF.bind(A.AD_SERVICE).to(eb).inSingletonScope(),iF.bind(A.APP_SERVICE).to(eT).inSingletonScope(),iF.bind(A.BRIDGE_SERVICE).to(eL).inSingletonScope(),iF.bind(A.HTTP_SERVICE).to(ek).inSingletonScope(),iF.bind(L.M.IsOnsitePage).toConstantValue({value:!1}),iF.bind(A.COOKIE_SERVICE).to(ej).inSingletonScope(),iF.bind(A.CONSENT_SERVICE).to(eF).inSingletonScope()),t&&!t._container&&(t._container=iF)},iJ=function(){iS.forEach(function(t){var e=t.to,n=t.name,r=t.required,o=t.identifier;(void 0!==r&&r||(0,s.Wf)(void 0===n?"":n))&&!iF.isBound(o)&&iF.bind(o).to(e).inSingletonScope()})},iX=function(){iU.forEach(function(t){var e=t.to,n=t.name,r=t.identifier;(0,s.Wf)(void 0===n?"":n)&&!iF.isBound(r)&&iF.bind(r).to(e).inSingletonScope()})},iZ=function(t,e,n){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"initAllModule",start:performance.now()}}catch(t){}iW(t,n),G(e,t),iX(),iY(t),iJ(),iN(e,t);try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}},iQ=function(){try{var t=iF.get(A.MONITOR_PLUGIN);if(t)return t;return null}catch(t){return null}},iz=function(t){var e=this.constructor;return this.then(function(n){return e.resolve(t()).then(function(){return n})},function(n){return e.resolve(t()).then(function(){return e.reject(n)})})},i$=function(t){return new this(function(e,n){if(!(t&&void 0!==t.length))return n(TypeError(typeof t+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);for(var o=r.length,i=0;i<r.length;i++)!function t(n,i){if(i&&("object"==typeof i||"function"==typeof i)){var a=i.then;if("function"==typeof a){a.call(i,function(e){t(n,e)},function(t){r[n]={status:"rejected",reason:t},0==--o&&e(r)});return}}r[n]={status:"fulfilled",value:i},0==--o&&e(r)}(i,r[i])})};function i0(t,e){this.name="AggregateError",this.errors=t,this.message=e||""}i0.prototype=Error.prototype;var i1=function(t){var e=this;return new e(function(n,r){if(!(t&&void 0!==t.length))return r(TypeError("Promise.any accepts an array"));var o=Array.prototype.slice.call(t);if(0===o.length)return r();for(var i=[],a=0;a<o.length;a++)try{e.resolve(o[a]).then(n).catch(function(t){i.push(t),i.length===o.length&&r(new i0(i,"All promises were rejected"))})}catch(t){r(t)}})},i2=setTimeout;function i3(t){return!!(t&&void 0!==t.length)}function i6(){}function i5(t){if(!(this instanceof i5))throw TypeError("Promises must be constructed via new");if("function"!=typeof t)throw TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],ae(t,this)}function i8(t,e){for(;3===t._state;)t=t._value;if(0===t._state){t._deferreds.push(e);return}t._handled=!0,i5._immediateFn(function(){var n,r=1===t._state?e.onFulfilled:e.onRejected;if(null===r){(1===t._state?i4:i9)(e.promise,t._value);return}try{n=r(t._value)}catch(t){i9(e.promise,t);return}i4(e.promise,n)})}function i4(t,e){try{if(e===t)throw TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n,r,o=e.then;if(e instanceof i5){t._state=3,t._value=e,i7(t);return}if("function"==typeof o){;ae((n=o,r=e,function(){n.apply(r,arguments)}),t);return}}t._state=1,t._value=e,i7(t)}catch(e){i9(t,e)}}function i9(t,e){t._state=2,t._value=e,i7(t)}function i7(t){2===t._state&&0===t._deferreds.length&&i5._immediateFn(function(){!t._handled&&i5._unhandledRejectionFn(t._value)});for(var e=0,n=t._deferreds.length;e<n;e++)i8(t,t._deferreds[e]);t._deferreds=null}function at(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function ae(t,e){var n=!1;try{t(function(t){!n&&(n=!0,i4(e,t))},function(t){!n&&(n=!0,i9(e,t))})}catch(t){if(n)return;n=!0,i9(e,t)}}i5.prototype.catch=function(t){return this.then(null,t)},i5.prototype.then=function(t,e){var n=new this.constructor(i6);return i8(this,new at(t,e,n)),n},i5.prototype.finally=iz,i5.all=function(t){return new i5(function(e,n){if(!i3(t))return n(TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);for(var o=r.length,i=0;i<r.length;i++)!function t(i,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var c=a.then;if("function"==typeof c){c.call(a,function(e){t(i,e)},n);return}}r[i]=a,0==--o&&e(r)}catch(t){n(t)}}(i,r[i])})},i5.any=i1,i5.allSettled=i$,i5.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i5?t:new i5(function(e){e(t)})},i5.reject=function(t){return new i5(function(e,n){n(t)})},i5.race=function(t){return new i5(function(e,n){if(!i3(t))return n(TypeError("Promise.race accepts an array"));for(var r=0,o=t.length;r<o;r++)i5.resolve(t[r]).then(e,n)})},i5._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){i2(t,0)},i5._unhandledRejectionFn=function(t){"undefined"!=typeof console};var an=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==n.g)return n.g;throw Error("unable to locate global object")}();"function"!=typeof an.Promise?an.Promise=i5:(!an.Promise.prototype.finally&&(an.Promise.prototype.finally=iz),!an.Promise.allSettled&&(an.Promise.allSettled=i$),!an.Promise.any&&(an.Promise.any=i1));var ar=(0,s.Gp)();i=n(666).Z;try{!function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var t={taskName:window.ttq._pf_tn||"init",functionName:window.ttq._pf_tn&&"init",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="init")}}catch(t){}var e=(0,a.wE)().pixelCode,n=(0,s.Ie)(),r=(0,u.RA)(),l=(0,u.b4)();if(iZ(n,iF,r),(0,s.Wf)("Monitor")){var p=iQ();null==p||p.info(c.O.BEFORE_INIT,{pixelCode:e,extJSON:{stack:(0,f.xF)(e)}}),T.init()}if(!!n){n._mounted?((0,a.um)(c.O.HANDLE_CACHE,{pixelCode:e}),B(iF,n)):(o=U(iF,r,l),window[ar]=F(n,o),n.resetCookieExpires&&n.resetCookieExpires(),B(iF,o),H(o));var h=iF.get(L.M.IsOnsitePage);h.value=l===P.S.ONSITE||n.reporters.every(function(t){return t.isOnsite()}),iF.rebind(L.M.IsOnsitePage).toConstantValue(h),!function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"handlePixelRules",start:performance.now()}}catch(t){}t.reporters.forEach(function(t){t.rules&&i&&new i(t.getReporterId(),t.rules)});try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}}(n);try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t),"init"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}}}()}catch(t){(0,a.vU)(c.O.INIT_ERROR,t)}}()}();