<form
    id="new-password-form"
    class="htmx-spinner"
    hx-post="/api/auth/new-password"
    hx-vals='{"reset_password_token":"{{reset_password_token}}"}'
    hx-sync="this:abort"
    hx-swap="outerHTML"
  > 
  <label class="{{#if errors.new_password}}error{{/if}}">
    New password:
    <input 
      id="new_password" 
      name="new_password" 
      type="password" 
      placeholder="New password..."
      hx-preserve="true"
      required 
    />
    {{#if errors.new_password}}<p class="error">{{errors.new_password}}</p>{{/if}}
  </label>
  <label class="{{#if errors.repeat_password}}error{{/if}}">
    Repeat password:
    <input 
      id="repeat_password" 
      name="repeat_password" 
      type="password" 
      placeholder="Repeat password..."
      hx-preserve="true"
      required 
    />
    {{#if errors.repeat_password}}<p class="error">{{errors.repeat_password}}</p>{{/if}}
  </label>
  <button type="submit" class="primary">
    <span>{{> icons/spinner}}</span>
    Set password
  </button>
  {{#unless errors}}
    {{#if error}}
      <p class="error">{{error}}</p>
    {{/if}}
  {{/unless}}
</form>