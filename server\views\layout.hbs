<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <!-- RESEARCH-BASED: Optimal viewport meta tag for mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
  <link rel="icon" sizes="196x196" href="/images/favicon-196x196.png" />
  <link rel="icon" sizes="32x32" href="/images/favicon-32x32.png" />
  <link rel="icon" sizes="16x16" href="/images/favicon-16x16.png" />
  <link rel="apple-touch-icon" href="/images/favicon-196x196.png" />
  <link rel="mask-icon" href="/images/icon.svg" color="blue" />
  <link rel="manifest" href="/manifest.webmanifest" />
  <meta name="theme-color" content="#f3f3f3" />
  <meta property="fb:app_id" content="123456789" />
  <meta name="htmx-config" content='{"withCredentials":true}'>
  <meta property="og:url" content="https://{{default_domain}}" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="{{site_name}}" />
  <meta property="og:image" content="https://{{default_domain}}/images/card.png" />
  <meta property="og:description" content="Free & Open Source Modern URL Shortener" />
  <meta name="twitter:url" content="https://{{default_domain}}" />
  <meta name="twitter:title" content="{{site_name}}" />
  <meta name="twitter:description" content="Free & Open Source Modern URL Shortener" />
  <meta name="twitter:image" content="https://{{default_domain}}/images/card.png" />
  <meta name="description" content="{{site_name}} is a free and open source URL shortener with custom domains and stats." />
  <title>{{site_name}} | {{title}}</title>
  <link rel="stylesheet" href="/css/styles.css">
  {{#each custom_styles}}
    <link rel="stylesheet" href="/css/{{this}}">
  {{/each}}
  {{{block "stylesheets"}}}
</head>
<body>
  <div class="main-wrapper">
    {{{body}}}
  </div>

  <!-- Global Dialogs -->
  <div id="dialog" class="dialog">
    <div class="box">
      <div class="content-wrapper">
        <div class="content">
          <h2>Dialog</h2>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Drop Modal (only for logged in users) -->
  {{#if user}}
  <div id="create-drop-dialog" class="dialog">
    <div class="box">
      <div class="content-wrapper">
        <div class="content">
          <h2>Create New Drop</h2>
          <form id="create-drop-form">
            <div class="form-group">
              <label for="drop-title">Title *</label>
              <input type="text" id="drop-title" name="title" required maxlength="255" placeholder="Enter drop title">
            </div>

            <div class="form-group">
              <label for="drop-description">Description</label>
              <textarea id="drop-description" name="description" rows="3" placeholder="Describe what this drop is about"></textarea>
            </div>

            <div class="form-group">
              <label for="drop-slug">URL Slug</label>
              <div class="slug-input-wrapper">
                <span class="slug-prefix">{{default_domain}}/drop/</span>
                <input type="text" id="drop-slug" name="slug" maxlength="100" placeholder="my-drop">
              </div>
              <small class="form-help">Leave empty to auto-generate from title</small>
            </div>

            <div class="buttons">
              <button type="button" onclick="closeDialog()">Cancel</button>
              <button type="submit" class="primary">
                <span>{{> icons/plus}}</span>
                Create Drop
              </button>
            </div>
          </form>

          <div id="create-drop-error" class="error" style="display: none;"></div>

          {{> icons/spinner}}
        </div>
      </div>
    </div>
  </div>
  {{/if}}

  {{{block "scripts"}}}
  <script src="/libs/htmx.min.js"></script>
  <script src="/libs/qrcode.min.js"></script>
  <script src="/scripts/main.js"></script>
</body>
</html>