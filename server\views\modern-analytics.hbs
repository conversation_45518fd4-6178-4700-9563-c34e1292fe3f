<!-- Analytics Dashboard -->
<div class="space-y-6">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Analytics
            </h2>
            <p class="mt-1 text-sm text-gray-500">
                Track your drops and links performance with detailed insights.
            </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <button onclick="exportData()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Data
            </button>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Drops</dt>
                            <dd class="text-lg font-medium text-gray-900">{{stats.totalDrops}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Drops</dt>
                            <dd class="text-lg font-medium text-gray-900">{{stats.activeDrops}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Links</dt>
                            <dd class="text-lg font-medium text-gray-900">{{stats.totalLinks}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Fans</dt>
                            <dd class="text-lg font-medium text-gray-900">{{stats.totalFans}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
                            <dd class="text-lg font-medium text-gray-900">{{stats.totalClicks}}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Performance Chart -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Performance Overview</h3>
                <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-500">Chart visualization coming soon</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Content -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Top Performing</h3>
                
                <!-- Top Drops -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">Top Drops</h4>
                    {{#if recentDrops.length}}
                    <div class="space-y-3">
                        {{#each recentDrops}}
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{this.title}}</p>
                                <p class="text-sm text-gray-500">{{this.signup_count}} fans</p>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{#if this.is_active}}bg-green-100 text-green-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                                    {{#if this.is_active}}Active{{else}}Inactive{{/if}}
                                </span>
                            </div>
                        </div>
                        {{/each}}
                    </div>
                    {{else}}
                    <p class="text-sm text-gray-500">No drops yet</p>
                    {{/if}}
                </div>

                <!-- Top Links -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-3">Top Links</h4>
                    {{#if recentLinks.length}}
                    <div class="space-y-3">
                        {{#each recentLinks}}
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{this.title}}</p>
                                <p class="text-sm text-gray-500">{{this.visit_count}} clicks</p>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{this.short_url}}" target="_blank" class="text-primary-600 hover:text-primary-500">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                        {{/each}}
                    </div>
                    {{else}}
                    <p class="text-sm text-gray-500">No links yet</p>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Tables -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Detailed Analytics</h3>
                <div class="flex space-x-2">
                    <button onclick="showDropsAnalytics()" id="dropsTab" class="px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md">
                        Drops
                    </button>
                    <button onclick="showLinksAnalytics()" id="linksTab" class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                        Links
                    </button>
                    <button onclick="showFansAnalytics()" id="fansTab" class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                        Recent Fan Signups
                    </button>
                </div>
            </div>

            <!-- Drops Analytics Table -->
            <div id="dropsAnalytics" class="block">
                {{#if recentDrops.length}}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drop</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fans</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{#each recentDrops}}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{this.title}}</div>
                                    <div class="text-sm text-gray-500">{{this.description}}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{#if this.is_active}}bg-green-100 text-green-800{{else}}bg-gray-100 text-gray-800{{/if}}">
                                        {{#if this.is_active}}Active{{else}}Inactive{{/if}}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{this.signup_count}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{formatDate this.created_at "MMM dd, yyyy"}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="/drops/{{this.id}}/edit" class="text-primary-600 hover:text-primary-900">Edit</a>
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
                {{else}}
                <div class="text-center py-8">
                    <p class="text-sm text-gray-500">No drops data available</p>
                </div>
                {{/if}}
            </div>

            <!-- Links Analytics Table -->
            <div id="linksAnalytics" class="hidden">
                {{#if recentLinks.length}}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Link</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Short URL</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{#each recentLinks}}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{this.title}}</div>
                                    <div class="text-sm text-gray-500 truncate max-w-xs">{{this.target}}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-primary-600 font-mono">{{this.short_url}}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{this.visit_count}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{formatDate this.created_at "MMM dd, yyyy"}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{this.short_url}}" target="_blank" class="text-primary-600 hover:text-primary-900">Visit</a>
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
                {{else}}
                <div class="text-center py-8">
                    <p class="text-sm text-gray-500">No links data available</p>
                </div>
                {{/if}}
            </div>

            <!-- Fan Signups Analytics Table -->
            <div id="fansAnalytics" class="hidden">
                <div class="mb-4 flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Recent Fan Signups</h4>
                        <p class="text-sm text-gray-500">Click on any row to view detailed information</p>
                    </div>
                    <button onclick="refreshFanSignups()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Refresh
                    </button>
                </div>

                <div id="fanSignupsContainer">
                    <div class="text-center py-8">
                        <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading fan signups...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics JavaScript -->
<script>
function showDropsAnalytics() {
    document.getElementById('dropsAnalytics').classList.remove('hidden');
    document.getElementById('linksAnalytics').classList.add('hidden');
    document.getElementById('fansAnalytics').classList.add('hidden');

    setActiveTab('dropsTab');
}

function showLinksAnalytics() {
    document.getElementById('dropsAnalytics').classList.add('hidden');
    document.getElementById('linksAnalytics').classList.remove('hidden');
    document.getElementById('fansAnalytics').classList.add('hidden');

    setActiveTab('linksTab');
}

function showFansAnalytics() {
    document.getElementById('dropsAnalytics').classList.add('hidden');
    document.getElementById('linksAnalytics').classList.add('hidden');
    document.getElementById('fansAnalytics').classList.remove('hidden');

    setActiveTab('fansTab');
    loadFanSignups();
}

function setActiveTab(activeTabId) {
    const tabs = ['dropsTab', 'linksTab', 'fansTab'];
    tabs.forEach(tabId => {
        const tab = document.getElementById(tabId);
        if (tabId === activeTabId) {
            tab.classList.add('text-primary-600', 'bg-primary-50');
            tab.classList.remove('text-gray-500', 'bg-gray-100');
        } else {
            tab.classList.add('text-gray-500', 'bg-gray-100');
            tab.classList.remove('text-primary-600', 'bg-primary-50');
        }
    });
}

function exportData() {
    // Export analytics data
    const data = {
        stats: {
            totalDrops: {{stats.totalDrops}},
            activeDrops: {{stats.activeDrops}},
            totalLinks: {{stats.totalLinks}},
            totalFans: {{stats.totalFans}},
            totalClicks: {{stats.totalClicks}}
        },
        exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'analytics-export.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showToast('Analytics data exported successfully!', 'success');
}

// Fan Signups functionality
async function loadFanSignups() {
    try {
        const container = document.getElementById('fanSignupsContainer');
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading fan signups...
                </div>
            </div>
        `;

        const response = await fetch('/api/drops/analytics/fans');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        if (result.success && result.data.fans.length > 0) {
            displayFanSignups(result.data.fans);
        } else {
            container.innerHTML = `
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No fan signups yet</h3>
                    <p class="mt-1 text-sm text-gray-500">When people sign up for your drops, they'll appear here.</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Failed to load fan signups:', error);
        document.getElementById('fanSignupsContainer').innerHTML = `
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading fan signups</h3>
                <p class="mt-1 text-sm text-gray-500">${error.message}</p>
                <button onclick="loadFanSignups()" class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Try Again
                </button>
            </div>
        `;
    }
}

function displayFanSignups(fans) {
    const container = document.getElementById('fanSignupsContainer');

    if (fans.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8">
                <p class="text-sm text-gray-500">No fan signups found</p>
            </div>
        `;
        return;
    }

    const tableHTML = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Join Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acq. Channel</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">RSVP Count</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${fans.map((fan, index) => createFanRow(fan, index)).join('')}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = tableHTML;
}

function createFanRow(fan, index) {
    const joinDate = new Date(fan.join_date).toLocaleDateString();
    const location = fan.location ? `${fan.location.city || 'Unknown'}, ${fan.location.state || fan.location.country || ''}` : 'Unknown';

    return `
        <tr class="hover:bg-gray-50 cursor-pointer transition-colors duration-200" onclick="toggleFanDetails(${index})">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <span class="text-sm font-medium text-primary-600">${(fan.name || fan.email || 'A').charAt(0).toUpperCase()}</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${fan.name || 'Anonymous'}</div>
                        <div class="text-sm text-gray-500">${fan.email || fan.phone || 'No contact info'}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${location}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${joinDate}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getChannelBadgeClass(fan.acquisition_channel)}">
                    ${fan.acquisition_channel || 'Direct'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${fan.total_rsvps || 1}</td>
        </tr>
        <tr id="fan-details-${index}" class="hidden">
            <td colspan="5" class="px-6 py-4 bg-gray-50">
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Contact Information</h4>
                            <dl class="space-y-1">
                                ${fan.name ? `<div><dt class="text-xs text-gray-500">Name:</dt><dd class="text-sm text-gray-900">${fan.name}</dd></div>` : ''}
                                ${fan.email ? `<div><dt class="text-xs text-gray-500">Email:</dt><dd class="text-sm text-gray-900">${fan.email}</dd></div>` : ''}
                                ${fan.phone ? `<div><dt class="text-xs text-gray-500">Phone:</dt><dd class="text-sm text-gray-900">${fan.phone}</dd></div>` : ''}
                                ${fan.ip_address ? `<div><dt class="text-xs text-gray-500">IP Address:</dt><dd class="text-sm text-gray-900">${fan.ip_address}</dd></div>` : ''}
                            </dl>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Signup Details</h4>
                            <dl class="space-y-1">
                                <div><dt class="text-xs text-gray-500">Drop:</dt><dd class="text-sm text-gray-900">${fan.drop_title}</dd></div>
                                <div><dt class="text-xs text-gray-500">Signup Date:</dt><dd class="text-sm text-gray-900">${new Date(fan.join_date).toLocaleString()}</dd></div>
                                <div><dt class="text-xs text-gray-500">Acquisition Channel:</dt><dd class="text-sm text-gray-900">${fan.acquisition_channel || 'Direct'}</dd></div>
                                ${fan.referrer ? `<div><dt class="text-xs text-gray-500">Referrer:</dt><dd class="text-sm text-gray-900 truncate">${fan.referrer}</dd></div>` : ''}
                            </dl>
                        </div>
                    </div>
                    ${fan.fan_drops && fan.fan_drops.length > 1 ? `
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">All Drop Signups (${fan.fan_drops.length})</h4>
                            <div class="space-y-1">
                                ${fan.fan_drops.map(drop => `
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-900">${drop.title}</span>
                                        <span class="text-gray-500">${new Date(drop.created_at).toLocaleDateString()}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </td>
        </tr>
    `;
}

function getChannelBadgeClass(channel) {
    switch (channel) {
        case 'social': return 'bg-blue-100 text-blue-800';
        case 'referral': return 'bg-green-100 text-green-800';
        case 'search': return 'bg-yellow-100 text-yellow-800';
        case 'email': return 'bg-purple-100 text-purple-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function toggleFanDetails(index) {
    const detailsRow = document.getElementById(`fan-details-${index}`);
    if (detailsRow.classList.contains('hidden')) {
        detailsRow.classList.remove('hidden');
        detailsRow.classList.add('animate-fadeIn');
    } else {
        detailsRow.classList.add('hidden');
        detailsRow.classList.remove('animate-fadeIn');
    }
}

function refreshFanSignups() {
    loadFanSignups();
    showToast('Fan signups refreshed!', 'success');
}

// Toast notification function
function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
}
</script>

<style>
.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsive adjustments for fan signups table */
@media (max-width: 768px) {
    .fan-signups-table th:nth-child(2),
    .fan-signups-table td:nth-child(2),
    .fan-signups-table th:nth-child(4),
    .fan-signups-table td:nth-child(4) {
        display: none;
    }
}
</style>
