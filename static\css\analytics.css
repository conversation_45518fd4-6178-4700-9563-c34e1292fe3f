/* 🚀 LAYLO-STYLE ANALYTICS DASHBOARD STYLES */


/* ===== ANALYTICS DASHBOARD LAYOUT ===== */

.analytics-dashboard {
    padding: 32px;
    max-width: 1200px;
    margin: 0 auto;
    background: #fafbfc;
    min-height: 600px;
}


/* ===== SUMMARY STATS GRID ===== */

.analytics-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.analytics-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 10px rgba(0, 0, 0, 0.1);
}

.analytics-card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.card-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card-header svg {
    color: #94a3b8;
    opacity: 0.7;
}

.card-value {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1;
}

.card-change {
    font-size: 13px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.card-change.positive {
    color: #059669;
    background: #d1fae5;
}

.card-change.negative {
    color: #dc2626;
    background: #fee2e2;
}

.card-change.neutral {
    color: #6b7280;
    background: #f3f4f6;
}


/* ===== FANS SECTION ===== */

.fans-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
}


/* RESEARCH-BASED HEADER LAYOUT */

.fans-header {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-areas: "title controls";
    align-items: start;
    gap: 24px;
    padding: 24px 32px;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.fans-title {
    grid-area: title;
    min-width: 0;
    /* Allows text to wrap */
}

.fans-title h2 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 6px 0;
    line-height: 1.3;
}

.fans-title p {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

.fans-controls {
    grid-area: controls;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: flex-end;
}


/* ===== SEARCH AND CONTROLS ===== */

.search-container {
    position: relative;
    min-width: 280px;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    pointer-events: none;
}

.sort-select {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    font-size: 14px;
    background: white;
    min-width: 160px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sort-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.export-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}


/* ===== TABLE STYLES ===== */

.fans-table-container {
    position: relative;
    min-height: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 32px;
    color: #64748b;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}


/* ===== RESEARCH-BASED RESPONSIVE TABLE SYSTEM ===== */


/* Desktop Table - Default View */

.table-desktop {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    max-width: 100%;
}

.table-desktop::-webkit-scrollbar {
    height: 8px;
}

.table-desktop::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.table-desktop::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.table-desktop::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.fans-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
    table-layout: auto;
    /* Let browser calculate optimal widths */
}


/* Mobile Cards - Hidden by default */

.table-mobile {
    display: none;
}


/* Header styling moved to avoid conflicts */


/* ===== RESPONSIVE COLUMN SYSTEM ===== */


/* Desktop: Use percentage-based widths for proper scaling */

@media (min-width: 769px) {
    .fans-table {
        min-width: 900px;
        /* Minimum width for desktop readability */
    }
    /* Contact Column - 40% */
    .fans-table thead th:nth-child(1),
    .fans-table tbody td:nth-child(1) {
        width: 40%;
        min-width: 300px;
    }
    /* Location Column - 20% */
    .fans-table thead th:nth-child(2),
    .fans-table tbody td:nth-child(2) {
        width: 20%;
        min-width: 150px;
    }
    /* Date Column - 15% */
    .fans-table thead th:nth-child(3),
    .fans-table tbody td:nth-child(3) {
        width: 15%;
        min-width: 120px;
    }
    /* Channel Column - 15% */
    .fans-table thead th:nth-child(4),
    .fans-table tbody td:nth-child(4) {
        width: 15%;
        min-width: 120px;
    }
    /* RSVP Column - 10% */
    .fans-table thead th:nth-child(5),
    .fans-table tbody td:nth-child(5) {
        width: 10%;
        min-width: 80px;
        text-align: center;
    }
}

.fans-table tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
    cursor: pointer;
}

.fans-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f3f4f6 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.fans-table tbody tr.repeat-fan {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.03) 0%, rgba(255, 255, 255, 0) 100%);
    border-left: 3px solid #3b82f6;
}

.fans-table tbody tr.repeat-fan:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.08) 0%, rgba(248, 250, 252, 1) 100%);
}

.fans-table tbody td {
    padding: 16px 20px;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0;
    /* Forces text-overflow to work */
}


/* ===== TABLE HEADER STYLING ===== */

.fans-table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e2e8f0;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}


/* ===== TABLE CELL STYLES ===== */

.contact-cell {
    position: relative;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.contact-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.contact-email {
    color: #64748b;
    font-size: 13px;
}

.contact-phone {
    color: #64748b;
    font-size: 12px;
}

.repeat-badge {
    position: absolute;
    top: -2px;
    right: -8px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.location-cell {
    color: #64748b;
    font-size: 13px;
}

.date-cell {
    color: #64748b;
    font-size: 13px;
    white-space: nowrap;
}

.channel-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.channel-direct {
    background: #f3f4f6;
    color: #6b7280;
}

.channel-instagram {
    background: #fce7f3;
    color: #be185d;
}

.channel-twitter {
    background: #dbeafe;
    color: #1d4ed8;
}

.channel-facebook {
    background: #dbeafe;
    color: #1e40af;
}

.channel-tiktok {
    background: #f3e8ff;
    color: #7c3aed;
}

.channel-youtube {
    background: #fef3c7;
    color: #d97706;
}

.channel-google {
    background: #dcfce7;
    color: #059669;
}

.channel-other {
    background: #f1f5f9;
    color: #64748b;
}

.rsvp-cell {
    text-align: center;
}

.rsvp-count {
    display: block;
    font-weight: 700;
    font-size: 16px;
    color: #1e293b;
}

.rsvp-drops {
    display: block;
    font-size: 11px;
    color: #64748b;
    margin-top: 2px;
}


/* ===== PAGINATION ===== */

.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 32px;
    border-top: 1px solid #e2e8f0;
    background: #fafbfc;
}

.pagination-info {
    color: #64748b;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #9ca3af;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 4px;
}

.page-number {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

.page-number.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}


/* ===== EMPTY STATE ===== */

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 32px;
    text-align: center;
}

.empty-icon {
    color: #cbd5e1;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    color: #64748b;
    margin: 0 0 8px 0;
}

.empty-state p {
    color: #94a3b8;
    margin: 0;
}

.retry-btn {
    margin-top: 16px;
    padding: 10px 20px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-btn:hover {
    background: #2563eb;
}


/* ===== MOBILE CARD LAYOUT ===== */

.fans-cards {
    display: none;
    gap: 16px;
    padding: 20px;
}

.fan-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.fan-card.repeat-fan {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, white 100%);
}

.fan-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.fan-card-contact {
    flex: 1;
}

.fan-card-name {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.fan-card-email {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 2px;
}

.fan-card-phone {
    font-size: 13px;
    color: #64748b;
}

.fan-card-badges {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.fan-card-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding-top: 16px;
    border-top: 1px solid #f1f5f9;
}

.fan-card-detail {
    display: flex;
    flex-direction: column;
}

.fan-card-detail-label {
    font-size: 12px;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.fan-card-detail-value {
    font-size: 14px;
    color: #1e293b;
    font-weight: 500;
}


/* ===== FAN MODAL STYLES ===== */

.fan-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.fan-modal.active {
    display: flex;
}

.fan-modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.fan-modal-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 24px;
}

.fan-modal-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.fan-modal-title h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.fan-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.fan-modal-close:hover {
    background: #f1f5f9;
    color: #374151;
}

.fan-modal-contact {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-bottom: 24px;
}

.fan-modal-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
}

.fan-modal-contact-info h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
}

.fan-modal-contact-info p {
    font-size: 14px;
    color: #64748b;
    margin: 0;
}

.fan-modal-body {
    padding: 0 24px 24px 24px;
}

.fan-modal-section {
    margin-bottom: 32px;
}

.fan-modal-section:last-child {
    margin-bottom: 0;
}

.fan-modal-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.fan-modal-section-icon {
    width: 20px;
    height: 20px;
    color: #3b82f6;
}

.fan-modal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.fan-modal-field {
    display: flex;
    flex-direction: column;
}

.fan-modal-field-label {
    font-size: 12px;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 6px;
}

.fan-modal-field-value {
    font-size: 14px;
    color: #1e293b;
    font-weight: 500;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.fan-modal-drops-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fan-modal-drop-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.fan-modal-drop-name {
    font-weight: 500;
    color: #1e293b;
}

.fan-modal-drop-date {
    font-size: 13px;
    color: #64748b;
}


/* ===== MOBILE RESPONSIVE ===== */

@media (max-width: 768px) {
    .analytics-dashboard {
        padding: 16px;
    }
    .analytics-summary-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }
    .fans-header {
        grid-template-columns: 1fr;
        grid-template-areas: "title" "controls";
        gap: 20px;
        padding: 20px;
    }
    .fans-title h2 {
        font-size: 20px;
    }
    .fans-controls {
        justify-content: stretch;
        flex-direction: column;
        gap: 12px;
    }
    .search-container {
        min-width: auto;
    }
    .search-input,
    .sort-select {
        width: 100%;
    }
    /* Hide desktop table, show mobile cards */
    .table-desktop {
        display: none;
    }
    .table-mobile {
        display: block;
    }
    .fans-cards {
        display: flex;
        flex-direction: column;
    }
    .table-pagination {
        flex-direction: column;
        gap: 16px;
        padding: 16px 20px;
    }
    .pagination-controls {
        width: 100%;
        justify-content: center;
    }
    /* Modal adjustments for mobile */
    .fan-modal {
        padding: 10px;
    }
    .fan-modal-content {
        max-height: 95vh;
        border-radius: 12px;
    }
    .fan-modal-header,
    .fan-modal-body {
        padding: 20px;
    }
    .fan-modal-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    .fan-modal-contact {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
}