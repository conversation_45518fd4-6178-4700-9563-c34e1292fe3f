<thead>
  {{> admin/table_tab title='domains'}}
  <tr class="controls domains-controls with-filters">
    <th class="filters">
      <div>
        <div class="search-input-wrapper">
          <input 
            id="search" 
            name="search" 
            type="text" 
            placeholder="Search domain..." 
            class="table-input search admin" 
            hx-on:input="onSearchChange(event)" 
            hx-on:keyup="resetTableNav()"
            value="{{query.search}}"
          />
          <button 
            type="button" 
            aria-label="Clear search" 
            class="clear" 
            onclick="clearSeachInput(event)"
          >
            {{> icons/x}}
          </button>
        </div>
        <div class="search-input-wrapper">
          <input 
            id="search_user" 
            name="user" 
            type="text" 
            placeholder="Search user..." 
            class="table-input search admin" 
            hx-on:input="onSearchChange(event)" 
            hx-on:keyup="resetTableNav()"
            value="{{query.user}}"
          />
          <button 
            type="button" 
            aria-label="Clear user" 
            class="clear" 
            onclick="clearSeachInput(event)"
          >
            {{> icons/x}}
          </button>
        </div>
        <select id="domains-select-banned" name="banned" class="table-input ban" hx-on:change="resetTableNav()">
          <option value="" selected>Banned...</option>
          <option value="true">Banned</option>
          <option value="false">Not banned</option>
        </select>
      </div>
      <div>
        <select id="domains-select-links" name="links" class="table-input links" hx-on:change="resetTableNav()">
          <option value="" selected>Links...</option>
          <option value="true" {{#ifEquals query.links 'true'}}selected{{/ifEquals}}>With links</option>
          <option value="false" {{#ifEquals query.links 'true'}}selected{{/ifEquals}}>No links</option>
        </select>
        <select id="domains-select-owner" name="owner" class="table-input owner" hx-on:change="resetTableNav()">
          <option value="" selected>Owner...</option>
          <option value="true" {{#ifEquals query.owner 'true'}}selected{{/ifEquals}}>With owner</option>
          <option value="false" {{#ifEquals query.owner 'true'}}selected{{/ifEquals}}>No owner</option>
        </select>
        <input id="total" name="total" type="hidden" value="{{total}}" />
        <input id="limit" name="limit" type="hidden" value="10" />
        <input id="skip" name="skip" type="hidden" value="0" />
        <button 
          class="table primary"
          hx-on:click='openDialog("admin-table-dialog")' 
          hx-get="/add-domain" 
          hx-target="#admin-table-dialog .content-wrapper" 
          hx-indicator="#admin-table-dialog"
        >
          <span>{{> icons/plus}}</span>
          Add domain
        </button>
      </div>
    </th>
    {{> admin/table_nav}}
  </tr>
  <tr>
    <th class="domains-id">ID</th>
    <th class="domains-address">Address</th>
    <th class="domains-homepage">Homepage</th>
    <th class="domains-created-at">Created at</th>
    <th class="domains-links-count">Total links</th>
    <th class="domains-actions"></th>
  </tr>
</thead>