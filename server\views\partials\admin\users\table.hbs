<table 
  hx-get="/api/users/admin"
  hx-target="tbody"
  hx-swap="outerHTML" 
  hx-select="tbody"
  hx-disinherit="*"
  hx-include=".users-controls"
  hx-params="not total"
  hx-sync="this:replace"
  hx-select-oob="#total,#category-total" 
  hx-trigger="
    {{#if onload}}load once,{{/if}}
    reloadMainTable from:body,
    click delay:100ms from:button.nav, 
    input changed delay:500ms from:[name='search'],
    input changed from:[name='verified'],
    input changed from:[name='banned'],
    input changed from:[name='role'],
    input changed from:[name='domains'],
    input changed from:[name='links'],
  "
  hx-on:htmx:after-on-load="updateLinksNav()"
  hx-on:htmx:after-settle="onSearchInputLoad();"
>
  {{> admin/users/thead}}
  {{> admin/users/tbody}}
  {{> admin/users/tfoot}}
</table>
<template>
  <h2 id="admin-table-title" hx-swap-oob="true">Recent created users.</h2>
</template>