
<html lang="en">
  <head>
    <title>Visit www.pixelrocket.store to learn how to become a frontend web developer</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link rel="preconnect" href="https://fonts.gstatic.com"/>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&amp;display=swap" rel="stylesheet"/>
    <link href="https://api.fontshare.com/v2/css?f[]=clash-grotesk@400,300,500&amp;display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="css/tailwind/tailwind.min.css"/>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon.png"/>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer="defer"></script>
  </head>
  <body class="antialiased bg-body text-body font-body">
    <div>
      <p class="py-4 bg-green-500 text-green-900 text-center">Want to learn how to build websites like this one? <a href="https://www.pixelrocket.store">Visit Pixel Rocket</a></p>
      <section x-data="{ mobileNavOpen: false }">
        <div class="container px-4 mx-auto">
          <div class="flex items-center justify-between pt-5 pb-2.5 -m-2">
            <div class="w-auto p-2">
              <div class="flex flex-wrap items-center">
                <div class="w-auto"><a class="relative z-10 inline-block" href="index.html"><img src="images/logo.svg" alt=""/></a></div>
              </div>
            </div>
            <div class="w-auto p-2">
              <div class="flex flex-wrap items-center">
                <div class="w-auto hidden lg:block">
                  <ul class="flex items-center mr-12">
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="about.html">About</a></li>
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="pricing.html">Pricing</a></li>
                    <li class="mr-12 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="blog.html">Blog</a></li>
                    <li class="text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="contact.html">Contact</a></li>
                  </ul>
                </div>
                <div class="w-auto hidden lg:block">
                  <div class="inline-block"><a class="inline-block px-8 py-4 text-white hover:text-black tracking-tighter hover:bg-green-400 border-2 border-white focus:border-green-400 focus:border-opacity-40 hover:border-green-400 focus:ring-4 focus:ring-green-400 focus:ring-opacity-40 rounded-full transition duration-300" href="login.html">Login</a></div>
                </div>
                <div class="w-auto lg:hidden">
                  <button class="relative z-10 inline-block" x-on:click="mobileNavOpen = !mobileNavOpen">
                    <svg class="text-green-500" width="51" height="51" viewbox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="56" height="56" rx="28" fill="currentColor"></rect>
                      <path d="M37 32H19M37 24H19" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="hidden fixed top-0 left-0 bottom-0 w-4/6 sm:max-w-xs z-50" :class="{'block': mobileNavOpen, 'hidden': !mobileNavOpen}">
          <div class="fixed inset-0 bg-black opacity-60" x-on:click="mobileNavOpen = !mobileNavOpen"></div>
          <nav class="relative z-10 px-9 pt-8 h-full bg-black overflow-y-auto">
            <div class="flex flex-wrap justify-between h-full">
              <div class="w-full">
                <div class="flex items-center justify-between -m-2">
                  <div class="w-auto p-2"><a class="inline-block" href="#"><img src="images/logo.svg" alt=""/></a></div>
                  <div class="w-auto p-2">
                    <button class="inline-block text-white" x-on:click="mobileNavOpen = !mobileNavOpen">
                      <svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 18L18 6M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex flex-col justify-center py-16 w-full">
                <ul>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="about.html">About</a></li>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="pricing.html">Pricing</a></li>
                  <li class="mb-8 text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="blog.html">Blog</a></li>
                  <li class="text-white font-medium hover:text-opacity-90 tracking-tighter"><a href="contact.html">Contact</a></li>
                </ul>
              </div>
              <div class="flex flex-col justify-end w-full pb-8"><a class="inline-block px-8 py-4 text-center text-white hover:text-black tracking-tighter hover:bg-green-400 border-2 border-white focus:border-green-400 focus:border-opacity-40 hover:border-green-400 focus:ring-4 focus:ring-green-400 focus:ring-opacity-40 rounded-full transition duration-300" href="login.html">Login</a></div>
            </div>
          </nav>
        </div>
      </section>
      <section class="overflow-hidden">
        <div class="flex flex-wrap -m-8">
          <div class="w-full md:w-1/2 p-8">
            <div class="px-4 pt-10 md:pb-40 max-w-lg mx-auto"><a class="inline-block mb-36" href="#">                </a>              
              <div class="text-center mx-auto">
                <h3 class="mb-4 text-5xl text-white tracking-5xl">Let`s Get Started</h3>
                <p class="mb-10 text-gray-300">Now create your account!</p>
                <div class="flex flex-wrap -1 mb-7">
                  <div class="w-full p-1"><a class="p-5 flex flex-wrap justify-center bg-gray-900 hover:bg-gray-900 bg-opacity-30 hover:bg-opacity-10 rounded-full transition duration-300" href="#">
                      <div class="mr-4 inline-block"><img src="template-assets/images/sign-in/google.svg" alt=""/></div><span class="text-sm text-white font-medium">Sign up with Google</span></a></div>
                  <div class="w-full p-1"><a class="p-5 flex flex-wrap justify-center bg-gray-900 hover:bg-gray-900 bg-opacity-30 hover:bg-opacity-10 rounded-full transition duration-300" href="#">
                      <div class="mr-4 inline-block"><img src="template-assets/images/sign-in/apple-logo.svg" alt=""/></div><span class="text-sm text-white font-medium">Sign up with Apple</span></a></div>
                </div>
                <div class="flex flex-wrap items-center mb-10">
                  <div class="flex-1 bg-gray-900">
                    <div class="h-px"></div>
                  </div>
                  <div class="px-5 text-xs text-gray-300 font-medium">or sign up with email</div>
                  <div class="flex-1 bg-gray-900">
                    <div class="h-px"></div>
                  </div>
                </div>
                <div class="mb-6 border border-gray-900 focus-within:border-white overflow-hidden rounded-3xl">
                  <input class="pl-6 pr-16 py-4 text-gray-300 w-full placeholder-gray-300 outline-none bg-transparent" type="text" placeholder="Enter your email"/>
                </div><a class="block mb-6 px-14 py-4 text-center font-medium tracking-2xl border-2 border-green-400 bg-green-400 hover:bg-green-500 text-black focus:ring-4 focus:ring-green-500 focus:ring-opacity-40 rounded-full transition duration-300" href="#">Sign up</a>
                <p class="text-gray-300"><span>Alreade have account?</span>                  <a class="underline" href="login.html">Sign in</a></p>
              </div>
            </div>
          </div>
          <div class="w-full md:w-1/2 p-8">
            <div class="flex flex-wrap items-center justify-center h-full bg-gradient-radial-dark py-16 px-4">
              <div class="w-full mb-8"><img class="mx-auto" src="template-assets/images/sign-in/stats.png" alt=""/></div>
              <div class="w-full">
                <p class="mb-8 text-sm text-center text-gray-300">Trusted by</p>
                <div class="flex flex-wrap items-center justify-center -m-4">
                  <div class="w-auto p-4"><img src="template-assets/images/sign-in/netflix.svg" alt=""/></div>
                  <div class="w-auto p-4"><img src="template-assets/images/sign-in/allianz.svg" alt=""/></div>
                  <div class="w-auto p-4"><img src="template-assets/images/sign-in/spotify.svg" alt=""/></div>
                  <div class="w-auto p-4"><img src="template-assets/images/sign-in/uber.svg" alt=""/></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="py-12">
        <div class="container px-4 mx-auto">
          <div class="relative pt-20 px-4 bg-gray-900 bg-opacity-20 overflow-hidden rounded-6xl">
            <div class="text-center md:max-w-xl mx-auto removed pb-20"><span class="inline-block mb-4 text-sm text-green-400 font-medium tracking-tighter">Learn to code</span>
              <h2 class="font-heading mb-6 text-7xl text-white tracking-8xl">Want to build templates like this one?</h2><a class="mb-8 text-gray-300 relative z-10" href="https://www.pixelrocket.store">Visit www.pixelrocket.store and learn to become a frontend web developer today</a><img class="absolute -bottom-24 right-0 z-0" src="template-assets/images/application-section/lines2.png" alt=""/>
            </div>
          </div>
        </div>
      </section>
      <section class="bg-gray-50 overflow-hidden">
        <div class="py-14 bg-black rounded-b-7xl"></div>
        <div class="py-24">
          <div class="container px-4 mx-auto">
            <div class="flex flex-wrap justify-center -m-8 mb-28">
              <div class="w-full md:w-1/2 lg:w-4/12 p-8">
                <div class="md:max-w-xs"><img class="mb-7" src="images/logo-dark.svg" alt=""/>
                  <p class="text-gray-400 font-medium">Global Bank is a strategic branding agency focused on brand creation, rebrands, and brand</p>
                </div>
              </div>
              <div class="w-full md:w-1/2 lg:w-2/12 p-8">
                <h3 class="mb-6 text-lg text-black font-medium">About</h3>
                <ul>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Contact</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Blog</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Our Story</a></li>
                  <li><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Careers</a></li>
                </ul>
              </div>
              <div class="w-full md:w-1/2 lg:w-2/12 p-8">
                <h3 class="mb-6 text-lg text-black font-medium">Company</h3>
                <ul>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Contact</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Blog</a></li>
                  <li class="mb-2.5"><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Our Story</a></li>
                  <li><a class="inline-block text-lg font-medium text-gray-400 hover:text-black transition duration-300" href="#">Careers</a></li>
                </ul>
              </div>
              <div class="w-full md:w-1/2 lg:flex-1 p-8">
                <div class="flex flex-wrap -m-2">
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/twitter.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on Twitter for updates</p>
                        </div>
                      </div></a></div>
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/instagram.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on Instagram for updates</p>
                        </div>
                      </div></a></div>
                  <div class="w-full p-2"><a class="block py-5 px-8 bg-white rounded-full" href="#">
                      <div class="flex flex-wrap items-center -m-2">
                        <div class="w-auto p-2"><img src="template-assets/images/footers/tiktok.svg" alt=""/></div>
                        <div class="flex-1 p-2">
                          <p class="text-black">Follow us on TikTok for updates</p>
                        </div>
                      </div></a></div>
                </div>
              </div>
            </div>
            <div class="flex flex-wrap justify-between -m-2">
              <div class="w-auto p-2">
                <p class="inline-block text-sm font-medium text-black text-opacity-60">© 2023 Global Bank</p>
              </div>
              <div class="w-auto p-2">
                <div class="flex flex-wrap items-center -m-2 sm:-m-7">
                  <div class="w-auto p-2 sm:p-7"><a class="inline-block text-sm text-black text-opacity-60 hover:text-opacity-100 font-medium transition duration-300" href="#">Terms of Use</a></div>
                  <div class="w-auto p-2 sm:p-7"><a class="inline-block text-sm text-black text-opacity-60 hover:text-opacity-100 font-medium transition duration-300" href="#">Privacy Policy</a></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </body>
</html>