/* ===== AUTHENTIC HTML GLOBALBANK TEMPLATE CSS ===== */
/* Exact replica of the original HTML GlobalBank template design system */

/* Reset and Base Styles */
*, ::before, ::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
}

html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
    margin: 0;
    line-height: inherit;
}

/* HTML GlobalBank Color System */
:root {
    --color-green-400: #CCFF00;
    --color-green-500: #B2E600;
    --color-gray-300: #D1D5DB;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
    --color-blueGray-950: #0F172A;
    --color-body: #0E0F11;
}

/* Body Styling */
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.bg-body {
    background-color: var(--color-body);
}

.text-body {
    color: #ffffff;
}

.font-body {
    font-family: 'Clash Grotesk', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Typography */
.font-heading {
    font-family: 'Clash Grotesk', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

.text-7xl {
    font-size: 4.5rem;
    line-height: 1;
}

.text-8xl {
    font-size: 6rem;
    line-height: 1;
}

.text-10xl {
    font-size: 8rem;
    line-height: 1;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-white {
    color: #ffffff;
}

.text-black {
    color: #000000;
}

.text-green-400 {
    color: var(--color-green-400);
}

.text-gray-300 {
    color: var(--color-gray-300);
}

.tracking-tighter {
    letter-spacing: -0.05em;
}

.tracking-8xl {
    letter-spacing: -0.05em;
}

.font-medium {
    font-weight: 500;
}

/* Layout */
.relative {
    position: relative;
}

.overflow-hidden {
    overflow: hidden;
}

.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
    }
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.text-center {
    text-align: center;
}

.pt-20 {
    padding-top: 5rem;
}

.lg\:pt-28 {
    padding-top: 7rem;
}

@media (min-width: 1024px) {
    .lg\:pt-28 {
        padding-top: 7rem;
    }
}

.mb-24 {
    margin-bottom: 6rem;
}

.mb-10 {
    margin-bottom: 2.5rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-2\.5 {
    margin-bottom: 0.625rem;
}

.max-w-4xl {
    max-width: 56rem;
}

.max-w-md {
    max-width: 28rem;
}

.z-10 {
    z-index: 10;
}

/* Buttons */
.inline-block {
    display: inline-block;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.bg-green-400 {
    background-color: var(--color-green-400);
}

.hover\:bg-green-500:hover {
    background-color: var(--color-green-500);
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-5xl {
    border-radius: 1.875rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.focus\:ring-4:focus {
    box-shadow: 0 0 0 4px rgba(204, 255, 0, 0.4);
}

.focus\:ring-green-500:focus {
    box-shadow: 0 0 0 4px rgba(178, 230, 0, 0.4);
}

.focus\:ring-opacity-40:focus {
    --tw-ring-opacity: 0.4;
}

/* Forms */
.w-full {
    width: 100%;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.bg-transparent {
    background-color: transparent;
}

.border-2 {
    border-width: 2px;
}

.border-gray-800 {
    border-color: var(--color-gray-800);
}

.focus\:border-green-400:focus {
    border-color: var(--color-green-400);
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* Platform Links */
.bg-blueGray-950 {
    background-color: var(--color-blueGray-950);
}

.pb-24 {
    padding-bottom: 6rem;
}

.mb-20 {
    margin-bottom: 5rem;
}

.flex {
    display: flex;
}

.flex-wrap {
    flex-wrap: wrap;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

.-m-4 {
    margin: -1rem;
}

.p-4 {
    padding: 1rem;
}

.w-auto {
    width: auto;
}

.w-24 {
    width: 6rem;
}

.h-24 {
    height: 6rem;
}

.w-8 {
    width: 2rem;
}

.h-8 {
    height: 2rem;
}

.bg-gradient-radial-dark {
    background-image: radial-gradient(72.20% 78.49% at 49.87% 50.10%, rgba(71, 80, 98, 0.26) 0%, rgba(137, 137, 137, 0.00) 100%);
}

.border {
    border-width: 1px;
}

.border-gray-900 {
    border-color: var(--color-gray-900);
}

.border-opacity-30 {
    --tw-border-opacity: 0.3;
}

.hover\:border-green-400:hover {
    border-color: var(--color-green-400);
}

/* Utility Classes */
.hidden {
    display: none;
}

.bg-red-500 {
    background-color: #ef4444;
}

/* Responsive Design */
@media (min-width: 768px) {
    .md\:max-w-4xl {
        max-width: 56rem;
    }
    
    .md\:max-w-md {
        max-width: 28rem;
    }
}

@media (min-width: 1024px) {
    .lg\:text-8xl {
        font-size: 6rem;
        line-height: 1;
    }
}

@media (min-width: 1280px) {
    .xl\:text-10xl {
        font-size: 8rem;
        line-height: 1;
    }
}
