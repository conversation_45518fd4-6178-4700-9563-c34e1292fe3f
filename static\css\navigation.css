/**
 * 🧭 MODERN NAVIGATION SYSTEM
 *
 * Research-based implementation following:
 * - Material Design navigation patterns
 * - Apple Human Interface Guidelines
 * - Modern web app navigation best practices
 * - Accessibility standards (WCAG 2.1)
 */


/* ===== LAYOUT STRUCTURE ===== */

.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-primary);
}

.dashboard-sidebar {
    width: var(--nav-width);
    background: #2c3e50;
    border-right: none;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
}

.dashboard-main {
    flex: 1;
    margin-left: var(--nav-width);
    background-color: var(--bg-secondary);
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}


/* ===== SIDEBAR HEADER ===== */

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: transparent;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.125rem;
    transition: opacity 0.2s ease;
}

.sidebar-logo:hover {
    opacity: 0.8;
}

.sidebar-logo-icon {
    width: 32px;
    height: 32px;
    background: #3498db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 14px;
}


/* ===== CREATE DROP BUTTON ===== */

.create-drop-section {
    padding: var(--space-lg);
}

.create-drop-btn {
    width: 100%;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.create-drop-btn:hover {
    background: #2980b9;
}

.create-drop-btn:active {
    transform: scale(0.98);
}

.create-drop-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    font-weight: 900;
}


/* ===== NAVIGATION MENU ===== */

.nav-menu {
    flex: 1;
    padding: var(--space-md) 0;
    overflow-y: auto;
}

.nav-menu::-webkit-scrollbar {
    width: 4px;
}

.nav-menu::-webkit-scrollbar-track {
    background: transparent;
}

.nav-menu::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 2px;
}

.nav-item {
    margin: 0 var(--space-md) var(--space-xs) var(--space-md);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    margin: 2px 16px;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background-color: #3498db;
    color: white;
    font-weight: 600;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nav-text {
    flex: 1;
}


/* ===== SIDEBAR FOOTER ===== */

.sidebar-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--nav-border);
    background-color: var(--bg-tertiary);
}

.theme-toggle {
    width: 100%;
    background: transparent;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md) var(--space-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    font-size: 0.9rem;
}

.theme-toggle:hover {
    background-color: var(--nav-item-hover);
    border-color: var(--border-secondary);
    color: var(--text-primary);
}


/* ===== ULTRA-MODERN MOBILE NAVIGATION ===== */

@media (max-width: 768px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
        width: 100%;
        background: transparent;
        border: none;
        box-shadow: none;
    }
    .dashboard-main {
        margin-left: 0;
        padding-bottom: 100px;
        /* Space for floating nav */
    }
    /* Sleek Bottom Navigation Container */
    .mobile-nav {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        z-index: 1000;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        height: 70px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    [data-theme="dark"] .mobile-nav {
        background: rgba(15, 23, 42, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 2px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }
    .mobile-nav.expanded {
        height: 380px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(40px);
        -webkit-backdrop-filter: blur(40px);
        border-radius: 32px;
        bottom: 20px;
    }
    [data-theme="dark"] .mobile-nav.expanded {
        background: rgba(15, 23, 42, 0.95);
    }
    /* Floating Action Button Style Toggle */
    .mobile-nav-toggle {
        position: absolute;
        top: 15px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4), 0 2px 8px rgba(102, 126, 234, 0.2);
        z-index: 10;
    }
    .mobile-nav-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5), 0 3px 12px rgba(102, 126, 234, 0.3);
    }
    .mobile-nav-toggle:active {
        transform: scale(0.95);
    }
    .mobile-nav-toggle-icon {
        width: 20px;
        height: 20px;
        color: white;
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        font-size: 16px;
    }
    .mobile-nav.expanded .mobile-nav-toggle-icon {
        transform: rotate(45deg);
    }
    /* Collapsed State - Minimal Tab Bar */
    .mobile-nav-collapsed {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
        height: 70px;
        opacity: 1;
        transition: opacity 0.3s ease;
    }
    .mobile-nav.expanded .mobile-nav-collapsed {
        opacity: 0;
        pointer-events: none;
    }
    .mobile-nav-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        padding: 8px 12px;
        border-radius: 16px;
        text-decoration: none;
        color: var(--text-secondary);
        transition: all 0.2s ease;
        position: relative;
        min-width: 50px;
    }
    .mobile-nav-tab.active {
        color: var(--brand-primary);
        background: rgba(102, 126, 234, 0.1);
    }
    .mobile-nav-tab.active::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 3px;
        background: var(--brand-primary);
        border-radius: 2px;
    }
    .mobile-nav-tab-icon {
        width: 20px;
        height: 20px;
        transition: transform 0.2s ease;
        stroke-width: 2.5;
    }
    .mobile-nav-tab:active .mobile-nav-tab-icon {
        transform: scale(0.9);
    }
    .mobile-nav-tab-label {
        font-size: 9px;
        font-weight: 700;
        letter-spacing: 0.3px;
        text-transform: uppercase;
        margin-top: 2px;
    }
    /* Expanded State - Full Menu */
    .mobile-nav-content {
        padding: 80px 32px 32px;
        height: 100%;
        display: flex;
        flex-direction: column;
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        pointer-events: none;
    }
    .mobile-nav.expanded .mobile-nav-content {
        opacity: 1;
        transform: translateY(0);
        pointer-events: all;
    }
    /* Navigation Grid */
    .mobile-nav-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;
    }
    .mobile-nav-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px;
        border-radius: 20px;
        text-decoration: none;
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
    }
    [data-theme="dark"] .mobile-nav-item {
        background: rgba(51, 65, 85, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .mobile-nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }
    .mobile-nav-item:hover::before {
        left: 100%;
    }
    .mobile-nav-item:active {
        transform: scale(0.98);
    }
    .mobile-nav-item.active {
        background: linear-gradient(135deg, var(--brand-primary), var(--brand-accent));
        color: white;
        border: 1px solid var(--brand-primary);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }
    .mobile-nav-icon {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        stroke-width: 2;
    }
    .mobile-nav-text {
        font-size: 15px;
        font-weight: 600;
        flex: 1;
    }
    /* Create Drop Button */
    .mobile-create-drop {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 18px 24px;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 700;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4), 0 4px 12px rgba(102, 126, 234, 0.2);
        position: relative;
        overflow: hidden;
        min-height: 56px;
    }
    .create-drop-icon {
        width: 20px;
        height: 20px;
        stroke-width: 3;
    }
    .mobile-create-drop::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }
    .mobile-create-drop:hover::before {
        left: 100%;
    }
    .mobile-create-drop:active {
        transform: scale(0.98);
    }
    /* Theme Toggle in Mobile */
    .mobile-theme-toggle {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 36px;
        height: 36px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0;
        pointer-events: none;
    }
    .mobile-nav.expanded .mobile-theme-toggle {
        opacity: 1;
        pointer-events: all;
    }
    [data-theme="dark"] .mobile-theme-toggle {
        background: rgba(51, 65, 85, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .mobile-theme-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }
    .mobile-theme-icon {
        width: 18px;
        height: 18px;
        color: var(--text-primary);
        stroke-width: 2;
    }
}


/* ===== LAYLO-STYLE MOBILE NAVIGATION ===== */


/* Mobile Navigation Overlay */

.laylo-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.laylo-mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}


/* Mobile Navigation Menu */

.laylo-mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px 20px 0 0;
    z-index: 999;
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.1);
}

.laylo-mobile-nav.active {
    transform: translateY(0);
}


/* Mobile Nav Header */

.laylo-mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.laylo-mobile-close,
.laylo-mobile-share {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s ease;
}

.laylo-mobile-close:hover,
.laylo-mobile-share:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.laylo-mobile-close svg,
.laylo-mobile-share svg {
    width: 18px;
    height: 18px;
}


/* Mobile Navigation Items */

.laylo-mobile-nav-items {
    padding: 8px 0;
}

.laylo-mobile-nav-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    text-decoration: none;
    color: #666;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.laylo-mobile-nav-item:hover {
    background: rgba(0, 0, 0, 0.03);
    color: #333;
}

.laylo-mobile-nav-item.active {
    background: rgba(59, 130, 246, 0.08);
    color: #3b82f6;
}

.laylo-mobile-nav-icon {
    width: 24px;
    height: 24px;
    margin-right: 16px;
    flex-shrink: 0;
}

.laylo-mobile-nav-text {
    font-size: 16px;
    font-weight: 500;
}


/* Mobile User Section */

.laylo-mobile-user-section {
    padding: 20px 24px 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    background: rgba(0, 0, 0, 0.02);
}

.laylo-mobile-user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.laylo-mobile-user-avatar {
    width: 48px;
    height: 48px;
    margin-right: 16px;
    flex-shrink: 0;
}

.laylo-mobile-user-logo {
    width: 48px;
    height: 48px;
    background: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.laylo-mobile-logo-text {
    color: white;
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.laylo-mobile-user-details {
    flex: 1;
}

.laylo-mobile-user-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.laylo-mobile-user-phone {
    font-size: 14px;
    color: #666;
}


/* Mobile Drop Button */

.laylo-mobile-drop-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 16px;
    background: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.laylo-mobile-drop-btn:hover {
    background: #2563eb;
    color: white;
    transform: translateY(-1px);
}

.laylo-mobile-drop-icon {
    margin-right: 8px;
    font-size: 18px;
    font-weight: 400;
}


/* Mobile Navigation Trigger */

.laylo-mobile-trigger {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.laylo-mobile-trigger:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.laylo-mobile-trigger svg {
    width: 20px;
    height: 20px;
    color: #333;
}


/* Show Laylo mobile navigation on mobile devices */

@media (max-width: 768px) {
    .laylo-mobile-trigger {
        display: flex !important;
        position: fixed !important;
        top: 16px !important;
        left: 16px !important;
        width: 44px !important;
        height: 44px !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;
        z-index: 1000 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        cursor: pointer !important;
    }
    .laylo-mobile-trigger:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: scale(1.05) !important;
    }
    .laylo-mobile-trigger svg {
        width: 20px !important;
        height: 20px !important;
        color: #374151 !important;
        stroke-width: 2.5 !important;
    }
    .laylo-sidebar {
        display: none !important;
    }
    .laylo-main {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 0 !important;
    }
    /* Enhanced mobile navigation menu */
    .laylo-mobile-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 20px 20px 0 0 !important;
        z-index: 999 !important;
        transform: translateY(100%) !important;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        max-height: 85vh !important;
        overflow-y: auto !important;
        box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
    .laylo-mobile-nav.active {
        transform: translateY(0) !important;
    }
    /* Mobile navigation overlay */
    .laylo-mobile-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.5) !important;
        backdrop-filter: blur(4px) !important;
        z-index: 998 !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    .laylo-mobile-overlay.active {
        opacity: 1 !important;
        visibility: visible !important;
    }
    /* Mobile navigation header */
    .laylo-mobile-nav-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 20px 20px 16px 20px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
    .laylo-mobile-close,
    .laylo-mobile-share {
        width: 36px !important;
        height: 36px !important;
        border-radius: 8px !important;
        border: none !important;
        background: rgba(0, 0, 0, 0.05) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }
    .laylo-mobile-close:hover,
    .laylo-mobile-share:hover {
        background: rgba(0, 0, 0, 0.1) !important;
        transform: scale(1.05) !important;
    }
    .laylo-mobile-close svg,
    .laylo-mobile-share svg {
        width: 18px !important;
        height: 18px !important;
        color: #374151 !important;
        stroke-width: 2 !important;
    }
    /* Mobile navigation items */
    .laylo-mobile-nav-items {
        padding: 16px 20px !important;
    }
    .laylo-mobile-nav-item {
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
        padding: 16px 12px !important;
        border-radius: 12px !important;
        text-decoration: none !important;
        color: #374151 !important;
        font-weight: 500 !important;
        font-size: 0.95rem !important;
        transition: all 0.2s ease !important;
        margin-bottom: 4px !important;
    }
    .laylo-mobile-nav-item:hover,
    .laylo-mobile-nav-item.active {
        background: rgba(59, 130, 246, 0.1) !important;
        color: #3b82f6 !important;
        text-decoration: none !important;
    }
    .laylo-mobile-nav-icon {
        width: 22px !important;
        height: 22px !important;
        stroke-width: 2 !important;
        flex-shrink: 0 !important;
    }
    .laylo-mobile-nav-text {
        font-weight: 500 !important;
    }
    /* Mobile user section */
    .laylo-mobile-user-section {
        padding: 20px !important;
        border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
        background: rgba(0, 0, 0, 0.02) !important;
    }
    .laylo-mobile-user-info {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        margin-bottom: 16px !important;
    }
    .laylo-mobile-user-avatar {
        width: 48px !important;
        height: 48px !important;
        border-radius: 12px !important;
        background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-shrink: 0 !important;
    }
    .laylo-mobile-logo-text {
        color: white !important;
        font-weight: 700 !important;
        font-size: 0.875rem !important;
    }
    .laylo-mobile-user-image {
        width: 48px !important;
        height: 48px !important;
        border-radius: 12px !important;
        object-fit: cover !important;
    }
    .laylo-mobile-user-name {
        font-weight: 600 !important;
        color: #1a202c !important;
        font-size: 0.95rem !important;
        margin-bottom: 2px !important;
    }
    .laylo-mobile-user-phone {
        color: #64748b !important;
        font-size: 0.8rem !important;
    }
    .laylo-mobile-drop-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px !important;
        width: 100% !important;
        padding: 14px !important;
        background: #3b82f6 !important;
        color: white !important;
        border-radius: 12px !important;
        text-decoration: none !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
        transition: all 0.2s ease !important;
    }
    .laylo-mobile-drop-btn:hover,
    .laylo-mobile-drop-btn:active {
        background: #2563eb !important;
        color: white !important;
        text-decoration: none !important;
        transform: translateY(-1px) !important;
    }
    .laylo-mobile-drop-icon {
        font-size: 1.1rem !important;
        font-weight: 700 !important;
    }
}


/* Dark mode support for Laylo mobile navigation */

@media (prefers-color-scheme: dark) {
    .laylo-mobile-nav {
        background: rgba(30, 30, 30, 0.95);
    }
    .laylo-mobile-nav-item {
        color: #ccc;
    }
    .laylo-mobile-nav-item:hover {
        background: rgba(255, 255, 255, 0.05);
        color: #fff;
    }
    .laylo-mobile-nav-item.active {
        background: rgba(59, 130, 246, 0.15);
        color: #60a5fa;
    }
    .laylo-mobile-close,
    .laylo-mobile-share {
        background: rgba(255, 255, 255, 0.1);
        color: #ccc;
    }
    .laylo-mobile-close:hover,
    .laylo-mobile-share:hover {
        background: rgba(255, 255, 255, 0.15);
        color: #fff;
    }
    .laylo-mobile-user-section {
        background: rgba(255, 255, 255, 0.03);
        border-top-color: rgba(255, 255, 255, 0.1);
    }
    .laylo-mobile-user-name {
        color: #fff;
    }
    .laylo-mobile-user-phone {
        color: #999;
    }
    .laylo-mobile-trigger {
        background: rgba(30, 30, 30, 0.9);
    }
    .laylo-mobile-trigger:hover {
        background: rgba(30, 30, 30, 1);
    }
    .laylo-mobile-trigger svg {
        color: #fff;
    }
}